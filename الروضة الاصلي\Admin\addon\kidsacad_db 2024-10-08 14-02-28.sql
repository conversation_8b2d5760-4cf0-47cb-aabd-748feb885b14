
-- Database Backup --
-- Ver. : 1.0.1
-- Host : 127.0.0.1
-- Generating Time : Oct 08, 2024 at 14:02:28:PM



CREATE TABLE `depit_tb` (
  `id` int(100) NOT NULL AUTO_INCREMENT,
  `userID` int(100) NOT NULL,
  `depit_note` varchar(250) NOT NULL,
  `depit_date` date NOT NULL,
  `depit_date2` date DEFAULT NULL,
  `depit_cash` float NOT NULL,
  PRIMARY KEY (`id`),
  KEY `userID` (`userID`),
  CONSTRAINT `depit_tb_ibfk_1` FOREIGN KEY (`userID`) REFERENCES `users_tb` (`id_user`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=372 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


INSERT INTO depit_tb VALUES
("268","7","تصليح كسر جامه اداره +بطيخ +توصيل ","2024-06-05","0000-00-00","19500"),
("269","7","لاصق +فيش كاميرات +خواشيك +صينيه +مقراضه +مشط+سله مهملات+كاسات +بلم تقديم تفاح 2كيلو ","2024-06-08","0000-00-00","70000"),
("270","7","وايبس +ديتول +قاصر كبير +باله كلينس +برتقال ","2024-06-07","0000-00-00","16000"),
("271","7","دبه ماء ","2024-06-10","0000-00-00","1000"),
("272","7","تأسيس المغسله مع المغسله +اجره العمل +منضف دهون +معطر ارضيه ","2024-06-11","0000-00-00","79500"),
("273","7","45الف جراجف اسره الحضانه مع الخياط وومخاد +وجوه مخاد +تلبيسه تخم ","2024-12-06","0000-00-00","55000"),
("274","7","ركي +2دبه مي ","2024-06-13","0000-00-00","10000"),
("275","7","رز جوكر 4.5كيلو +فنكر +خيار +توصيل ","2024-06-23","0000-00-00","15750"),
("276","7","تفاح +برتقال +بتيته ","2024-06-24","0000-00-00","5500"),
("277","7","باميا +طماطه +توصيل +زيت +معجون +صندوق ماء ","2024-06-26","0000-00-00","13000"),
("278","7","مسواك اسبوعي من المؤسسه 20 الف","2024-06-30","0000-00-00","20"),
("284","7","\r\n مسواك اسبوعي من الموسسه \r\nعنب 4.500\r\nبطاطا 3.000\r\nخيار 3.000\r\nطماطه 1.250\r\nتفاح اصفر 3.000\r\nتفاح احمر 2000\r\nموز 3.000","2024-07-08","0000-00-00","19750"),
("285","7"," تمن 6.500\r\nمع التىوصيل","2024-07-09","0000-00-00","6500"),
("289","7"," دبات ماء العدد2","2024-07-10","0000-00-00","2000"),
("290","7","لزكات فار العدد 6","2024-07-11","0000-00-00","5000"),
("294","7","زيت+بهارات ماجي 5000\r\nباميا+ثوم+ركيه7.500","2024-07-16","0000-00-00","12500"),
("295","7"," دبه ماء 1000روبه1.7502وصل مسح 2.500","2024-07-18","0000-00-00","5750"),
("296","7","مسواك من المؤسسه","2024-07-21","0000-00-00","20000"),
("297","7","سله نفايات مغلقه العدد3","2024-07-21","0000-00-00","28"),
("298","7","تصليح سبلت المطبخ","2024-07-22","0000-00-00","25000"),
("301","7","شراء دبه ماء 1 +تعبئه عدد2دبه+كارتون ماء","2024-07-22","0000-00-00","8500"),
("304","7","\r\nتول ضلي","2024-07-24","0000-00-00","66"),
("305","7","+تصليح سبلت الاداره+شد تول المرر +خلف الحضانه","2024-07-30","0000-00-00","40"),
("306","7","مخضر حضانه","2024-07-30","0000-00-00","8250"),
("307","7"," نقل الثيل القديم ","2024-08-04","0000-00-00","10000"),
("308","7","مسواك من المؤسسه","2024-08-04","0000-00-00","22000"),
("314","7","معقمات عدد خمسه 10الاف ديتول اصلي 6الاف مزيل  دهون2ونص معطر ارضيه2 ونص معطر جو  3الاف خواشيك 2الافسبوره 7الاف سيتين مشاطه الفين ثلالث وردات 3الافجراجف الفين","2024-08-07","0000-00-00","40000"),
("315","7","غداء المسات","2024-08-08","0000-00-00","39000"),
("316","7","سحب المديره 25 الف للمشاركه في ورق جدار الصف التحضير ي","2024-08-07","0000-00-00","25000"),
("317","7","منضف ارضيات 2250\\\\ سائل جلي منضف 1750\\كلنس 1500\\مناديل مبلله1000\\اكياس شفاف 250\\معطر جو7000\\معطر اقمشه 1750\\اكياس نفايات2000","2024-08-12","0000-00-00","17500"),
("319","6","كلينس عدد 2  معطر ارضيات عدد     1   سائل جلي صحون عدد   1    نشاط   ورق  بند   مناديل مبللة  عدد 2","2024-08-14","0000-00-00","16"),
("320","6","معطر  ارضيات  عدد 4   سائل جلي صحون عدد  1         ماء  عدد 4 ","2024-08-18","0000-00-00","28"),
("321","6","بطاطا    خيار   طماطه  بطيخ      تفاح احمر   تفاح اصفر   برتقال ","2024-08-18","0000-00-00","19"),
("322","7","مسؤاك من المؤسسه","2024-08-18","0000-00-00","19250"),
("323","7","تصليح سبلت صف التحضيري ","2024-08-19","0000-00-00","25000"),
("324","7","كارتونه ماء اكواب","2024-08-19","0000-00-00","2500"),
("325","7","منضفات+معطرات","2024-08-19","0000-00-00","13000"),
("326","6","بطاطا    خيار      تفاح    برتقال","2024-08-26","0000-00-00","10"),
("327","6","معجون طعاطم      سكر   ابيض    شاي     علاكة كبيرة","2024-08-27","0000-00-00","7"),
("328","7","دبات ماء مع كارتونه كاسات ماء","2024-08-26","0000-00-00","7000"),
("329","7","دبات ماء مع كارتونه كاسات ماء","2024-08-26","0000-00-00","7000"),
("330","7"," علاج الحشائش الضاره ","2024-08-27","0000-00-00","10000"),
("332","6","ماء   عدد     3","2024-09-03","0000-00-00","6"),
("334","6","معكرون   عدد      2  زيت الدار    عدد   1    كلينلس    عدد  1   كلينكس مبلله   عدد  1","2024-09-04","0000-00-00","8"),
("335","6","معطر     ارضيات      عدد  9","2024-09-04","0000-00-00","22"),
("336","6","تفاح     برتقال   جزر    خيار   بطاطا","2024-09-08","0000-00-00","7"),
("338","7","3كارتونات ماء","2024-09-03","0000-00-00","6000"),
("339","7"," منضفات+معطرات","2024-09-04","0000-00-00","22000"),
("340","7","3دبات ماء","2024-09-04","0000-00-00","2000"),
("341","7"," مسؤاك من المؤسسه","2024-09-08","0000-00-00","7000"),
("342","7"," اجور نجار لتصليح الاسره واللوكرات","2024-09-08","0000-00-00","25000"),
("343","7","مبلغ صبغ للحضانه","2024-09-09","0000-00-00","75000"),
("344","7","تصليح نقطه كهرباء للحضانه+درنفيس فحص","2024-09-09","0000-00-00","6000"),
("345","7","غداء للصباغ","2024-09-09","0000-00-00","8500"),
("346","6","برتغال   بطاطا   جزر  تفاح احمر  تفاح اصفر  ","2024-09-16","0000-00-00","10"),
("347","7"," مسؤاك من المؤسسه","2024-09-16","0000-00-00","10250"),
("349","6","بطاطا   خيار  جزر   برتقال  تفاح   ","2024-09-22","0000-00-00","13"),
("351","6","ماء عدد","2024-09-22","0000-00-00","6"),
("352","7","ثلاث ماء ","2024-09-22","0000-00-00","3000"),
("353","7","مسواك \r\nبسعر ثلاث عشر ونصف ","2024-09-22","0000-00-00","13"),
("360","7","4صوندات مي","2024-09-30","0000-00-00","15"),
("361","7","مسواك من المول \r\nلاسق كبير \r\nلاسق وجهين \r\nاعواد اذان \r\nكفوف استعمال واحد ","2024-10-02","0000-00-00","5"),
("363","6","معطرات ارضيات    عدد   10","2024-10-02","0000-00-00","25"),
("364","6","خيار   بطاطا برتقال  تفاح","2024-10-06","0000-00-00","10"),
("365","7","\r\nقاصر 2\r\nمعطر ارضيه 2\r\nمعطر جو 3\r\nملمع ارضيه 1\r\nفلاش1\r\nديتول1\r\nزاهي كبير ","2024-10-20","","25000"),
("366","7","تصليح سبلت ","2024-10-02","","25"),
("367","7","ريموند سبلت ","2024-10-02","","10000"),
("368","7","مسواك ","2024-10-06","","10000"),
("369","7","\r\nدبات ماء 3\r\nصندوقان ماء","2024-10-06","","7000"),
("370","7","4 صناديق ماء ","2024-10-08","","8000"),
("371","7","علبة لبن حجم كبير ","2024-10-08","","3000");




CREATE TABLE `employ_tb` (
  `id_employ` int(11) NOT NULL AUTO_INCREMENT,
  `f_name` varchar(250) NOT NULL,
  `b_date` date NOT NULL,
  `job` varchar(250) NOT NULL,
  `date_start` date NOT NULL,
  `location` varchar(250) NOT NULL,
  `salary` float NOT NULL,
  `userID` int(100) NOT NULL,
  PRIMARY KEY (`id_employ`),
  KEY `userID` (`userID`),
  CONSTRAINT `employ_tb_ibfk_1` FOREIGN KEY (`userID`) REFERENCES `users_tb` (`id_user`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=215 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


INSERT INTO employ_tb VALUES
("47","منار قاسم","2002-04-23","عامله","2023-03-23","A7","300000","12"),
("50","ثريا محمد","1994-06-23","مربيه","2023-03-23","A5","300000","12"),
("59","جنان سلام حميد ","1995-04-18","حاضنه","2023-04-12","A5","300","8"),
("74","هبه خالد ","1982-06-01","مس كي جي 1","2023-02-05","بسمايه 807","300","9"),
("77","شهد جاسم","1997-03-09","مربيه","2023-05-04","A8","300000","12"),
("78","مريم اسعد","1995-06-06","مربيه","2023-02-04","A9","300000","12"),
("95","هبه جواد كاظم","1992-11-18","مديره","2023-10-19","A7","400","11"),
("97","زمن رعد","1988-11-20","مس PR","2023-10-01","a4","300","11"),
("100","غفران باسم صباح","1994-09-07","pr1","2023-11-21","A7  ","300","10"),
("103","نبراس ابراهيم عليوي","1984-05-06","    pr1","2024-10-01","جسر ديالى","300","10"),
("105","شذى سالم عبدالحسين","1990-08-25","عاملة نظافه","2023-12-04","A2","300","6"),
("108","شروق عطوان مهدي","1981-08-18","مس تحضيري","2023-10-08","A701--112","300","8"),
("112","فاطمة فهمي حميد","1992-04-30","مس تحضيري","2023-12-12","A814_304","300","8"),
("114","مريم جعفر ","1989-03-20","مديره","2023-10-30","8","400","9"),
("115","عبير عبد الحسين","1991-10-29","مربيه","2023-12-04","5","300","9"),
("126","هبة خالد","1996-03-12","مربيه اطفال /Pr","2023-10-09","a2 207/702","300","7"),
("127","اسيل  حسين فندي ","1997-10-10","معلمه روضه /KG1","2024-01-31","a4/412/708","350","7"),
("128","سجى فائز فاخر","1998-01-27","معلمه روضه /KG2","2024-01-28","a3/301/704","350","7"),
("138","فرح عدنان هاشم","1990-09-19","مربيه اطفال /Pr","2024-04-23","B/109","300","7"),
("145","ايات سامي","1996-02-24","مربيه ","2024-05-12","9","300","9"),
("146","سهام عبد الكريم","1989-02-02","مديرة","2023-03-08","بسمايه a6","400","6"),
("147","لينا سالم","1994-07-30","مس ","2024-03-03","5","350","9"),
("150","نور سعد","2024-08-16","مس KG1","2024-01-14","a4","350","11"),
("154","هديل محسن","2002-06-04","مس تحضيري","2024-05-19","بسماية1","300","6"),
("160","ايناس ابراهيم عليوي ","1983-04-09","محاسبة","2024-06-05","بلوك A9","300","9"),
("161","نسرين سهيل نجم","1980-04-16","موضفة ","2024-07-21","بلوك A9","300","9"),
("162","زينب احمد ضايف ","1996-02-09","مربية","2024-08-01","b3","300","6"),
("163","ضحىى عيدان عبود","2002-09-05","مربية","2024-08-01","b3","300","6"),
("167","طيبه عدنان هاشم ","1999-09-26","محاسبه","2024-07-22","A9   ","300","10"),
("171","اقسام لازم الغيوي","1994-09-09"," مربيه اطفال","2024-08-03","B812110","300","7"),
("174","ريم احمد سلمان","1981-12-24"," موظفه خدمه ","2024-08-14"," a301807","300","7"),
("175","بان قاسم مهدي","1992-01-29","مديره","2024-08-18","A804906","400","7"),
("176","تبارك مشتاق","1998-02-11","محاسبة","2023-06-19","بلوك A5","350","12"),
("177","سارة عبد الوهاب","1994-02-02","مديره","2024-08-01","بلوك A3","400","12"),
("179","سبا فيصل ","1995-03-20","محاسبة ","2024-01-20","A1`","350","6"),
("180","كفاح حسن اسد","1985-08-15","مديرة الحضانه ","2024-08-20","A9","400","8"),
("184","اسيل عبد جاسم","1985-12-03","مساعدة","2024-08-21","ناحيه الوحده","300","10"),
("190","زهراء علي    اشكير ","2000-01-01","مس kg2","2024-09-07","A1","350","8"),
("194","بنين مهد","2006-10-07","مربيه","2024-09-08","A8","300","12"),
("195","رسل جاسم محمد","2001-04-11","محاسبة ","2024-09-22","بلوك 3","300","7"),
("202","فاطمة  فهمي حميد","1992-03-01","مس تحضيري","2024-09-18","A8","300","12"),
("203","مريم عويد فرحان","2000-04-16","مس     KG2","2024-09-26","B2","350","10"),
("204","رقية علي فاضل ","1998-12-23","مس     KG2","2024-09-29","A6  ","350","10"),
("208","سحر عادل مجيد","1984-12-06","المديره","2024-10-02","A2   ","400","10"),
("209","شروق عطوان مهدي","1981-08-20","pr2","2024-10-02","A7  ","300","10"),
("210","سجود قاسم جمعه","1998-06-23","pr1","2024-10-02","A6 ","300","10"),
("211","نور الايمان عامر خليل","1997-06-10","مس تمهيدي","2024-10-02","A2","350","12"),
("212","نور الايمان عامر خليل","1997-06-10","مس تمهيدي","2024-10-02","A2","350","12"),
("213","بشرى كاظم خلف","1978-08-02","pr2","2024-10-07","A5  ","300","10"),
("214","شهد جواد جبار","2003-09-02","kg1","2024-10-06","A1   ","350","10");




CREATE TABLE `stat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_stud` int(11) NOT NULL,
  `stat_stud` varchar(250) NOT NULL,
  `data_stat` date NOT NULL,
  PRIMARY KEY (`id`),
  KEY `id_stud` (`id_stud`)
) ENGINE=InnoDB AUTO_INCREMENT=359 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


INSERT INTO stat VALUES
("1","2662","حاضر","2024-10-07"),
("2","2661","حاضر","2024-10-07"),
("3","2700","حاضر","2024-10-07"),
("4","2283","حاضر","2024-10-07"),
("5","2308","حاضر","2024-10-07"),
("6","1645","حاضر","2024-10-07"),
("7","2594","حاضر","2024-10-07"),
("8","2768","حاضر","2024-10-07"),
("9","3123","حاضر","2024-10-07"),
("10","3071","حاضر","2024-10-07"),
("11","2873","حاضر","2024-10-07"),
("12","3035","حاضر","2024-10-07"),
("13","3007","حاضر","2024-10-07"),
("14","2329","حاضر","2024-10-07"),
("15","2950","حاضر","2024-10-07"),
("16","3012","حاضر","2024-10-07"),
("17","2741","حاضر","2024-10-07"),
("18","3042","حاضر","2024-10-07"),
("19","2665","حاضر","2024-10-07"),
("20","3140","حاضر","2024-10-07"),
("21","2765","حاضر","2024-10-07"),
("22","2638","حاضر","2024-10-07"),
("23","3081","حاضر","2024-10-07"),
("24","3057","حاضر","2024-10-07"),
("25","461","حاضر","2024-10-07"),
("26","2970","حاضر","2024-10-07"),
("27","2284","حاضر","2024-10-07"),
("28","2907","حاضر","2024-10-07"),
("29","1781","حاضر","2024-10-07"),
("30","3028","حاضر","2024-10-07"),
("31","3092","حاضر","2024-10-07"),
("32","446","حاضر","2024-10-07"),
("33","2735","حاضر","2024-10-07"),
("34","3148","حاضر","2024-10-07"),
("35","3160","حاضر","2024-10-07"),
("36","3033","حاضر","2024-10-07"),
("37","3142","حاضر","2024-10-07"),
("38","3185","حاضر","2024-10-07"),
("39","3186","حاضر","2024-10-07"),
("40","3064","حاضر","2024-10-07"),
("41","3073","حاضر","2024-10-07"),
("42","2872","حاضر","2024-10-07"),
("43","2898","حاضر","2024-10-07"),
("44","2842","حاضر","2024-10-07"),
("45","2836","حاضر","2024-10-07"),
("46","3066","حاضر","2024-10-07"),
("47","2870","حاضر","2024-10-07"),
("48","2328","حاضر","2024-10-07"),
("49","2984","حاضر","2024-10-07"),
("50","2794","حاضر","2024-10-07"),
("51","2948","حاضر","2024-10-07"),
("52","2849","حاضر","2024-10-07"),
("53","3166","حاضر","2024-10-07"),
("54","3019","حاضر","2024-10-07"),
("55","3097","حاضر","2024-10-07"),
("56","2895","حاضر","2024-10-07"),
("57","3054","حاضر","2024-10-07"),
("58","3051","حاضر","2024-10-07"),
("59","1485","حاضر","2024-10-07"),
("60","2947","حاضر","2024-10-07"),
("61","2269","حاضر","2024-10-07"),
("62","3052","حاضر","2024-10-07"),
("63","762","حاضر","2024-10-07"),
("64","763","حاضر","2024-10-07"),
("65","1668","حاضر","2024-10-07"),
("66","783","حاضر","2024-10-07"),
("67","719","حاضر","2024-10-07"),
("68","798","حاضر","2024-10-07"),
("69","842","حاضر","2024-10-07"),
("70","2626","حاضر","2024-10-07"),
("71","2701","حاضر","2024-10-07"),
("72","2983","حاضر","2024-10-07"),
("73","3047","حاضر","2024-10-07"),
("74","3194","حاضر","2024-10-07"),
("75","1591","حاضر","2024-10-07"),
("76","1617","حاضر","2024-10-07"),
("77","1618","حاضر","2024-10-07"),
("78","2420","حاضر","2024-10-07"),
("79","2258","حاضر","2024-10-07"),
("80","2719","حاضر","2024-10-07"),
("81","3036","حاضر","2024-10-07"),
("82","2810","حاضر","2024-10-07"),
("83","3037","حاضر","2024-10-07"),
("84","3060","حاضر","2024-10-07"),
("85","3190","حاضر","2024-10-07"),
("86","791","حاضر","2024-10-07"),
("87","1625","حاضر","2024-10-07"),
("88","2303","حاضر","2024-10-07"),
("89","2422","حاضر","2024-10-07"),
("90","2423","حاضر","2024-10-07"),
("91","2508","حاضر","2024-10-07"),
("92","2521","حاضر","2024-10-07"),
("93","2718","حاضر","2024-10-07"),
("94","2798","حاضر","2024-10-07"),
("95","2924","حاضر","2024-10-07"),
("96","2961","حاضر","2024-10-07"),
("97","2969","حاضر","2024-10-07"),
("98","2982","حاضر","2024-10-07"),
("99","3089","حاضر","2024-10-07"),
("100","3173","حاضر","2024-10-07");
INSERT INTO stat VALUES
("101","3192","حاضر","2024-10-07"),
("102","748","حاضر","2024-10-07"),
("103","790","حاضر","2024-10-07"),
("104","797","حاضر","2024-10-07"),
("105","794","حاضر","2024-10-07"),
("106","1593","حاضر","2024-10-07"),
("107","1619","حاضر","2024-10-07"),
("108","2514","حاضر","2024-10-07"),
("109","1623","حاضر","2024-10-07"),
("110","1621","حاضر","2024-10-07"),
("111","2611","حاضر","2024-10-07"),
("112","2927","حاضر","2024-10-07"),
("113","2909","حاضر","2024-10-07"),
("114","2894","حاضر","2024-10-07"),
("115","2893","حاضر","2024-10-07"),
("116","2882","حاضر","2024-10-07"),
("117","2881","حاضر","2024-10-07"),
("118","2840","حاضر","2024-10-07"),
("119","2802","حاضر","2024-10-07"),
("120","3022","حاضر","2024-10-07"),
("121","3016","حاضر","2024-10-07"),
("122","2972","حاضر","2024-10-07"),
("123","2960","حاضر","2024-10-07"),
("124","2959","حاضر","2024-10-07"),
("125","2937","حاضر","2024-10-07"),
("126","2936","حاضر","2024-10-07"),
("127","2935","حاضر","2024-10-07"),
("128","2931","حاضر","2024-10-07"),
("129","3055","حاضر","2024-10-07"),
("130","3085","حاضر","2024-10-07"),
("131","3090","حاضر","2024-10-07"),
("132","3172","حاضر","2024-10-07"),
("133","3191","حاضر","2024-10-07"),
("134","3135","حاضر","2024-10-07"),
("135","3193","حاضر","2024-10-07"),
("136","3090","حاضر","2024-10-08"),
("137","797","حاضر","2024-10-08"),
("138","2802","حاضر","2024-10-08"),
("139","3199","حاضر","2024-10-08"),
("140","3135","حاضر","2024-10-08"),
("141","3191","حاضر","2024-10-08"),
("142","2514","حاضر","2024-10-08"),
("143","2881","حاضر","2024-10-08"),
("144","2882","حاضر","2024-10-08"),
("145","2893","حاضر","2024-10-08"),
("146","2909","حاضر","2024-10-08"),
("147","2927","حاضر","2024-10-08"),
("148","2931","حاضر","2024-10-08"),
("149","2700","حاضر","2024-10-08"),
("150","2329","حاضر","2024-10-08"),
("151","2283","حاضر","2024-10-08"),
("152","2959","حاضر","2024-10-08"),
("153","3183","حاضر","2024-10-08"),
("154","2937","حاضر","2024-10-08"),
("155","2768","حاضر","2024-10-08"),
("156","2662","حاضر","2024-10-08"),
("157","3016","حاضر","2024-10-08"),
("159","2661","حاضر","2024-10-08"),
("160","2935","حاضر","2024-10-08"),
("161","2936","حاضر","2024-10-08"),
("162","1621","حاضر","2024-10-08"),
("163","2611","حاضر","2024-10-08"),
("164","1645","حاضر","2024-10-08"),
("165","2972","حاضر","2024-10-08"),
("166","3196","حاضر","2024-10-08"),
("167","3055","حاضر","2024-10-08"),
("168","3123","حاضر","2024-10-08"),
("169","3007","حاضر","2024-10-08"),
("170","2873","حاضر","2024-10-08"),
("171","3059","حاضر","2024-10-08"),
("172","2594","حاضر","2024-10-08"),
("173","2948","حاضر","2024-10-08"),
("174","2328","حاضر","2024-10-08"),
("175","2555","حاضر","2024-10-08"),
("176","2870","حاضر","2024-10-08"),
("177","3097","حاضر","2024-10-08"),
("178","2960","حاضر","2024-10-08"),
("179","3094","حاضر","2024-10-08"),
("180","2849","حاضر","2024-10-08"),
("181","2984","حاضر","2024-10-08"),
("182","3054","حاضر","2024-10-08"),
("183","3066","حاضر","2024-10-08"),
("184","3051","حاضر","2024-10-08"),
("185","2895","حاضر","2024-10-08"),
("186","3166","حاضر","2024-10-08"),
("187","2794","حاضر","2024-10-08"),
("188","2747","حاضر","2024-10-08"),
("189","2947","حاضر","2024-10-08"),
("190","2269","حاضر","2024-10-08"),
("191","2287","حاضر","2024-10-08"),
("192","2916","حاضر","2024-10-08"),
("193","2949","حاضر","2024-10-08"),
("194","1485","حاضر","2024-10-08"),
("195","3019","حاضر","2024-10-08"),
("196","3052","حاضر","2024-10-08"),
("197","2290","حاضر","2024-10-08"),
("198","3160","حاضر","2024-10-08"),
("199","3142","حاضر","2024-10-08"),
("200","2872","حاضر","2024-10-08"),
("201","3041","حاضر","2024-10-08");
INSERT INTO stat VALUES
("202","2898","حاضر","2024-10-08"),
("203","3197","حاضر","2024-10-08"),
("204","3185","حاضر","2024-10-08"),
("206","3148","حاضر","2024-10-08"),
("207","3186","حاضر","2024-10-08"),
("208","3064","حاضر","2024-10-08"),
("209","2264","حاضر","2024-10-08"),
("210","2265","حاضر","2024-10-08"),
("211","3147","حاضر","2024-10-08"),
("212","2950","حاضر","2024-10-08"),
("213","3012","حاضر","2024-10-08"),
("214","446","حاضر","2024-10-08"),
("215","3042","حاضر","2024-10-08"),
("216","2665","حاضر","2024-10-08"),
("217","3140","حاضر","2024-10-08"),
("218","3081","حاضر","2024-10-08"),
("219","461","حاضر","2024-10-08"),
("220","3057","حاضر","2024-10-08"),
("221","2970","حاضر","2024-10-08"),
("222","2284","حاضر","2024-10-08"),
("223","1781","حاضر","2024-10-08"),
("224","3040","حاضر","2024-10-08"),
("225","2765","حاضر","2024-10-08"),
("226","3028","حاضر","2024-10-08"),
("227","3198","حاضر","2024-10-08"),
("228","719","حاضر","2024-10-08"),
("229","842","حاضر","2024-10-08"),
("230","2701","حاضر","2024-10-08"),
("231","2855","حاضر","2024-10-08"),
("232","2983","حاضر","2024-10-08"),
("233","2923","حاضر","2024-10-08"),
("234","3030","حاضر","2024-10-08"),
("235","798","حاضر","2024-10-08"),
("236","3194","حاضر","2024-10-08"),
("237","3164","حاضر","2024-10-08"),
("238","3095","حاضر","2024-10-08"),
("239","3096","حاضر","2024-10-08"),
("240","2741","حاضر","2024-10-08"),
("241","783","حاضر","2024-10-08"),
("242","1591","حاضر","2024-10-08"),
("243","1617","حاضر","2024-10-08"),
("244","1618","حاضر","2024-10-08"),
("245","2258","حاضر","2024-10-08"),
("246","2719","حاضر","2024-10-08"),
("247","2554","حاضر","2024-10-08"),
("248","2420","حاضر","2024-10-08"),
("249","3060","حاضر","2024-10-08"),
("250","3037","حاضر","2024-10-08"),
("251","3036","حاضر","2024-10-08"),
("252","2810","حاضر","2024-10-08"),
("253","2722","حاضر","2024-10-08"),
("254","2508","حاضر","2024-10-08"),
("255","2423","حاضر","2024-10-08"),
("256","2303","حاضر","2024-10-08"),
("257","2422","حاضر","2024-10-08"),
("258","1668","حاضر","2024-10-08"),
("259","1625","حاضر","2024-10-08"),
("260","791","حاضر","2024-10-08"),
("261","763","حاضر","2024-10-08"),
("262","762","حاضر","2024-10-08"),
("263","2521","حاضر","2024-10-08"),
("264","2718","حاضر","2024-10-08"),
("265","2798","حاضر","2024-10-08"),
("266","2961","حاضر","2024-10-08"),
("267","2982","حاضر","2024-10-08"),
("268","2969","حاضر","2024-10-08"),
("269","3050","حاضر","2024-10-08"),
("270","3089","حاضر","2024-10-08"),
("271","3192","حاضر","2024-10-08"),
("272","3173","حاضر","2024-10-08"),
("273","1478","حاضر","2024-10-08"),
("274","1893","حاضر","2024-10-08"),
("275","2277","حاضر","2024-10-08"),
("276","2525","حاضر","2024-10-08"),
("277","2657","حاضر","2024-10-08"),
("278","2677","حاضر","2024-10-08"),
("279","2695","حاضر","2024-10-08"),
("280","2783","حاضر","2024-10-08"),
("281","2791","حاضر","2024-10-08"),
("282","2801","حاضر","2024-10-08"),
("283","2811","حاضر","2024-10-08"),
("284","2985","حاضر","2024-10-08"),
("285","2988","حاضر","2024-10-08"),
("286","3023","حاضر","2024-10-08"),
("287","3113","حاضر","2024-10-08"),
("288","3117","حاضر","2024-10-08"),
("289","2278","حاضر","2024-10-08"),
("290","2404","حاضر","2024-10-08"),
("291","2405","حاضر","2024-10-08"),
("292","2445","حاضر","2024-10-08"),
("293","2529","حاضر","2024-10-08"),
("294","2625","حاضر","2024-10-08"),
("295","2632","حاضر","2024-10-08"),
("296","2733","حاضر","2024-10-08"),
("297","2745","حاضر","2024-10-08"),
("298","2770","حاضر","2024-10-08"),
("299","2826","حاضر","2024-10-08"),
("300","2861","حاضر","2024-10-08"),
("301","2899","حاضر","2024-10-08"),
("302","2958","حاضر","2024-10-08");
INSERT INTO stat VALUES
("303","3027","حاضر","2024-10-08"),
("304","3074","حاضر","2024-10-08"),
("305","3075","حاضر","2024-10-08"),
("306","3159","حاضر","2024-10-08"),
("307","3203","حاضر","2024-10-08"),
("308","2986","حاضر","2024-10-08"),
("309","2987","حاضر","2024-10-08"),
("310","2990","حاضر","2024-10-08"),
("311","3026","حاضر","2024-10-08"),
("312","3125","حاضر","2024-10-08"),
("313","3126","حاضر","2024-10-08"),
("314","738","حاضر","2024-10-08"),
("315","1020","حاضر","2024-10-08"),
("316","1021","حاضر","2024-10-08"),
("317","1554","حاضر","2024-10-08"),
("318","1699","حاضر","2024-10-08"),
("319","2703","حاضر","2024-10-08"),
("320","2823","حاضر","2024-10-08"),
("321","2864","حاضر","2024-10-08"),
("322","2921","حاضر","2024-10-08"),
("323","2993","حاضر","2024-10-08"),
("324","1377","حاضر","2024-10-08"),
("325","2378","حاضر","2024-10-08"),
("326","2379","حاضر","2024-10-08"),
("327","2622","حاضر","2024-10-08"),
("328","2860","حاضر","2024-10-08"),
("329","2880","حاضر","2024-10-08"),
("330","2901","حاضر","2024-10-08"),
("331","2975","حاضر","2024-10-08"),
("332","3008","حاضر","2024-10-08"),
("333","3010","حاضر","2024-10-08"),
("334","3067","حاضر","2024-10-08"),
("335","3145","حاضر","2024-10-08"),
("336","1697","حاضر","2024-10-08"),
("337","2359","حاضر","2024-10-08"),
("338","2606","حاضر","2024-10-08"),
("339","2755","حاضر","2024-10-08"),
("340","2820","حاضر","2024-10-08"),
("341","2824","حاضر","2024-10-08"),
("342","2878","حاضر","2024-10-08"),
("343","2919","حاضر","2024-10-08"),
("344","2920","حاضر","2024-10-08"),
("345","2973","حاضر","2024-10-08"),
("346","3068","حاضر","2024-10-08"),
("347","3138","حاضر","2024-10-08"),
("348","3139","حاضر","2024-10-08"),
("349","2101","حاضر","2024-10-08"),
("350","2623","حاضر","2024-10-08"),
("351","2817","حاضر","2024-10-08"),
("352","2818","حاضر","2024-10-08"),
("353","2847","حاضر","2024-10-08"),
("354","2974","حاضر","2024-10-08"),
("355","2991","حاضر","2024-10-08"),
("356","2992","حاضر","2024-10-08"),
("357","3009","حاضر","2024-10-08"),
("358","3161","حاضر","2024-10-08");




CREATE TABLE `stud_pay` (
  `id_P` int(100) NOT NULL AUTO_INCREMENT,
  `id_pay` varchar(250) NOT NULL,
  `cash_stud` float NOT NULL,
  `date_exp` date NOT NULL,
  `id_stud` int(11) NOT NULL,
  PRIMARY KEY (`id_P`),
  KEY `id_stud` (`id_stud`),
  CONSTRAINT `stud_pay_ibfk_1` FOREIGN KEY (`id_stud`) REFERENCES `stud_tb` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3193 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


INSERT INTO stud_pay VALUES
("340","302","100","2024-10-10","369"),
("362","286","100","2024-10-03","391"),
("363","394","100","2024-11-05","392"),
("369","234","100","2024-09-03","398"),
("385","374","100","2024-10-29","414"),
("386","375","100","2024-10-29","415"),
("407","287","100","2024-10-25","436"),
("417","283","100","2024-10-25","446"),
("418","243","100","2024-10-06","447"),
("432","298","100","2024-10-29","461"),
("438","252","100","2024-10-04","467"),
("445","242","100","2024-10-08","474"),
("519","69","100","2024-10-06","548"),
("633","589","100","2024-10-25","662"),
("661","606","100","2024-11-05","690"),
("662","605","100","2024-11-05","691"),
("690","585","100","2024-10-13","719"),
("709","658","100","2024-10-31","738"),
("719","644","50","2024-10-23","748"),
("729","562","100","2024-10-06","758"),
("733","593","100","2024-10-17","762"),
("734","592","100","2024-10-17","763"),
("753","565","100","2024-10-06","782"),
("754","651","100","2024-11-05","783"),
("761","577","100","2024-10-11","790"),
("762","578","100","2024-10-11","791"),
("765","643","50","2024-10-23","794"),
("768","629","100","2024-10-20","797"),
("769","626","100","2024-10-24","798"),
("813","603","100","2024-10-22","842"),
("833","520","100","2024-10-29","862"),
("845","365","100","2024-10-26","874"),
("846","366","100","2024-10-26","875"),
("883","505","100","2024-10-26","912"),
("921","236","100","2024-09-22","950"),
("991","86","100","2024-10-14","1020"),
("992","85","100","2024-10-14","1021"),
("1002","229","100","2024-10-03","1031"),
("1018","306","100","2024-10-16","1047"),
("1019","307","100","2024-10-16","1048"),
("1023","304","100","2024-10-15","1052"),
("1077","523","100","2024-10-29","1106"),
("1099","305","100","2024-10-15","1128"),
("1153","397","100","2024-11-05","1182"),
("1167","367","100","2024-10-29","1196"),
("1189","642","100","2024-10-26","1218"),
("1201","562","100","2024-10-08","1230"),
("1248","443","100","2024-10-10","1277"),
("1276","565","100","2024-10-05","1305"),
("1277","564","100","2024-10-05","1306"),
("1289","383","100","2024-10-31","1318"),
("1290","388","100","2024-11-01","1319"),
("1291","389","100","2024-11-01","1320"),
("1299","602","100","2024-10-30","1328"),
("1348","100","100","2024-10-24","1377"),
("1375","457","100","2024-10-16","1404"),
("1378","567","100","2024-11-05","1407"),
("1413","637","100","2024-10-25","1442"),
("1430","68","100","2024-10-04","1459"),
("1449","200","100","2024-10-22","1478"),
("1455","423","100","2024-11-05","1484"),
("1456","428","100","2024-11-03","1485"),
("1478","235","100","2024-09-03","1507"),
("1489","437","100","2024-10-19","1518"),
("1525","103","100","2024-10-23","1554"),
("1541","213","100","2024-09-22","1570"),
("1562","584","100","2024-10-13","1591"),
("1564","649","100","2024-10-31","1593"),
("1588","616","75","2024-10-22","1617"),
("1589","615","75","2024-10-22","1618"),
("1590","614","100","2024-10-22","1619"),
("1592","571","100","2024-10-10","1621"),
("1594","567","100","2024-10-08","1623"),
("1596","574","100","2024-10-10","1625"),
("1601","409","100","2024-11-07","1630"),
("1616","286","100","2024-10-21","1645"),
("1623","197","100","2024-09-05","1652"),
("1639","585","100","2024-10-13","1668"),
("1668","129","100","2024-11-03","1697"),
("1670","108","100","2024-10-25","1699"),
("1698","540","100","2024-09-22","1727"),
("1699","539","100","2024-09-22","1728"),
("1721","380","100","2024-10-31","1750"),
("1752","250","100","2024-10-10","1781"),
("1806","294","100","2024-10-02","1835"),
("1864","239","100","2024-10-28","1893"),
("1870","604","100","2024-11-01","1899"),
("1878","601","100","2024-10-30","1907"),
("1900","590","100","2024-10-26","1929"),
("1901","591","50","2024-10-26","1930"),
("1904","574","100","2024-11-07","1933"),
("1936","561","100","2024-11-05","1965"),
("1951","191","100","2024-10-07","1980"),
("2024","562","100","2024-11-05","2053"),
("2038","560","100","2024-10-30","2067"),
("2039","544","100","2024-10-31","2068"),
("2063","516","100","2024-10-06","2092"),
("2072","76","100","2024-10-15","2101"),
("2076","513","100","2024-10-29","2105"),
("2088","275","100","2024-09-21","2117");
INSERT INTO stud_pay VALUES
("2096","537","100","2024-11-02","2125"),
("2130","559","100","2024-10-02","2159"),
("2137","318","100","2024-10-17","2166"),
("2163","402","100","2024-10-01","2192"),
("2202","281","100","2024-10-04","2231"),
("2205","404","100","2024-10-30","2234"),
("2206","405","100","2024-10-30","2235"),
("2229","576","100","2024-10-10","2258"),
("2235","430","100","2024-11-07","2264"),
("2236","430","100","2024-11-07","2265"),
("2240","419","100","2024-10-31","2269"),
("2248","259","100","2024-11-07","2277"),
("2249","229","100","2024-10-24","2278"),
("2254","290","100","2024-10-26","2283"),
("2255","291","100","2024-10-26","2284"),
("2258","258","100","2024-10-16","2287"),
("2261","434","100","2024-11-04","2290"),
("2274","656","100","2024-11-01","2303"),
("2279","245","100","2024-10-08","2308"),
("2299","406","100","2024-10-30","2328"),
("2300","407","100","2024-10-30","2329"),
("2306","560","100","2024-10-04","2335"),
("2307","561","100","2024-10-04","2336"),
("2309","595","100","2024-10-29","2338"),
("2316","233","100","2024-10-05","2345"),
("2317","233","100","2024-10-05","2346"),
("2330","73","100","2024-10-15","2359"),
("2348","402","100","2024-11-05","2377"),
("2349","80","100","2024-10-15","2378"),
("2350","81","100","2024-10-15","2379"),
("2354","311","100","2024-10-16","2383"),
("2356","396","100","2024-11-05","2385"),
("2365","64","100","2024-10-01","2394"),
("2372","225","100","2024-09-22","2401"),
("2375","249","100","2024-10-31","2404"),
("2376","250","100","2024-10-31","2405"),
("2391","581","100","2024-10-16","2420"),
("2393","586","100","2024-10-16","2422"),
("2394","587","100","2024-10-16","2423"),
("2404","594","100","2024-10-29","2433"),
("2414","312","100","2024-10-16","2443"),
("2416","219","100","2024-10-22","2445"),
("2431","515","100","2024-10-31","2460"),
("2434","521","100","2024-11-01","2463"),
("2435","522","100","2024-11-01","2464"),
("2438","393","100","2024-10-02","2467"),
("2439","559","100","2024-11-05","2468"),
("2441","557","100","2024-11-05","2470"),
("2442","558","100","2024-11-05","2471"),
("2444","435","0","2024-10-10","2473"),
("2451","447","100","2024-10-12","2480"),
("2452","438","100","2024-10-09","2481"),
("2454","551","100","2024-11-01","2483"),
("2458","444","50","2024-10-10","2487"),
("2459","445","100","2024-10-10","2488"),
("2462","539","100","2024-10-31","2491"),
("2476","357","100","2024-10-24","2505"),
("2477","346","100","2024-10-23","2506"),
("2479","622","100","2024-10-23","2508"),
("2485","570","100","2024-10-09","2514"),
("2487","556","100","2024-10-03","2516"),
("2488","555","100","2024-10-03","2517"),
("2492","583","100","2024-10-16","2521"),
("2493","623","100","2024-10-23","2522"),
("2496","243","100","2024-10-27","2525"),
("2500","255","100","2024-11-01","2529"),
("2506","608","100","2024-11-05","2535"),
("2507","520","100","2024-09-07","2536"),
("2511","435","100","2024-11-01","2540"),
("2517","199","100","2024-09-04","2546"),
("2522","290","100","2024-10-02","2551"),
("2523","291","100","2024-10-02","2552"),
("2525","659","100","2024-11-05","2554"),
("2526","660","100","2024-11-05","2555"),
("2529","511","100","2024-09-06","2558"),
("2538","415","100","2024-11-07","2567"),
("2539","416","100","2024-11-07","2568"),
("2543","547","100","2024-10-31","2572"),
("2544","548","100","2024-10-31","2573"),
("2549","468","100","2024-10-15","2578"),
("2554","400","100","2024-11-05","2583"),
("2557","609","100","2024-10-31","2586"),
("2563","359","100","2024-10-26","2592"),
("2564","360","100","2024-10-26","2593"),
("2565","297","100","2024-10-29","2594"),
("2567","53","100","2024-09-29","2596"),
("2571","358","100","2024-10-25","2600"),
("2572","398","100","2024-11-05","2601"),
("2575","385","100","2024-10-31","2604"),
("2577","94","100","2024-10-22","2606"),
("2578","390","100","2024-11-01","2607"),
("2579","391","100","2024-11-01","2608"),
("2580","190","100","2024-10-07","2609"),
("2581","268","100","2024-09-25","2610"),
("2582","582","100","2024-10-16","2611"),
("2585","225","50","2024-10-24","2614"),
("2589","184","100","2024-09-01","2618"),
("2592","572","100","2024-11-09","2621"),
("2593","109","100","2024-11-03","2622"),
("2594","110","100","2024-11-03","2623");
INSERT INTO stud_pay VALUES
("2596","251","100","2024-11-05","2625"),
("2597","647","100","2024-11-01","2626"),
("2599","414","100","2024-10-03","2628"),
("2600","312","100","2024-10-03","2629"),
("2601","188","100","2024-10-08","2630"),
("2603","252","100","2024-11-05","2632"),
("2604","529","100","2024-11-04","2633"),
("2609","246","100","2024-10-08","2638"),
("2612","607","100","2024-11-05","2641"),
("2620","489","100","2024-10-22","2649"),
("2623","533","100","2024-10-31","2652"),
("2626","466","100","2024-10-18","2655"),
("2628","238","100","2024-10-24","2657"),
("2632","288","100","2024-10-24","2661"),
("2633","289","100","2024-10-24","2662"),
("2636","420","100","2024-10-29","2665"),
("2645","498","100","2024-10-23","2674"),
("2646","499","100","2024-10-23","2675"),
("2647","292","100","2024-10-01","2676"),
("2648","248","100","2024-10-31","2677"),
("2649","392","100","2024-11-05","2678"),
("2651","190","100","2024-09-01","2680"),
("2653","226","100","2024-10-03","2682"),
("2657","323","100","2024-10-17","2686"),
("2658","303","100","2024-10-11","2687"),
("2660","567","100","2024-10-08","2689"),
("2661","611","100","2024-11-06","2690"),
("2663","551","100","2024-10-01","2692"),
("2664","550","100","2024-10-01","2693"),
("2666","202","100","2024-10-22","2695"),
("2667","192","100","2024-10-08","2696"),
("2669","541","100","2024-09-12","2698"),
("2670","461","100","2024-10-17","2699"),
("2671","256","100","2024-10-14","2700"),
("2672","575","100","2024-10-12","2701"),
("2674","120","100","2024-11-01","2703"),
("2676","412","100","2024-11-05","2705"),
("2680","525","100","2024-09-15","2709"),
("2681","326","100","2024-10-18","2710"),
("2682","456","100","2024-10-16","2711"),
("2683","535","100","2024-10-26","2712"),
("2684","334","100","2024-10-26","2713"),
("2689","595","100","2024-10-21","2718"),
("2690","633","100","2024-10-21","2719"),
("2693","652","100","2024-10-31","2722"),
("2694","587","100","2024-10-25","2723"),
("2695","586","100","2024-10-25","2724"),
("2696","481","0","2024-10-24","2725"),
("2702","553","100","2024-10-31","2731"),
("2703","415","100","2024-10-03","2732"),
("2704","232","100","2024-10-25","2733"),
("2706","410","100","2024-10-30","2735"),
("2708","532","100","2024-10-31","2737"),
("2710","542","100","2024-10-31","2739"),
("2712","418","100","2024-11-01","2741"),
("2714","545","100","2024-10-03","2743"),
("2715","65","100","2024-10-03","2744"),
("2716","253","100","2024-11-05","2745"),
("2718","422","100","2024-11-05","2747"),
("2719","399","100","2024-11-05","2748"),
("2720","563","100","2024-10-04","2749"),
("2721","559","0","2024-11-01","2750"),
("2723","192","100","2024-09-03","2752"),
("2725","407","100","2024-11-07","2754"),
("2726","124","100","2024-11-04","2755"),
("2727","196","100","2024-09-05","2756"),
("2728","196","100","2024-09-05","2757"),
("2730","504","100","2024-09-05","2759"),
("2731","507","100","2024-09-06","2760"),
("2732","566","100","2024-10-06","2761"),
("2733","576","100","2024-11-07","2762"),
("2735","348","100","2024-10-23","2764"),
("2736","251","100","2024-10-10","2765"),
("2737","450","100","2024-10-12","2766"),
("2738","319","100","2024-10-15","2767"),
("2739","426","100","2024-11-05","2768"),
("2740","207","100","2024-09-12","2769"),
("2741","193","100","2024-10-15","2770"),
("2742","458","100","2024-10-14","2771"),
("2743","459","100","2024-10-16","2772"),
("2745","257","100","2024-10-15","2774"),
("2746","257","100","2024-10-15","2775"),
("2747","542","100","2024-09-14","2776"),
("2748","543","100","2024-09-14","2777"),
("2749","324","100","2024-10-17","2778"),
("2751","212","100","2024-10-18","2780"),
("2752","213","100","2024-10-18","2781"),
("2754","201","100","2024-10-18","2783"),
("2755","199","100","2024-10-17","2784"),
("2757","528","100","2024-09-20","2786"),
("2758","403","100","2024-10-29","2787"),
("2759","165","100","2024-09-25","2788"),
("2760","364","100","2024-10-31","2789"),
("2761","218","100","2024-09-26","2790"),
("2762","237","100","2024-10-25","2791"),
("2763","552","100","2024-10-28","2792"),
("2764","242","100","2024-10-25","2793"),
("2765","415","100","2024-10-31","2794"),
("2766","531","100","2024-10-30","2795"),
("2768","549","100","2024-10-31","2797");
INSERT INTO stud_pay VALUES
("2769","640","100","2024-10-31","2798"),
("2770","568","100","2024-11-05","2799"),
("2771","544","100","2024-10-01","2800"),
("2772","247","100","2024-10-31","2801"),
("2773","650","100","2024-11-01","2802"),
("2774","560","100","2024-11-05","2803"),
("2775","541","100","2024-11-01","2804"),
("2777","563","100","2024-11-05","2806"),
("2778","646","100","2024-11-01","2807"),
("2779","575","100","2024-11-07","2808"),
("2780","553","100","2024-10-03","2809"),
("2781","655","100","2024-11-02","2810"),
("2782","258","100","2024-11-05","2811"),
("2783","573","100","2024-11-07","2812"),
("2784","421","100","2024-10-03","2813"),
("2785","556","100","2024-11-05","2814"),
("2786","61","100","2024-10-02","2815"),
("2788","117","100","2024-10-31","2817"),
("2789","123","100","2024-10-31","2818"),
("2790","59","100","2024-10-01","2819"),
("2791","132","100","2024-11-02","2820"),
("2794","122","100","2024-10-31","2823"),
("2795","125","100","2024-11-02","2824"),
("2796","54","100","2024-09-25","2825"),
("2797","256","100","2024-11-05","2826"),
("2798","566","100","2024-11-05","2827"),
("2799","429","100","2024-10-08","2828"),
("2800","430","100","2024-10-08","2829"),
("2801","406","100","2024-11-07","2830"),
("2802","413","100","2024-11-07","2831"),
("2803","561","100","2024-10-08","2832"),
("2805","432","100","2024-10-08","2834"),
("2806","433","100","2024-10-08","2835"),
("2807","436","100","2024-11-07","2836"),
("2808","241","100","2024-10-08","2837"),
("2809","568","100","2024-10-09","2838"),
("2810","439","100","2024-10-09","2839"),
("2811","572","100","2024-10-10","2840"),
("2812","573","100","2024-10-10","2841"),
("2813","244","100","2024-10-09","2842"),
("2814","440","100","2024-10-10","2843"),
("2815","441","50","2024-10-10","2844"),
("2816","571","100","2024-11-07","2845"),
("2817","446","100","2024-10-11","2846"),
("2818","70","100","2024-10-11","2847"),
("2819","557","100","2024-10-04","2848"),
("2820","248","100","2024-10-11","2849"),
("2821","249","100","2024-10-11","2850"),
("2822","448","100","2024-10-11","2851"),
("2823","449","50","2024-10-11","2852"),
("2825","68","100","2024-10-04","2854"),
("2826","579","100","2024-10-11","2855"),
("2827","569","100","2024-10-12","2856"),
("2828","308","100","2024-10-16","2857"),
("2829","309","100","2024-10-16","2858"),
("2830","310","100","2024-10-16","2859"),
("2831","71","100","2024-10-16","2860"),
("2832","194","100","2024-10-16","2861"),
("2833","195","100","2024-10-16","2862"),
("2834","313","100","2024-10-16","2863"),
("2835","72","100","2024-10-16","2864"),
("2836","451","100","2024-10-16","2865"),
("2837","452","100","2024-10-16","2866"),
("2838","453","100","2024-10-16","2867"),
("2839","454","100","2024-10-16","2868"),
("2840","455","100","2024-10-16","2869"),
("2841","255","100","2024-10-16","2870"),
("2842","254","100","2024-10-12","2871"),
("2843","253","100","2024-10-16","2872"),
("2844","253","100","2024-10-16","2873"),
("2845","314","100","2024-10-16","2874"),
("2846","315","100","2024-10-16","2875"),
("2847","315","75","2024-10-16","2876"),
("2848","316","75","2024-10-16","2877"),
("2849","74","100","2024-10-17","2878"),
("2850","317","100","2024-10-16","2879"),
("2851","75","100","2024-10-17","2880"),
("2852","588","100","2024-10-17","2881"),
("2853","589","100","2024-10-17","2882"),
("2854","460","100","2024-10-16","2883"),
("2855","320","100","2024-10-17","2884"),
("2856","321","100","2024-10-22","2885"),
("2857","462","100","2024-10-17","2886"),
("2858","463","100","2024-10-17","2887"),
("2859","464","0","2024-10-17","2888"),
("2860","465","0","2024-10-17","2889"),
("2861","196","100","2024-10-16","2890"),
("2862","197","100","2024-10-16","2891"),
("2863","322","100","2024-10-17","2892"),
("2864","590","100","2024-10-17","2893"),
("2865","591","100","2024-10-18","2894"),
("2866","259","100","2024-10-18","2895"),
("2867","467","100","2024-10-18","2896"),
("2869","260","100","2024-10-18","2898"),
("2870","198","100","2024-10-18","2899"),
("2872","77","100","2024-10-18","2901"),
("2875","570","100","2024-10-19","2904"),
("2876","571","50","2024-10-19","2905"),
("2877","469","100","2024-10-19","2906"),
("2878","261","100","2024-10-19","2907");
INSERT INTO stud_pay VALUES
("2879","573","100","2024-10-19","2908"),
("2880","594","100","2024-10-19","2909"),
("2881","327","100","2024-10-19","2910"),
("2882","471","100","2024-10-19","2911"),
("2883","328","100","2024-10-19","2912"),
("2885","472","100","2024-10-19","2914"),
("2886","473","100","2024-10-19","2915"),
("2887","262","100","2024-10-19","2916"),
("2888","474","100","2024-10-19","2917"),
("2889","475","100","2024-10-19","2918"),
("2890","78","100","2024-10-18","2919"),
("2891","79","100","2024-10-19","2920"),
("2892","82","100","2024-10-19","2921"),
("2893","84","100","2024-10-19","2922"),
("2894","596","100","2024-10-22","2923"),
("2895","597","100","2024-10-22","2924"),
("2896","329","100","2024-10-22","2925"),
("2897","330","100","2024-10-22","2926"),
("2898","598","100","2024-10-22","2927"),
("2899","331","100","2024-10-22","2928"),
("2900","599","100","2024-10-22","2929"),
("2901","332","100","2024-10-22","2930"),
("2902","600","100","2024-10-22","2931"),
("2903","333","100","2024-10-22","2932"),
("2904","576","100","2024-10-22","2933"),
("2905","334","100","2024-10-22","2934"),
("2906","602","100","2024-10-22","2935"),
("2907","604","100","2024-10-22","2936"),
("2908","605","100","2024-10-22","2937"),
("2909","203","100","2024-10-22","2938"),
("2910","204","100","2024-10-22","2939"),
("2911","476","100","2024-10-22","2940"),
("2912","477","100","2024-10-22","2941"),
("2913","478","100","2024-10-22","2942"),
("2914","479","50","2024-10-12","2943"),
("2915","480","100","2024-10-22","2944"),
("2916","481","100","2024-10-22","2945"),
("2917","482","100","2024-10-22","2946"),
("2918","264","100","2024-10-22","2947"),
("2919","265","100","2024-10-22","2948"),
("2920","266","100","2024-10-22","2949"),
("2921","267","100","2024-10-22","2950"),
("2922","577","100","2024-10-22","2951"),
("2923","574","100","2024-10-22","2952"),
("2924","335","100","2024-10-22","2953"),
("2925","336","100","2024-10-22","2954"),
("2926","205","100","2024-10-22","2955"),
("2927","206","100","2024-10-22","2956"),
("2928","207","100","2024-10-22","2957"),
("2929","208","100","2024-10-22","2958"),
("2930","608","100","2024-10-22","2959"),
("2931","609","100","2024-10-22","2960"),
("2932","610","100","2024-10-22","2961"),
("2933","578","100","2024-10-22","2962"),
("2934","483","100","2024-10-22","2963"),
("2935","484","100","2024-10-22","2964"),
("2936","486","100","2024-10-22","2965"),
("2937","487","100","2024-10-22","2966"),
("2938","488","100","2024-10-22","2967"),
("2939","209","100","2024-10-22","2968"),
("2940","611","100","2024-10-22","2969"),
("2941","263","100","2024-10-22","2970"),
("2942","337","100","2024-10-22","2971"),
("2943","612","100","2024-10-22","2972"),
("2944","89","100","2024-10-22","2973"),
("2945","90","100","2024-10-22","2974"),
("2946","91","100","2024-10-22","2975"),
("2947","92","100","2024-10-22","2976"),
("2948","338","100","2024-10-23","2977"),
("2949","339","100","2024-10-23","2978"),
("2950","340","100","2024-10-23","2979"),
("2951","341","100","2024-10-23","2980"),
("2952","342","100","2024-10-23","2981"),
("2953","619","100","2024-10-23","2982"),
("2954","618","100","2024-10-23","2983"),
("2955","273","100","2024-10-23","2984"),
("2956","210","100","2024-10-23","2985"),
("2957","211","100","2024-10-23","2986"),
("2958","214","100","2024-10-23","2987"),
("2959","215","100","2024-10-23","2988"),
("2960","343","100","2024-10-23","2989"),
("2961","216","100","2024-10-23","2990"),
("2962","93","100","2024-10-22","2991"),
("2963","95","100","2024-10-22","2992"),
("2964","96","100","2024-10-23","2993"),
("2965","344","100","2024-10-23","2994"),
("2966","345","100","2024-10-23","2995"),
("2967","620","100","2024-10-23","2996"),
("2968","485","100","2024-10-22","2997"),
("2969","617","100","2024-10-23","2998"),
("2970","491","100","2024-10-23","2999"),
("2971","492","100","2024-10-23","3000"),
("2972","493","100","2024-10-23","3001"),
("2973","494","100","2024-10-23","3002"),
("2974","495","100","2024-10-23","3003"),
("2976","496","75","2024-10-23","3005"),
("2977","497","100","2024-10-23","3006"),
("2978","276","100","2024-10-23","3007"),
("2979","97","100","2024-10-23","3008"),
("2980","98","100","2024-10-23","3009");
INSERT INTO stud_pay VALUES
("2981","99","100","2024-10-23","3010"),
("2982","579","100","2024-10-23","3011"),
("2983","269","100","2024-10-22","3012"),
("2984","277","100","2024-10-23","3013"),
("2985","580","100","2024-10-23","3014"),
("2986","581","100","2024-10-24","3015"),
("2987","624","100","2024-10-24","3016"),
("2988","500","100","2024-10-24","3017"),
("2989","347","100","2024-10-24","3018"),
("2990","278","100","2024-10-24","3019"),
("2991","582","100","2024-10-24","3020"),
("2992","490","100","2024-10-23","3021"),
("2993","625","100","2024-10-24","3022"),
("2994","217","100","2024-10-24","3023"),
("2995","349","100","2024-10-24","3024"),
("2996","218","100","2024-10-24","3025"),
("2997","220","100","2024-10-24","3026"),
("2998","221","100","2024-10-24","3027"),
("2999","280","100","2024-10-24","3028"),
("3000","222","100","2024-10-23","3029"),
("3001","627","100","2024-10-24","3030"),
("3002","583","100","2024-10-24","3031"),
("3004","279","100","2024-10-24","3033"),
("3005","350","100","2024-10-24","3034"),
("3006","279","100","2024-10-24","3035"),
("3007","630","100","2024-10-24","3036"),
("3008","631","100","2024-10-24","3037"),
("3009","351","100","2024-10-24","3038"),
("3010","352","100","2024-10-24","3039"),
("3011","281","100","2024-10-25","3040"),
("3012","282","100","2024-10-25","3041"),
("3013","283","100","2024-10-25","3042"),
("3014","353","100","2024-10-25","3043"),
("3015","354","100","2024-10-25","3044"),
("3016","355","100","2024-10-24","3045"),
("3017","356","100","2024-10-24","3046"),
("3018","632","100","2024-10-25","3047"),
("3019","501","100","2024-10-25","3048"),
("3020","502","100","2024-10-23","3049"),
("3021","628","100","2024-10-24","3050"),
("3022","284","100","2024-10-25","3051"),
("3023","285","100","2024-10-25","3052"),
("3024","503","100","2024-10-25","3053"),
("3025","271","100","2024-10-23","3054"),
("3026","606","100","2024-10-22","3055"),
("3027","504","100","2024-10-25","3056"),
("3028","270","100","2024-10-23","3057"),
("3029","287","100","2024-10-25","3058"),
("3030","274","100","2024-10-23","3059"),
("3031","607","100","2024-10-22","3060"),
("3032","621","100","2024-10-23","3061"),
("3033","572","100","2024-10-19","3062"),
("3034","575","100","2024-10-22","3063"),
("3035","268","100","2024-10-22","3064"),
("3036","272","100","2024-10-23","3065"),
("3037","275","100","2024-10-23","3066"),
("3038","102","100","2024-10-24","3067"),
("3039","101","100","2024-10-22","3068"),
("3040","584","100","2024-10-25","3069"),
("3041","585","50","2024-10-25","3070"),
("3042","292","100","2024-10-26","3071"),
("3043","361","100","2024-10-26","3072"),
("3044","293","100","2024-10-26","3073"),
("3045","223","100","2024-10-25","3074"),
("3046","224","100","2024-10-23","3075"),
("3047","226","50","2024-10-26","3076"),
("3048","588","100","2024-10-25","3077"),
("3049","362","100","2024-10-26","3078"),
("3050","363","100","2024-10-26","3079"),
("3051","294","100","2024-10-26","3080"),
("3052","295","100","2024-10-26","3081"),
("3053","104","100","2024-10-29","3082"),
("3054","106","100","2024-10-29","3083"),
("3055","592","100","2024-10-29","3084"),
("3056","634","50","2024-10-29","3085"),
("3057","368","100","2024-10-29","3086"),
("3058","369","100","2024-10-29","3087"),
("3059","370","100","2024-10-29","3088"),
("3060","636","100","2024-10-29","3089"),
("3061","635","0","2024-10-29","3090"),
("3062","371","100","2024-10-26","3091"),
("3063","296","100","2024-10-29","3092"),
("3064","593","100","2024-10-29","3093"),
("3065","299","100","2024-10-29","3094"),
("3066","300","100","2024-10-29","3095"),
("3067","401","100","2024-10-29","3096"),
("3068","402","100","2024-10-29","3097"),
("3069","506","100","2024-10-26","3098"),
("3070","507","100","2024-10-26","3099"),
("3071","508","100","2024-10-29","3100"),
("3072","509","100","2024-10-29","3101"),
("3073","510","100","2024-10-29","3102"),
("3074","511","100","2024-10-29","3103"),
("3075","512","100","2024-10-29","3104"),
("3076","514","100","2024-10-29","3105"),
("3077","516","100","2024-10-29","3106"),
("3078","517","100","2024-10-29","3107"),
("3079","518","100","2024-10-29","3108"),
("3080","519","100","2024-10-29","3109"),
("3081","230","100","2024-10-22","3110");
INSERT INTO stud_pay VALUES
("3082","228","100","2024-10-25","3111"),
("3083","227","100","2024-10-26","3112"),
("3084","231","100","2024-10-29","3113"),
("3085","233","100","2024-10-25","3114"),
("3086","234","100","2024-10-29","3115"),
("3087","235","100","2024-10-29","3116"),
("3088","236","100","2024-10-29","3117"),
("3089","372","100","2024-10-29","3118"),
("3090","373","100","2024-10-29","3119"),
("3091","596","100","2024-10-30","3120"),
("3092","376","100","2024-10-30","3121"),
("3093","377","100","2024-10-30","3122"),
("3094","408","100","2024-10-30","3123"),
("3095","409","100","2024-10-30","3124"),
("3096","240","100","2024-10-29","3125"),
("3097","241","100","2024-10-29","3126"),
("3098","597","100","2024-10-30","3127"),
("3099","378","100","2024-10-30","3128"),
("3100","524","100","2024-10-29","3129"),
("3101","525","50","2024-10-29","3130"),
("3102","526","100","2024-10-30","3131"),
("3103","527","100","2024-10-30","3132"),
("3104","528","100","2024-10-30","3133"),
("3105","530","100","2024-10-30","3134"),
("3106","638","100","2024-10-30","3135"),
("3107","403","75","2024-10-30","3136"),
("3108","379","100","2024-10-30","3137"),
("3109","111","100","2024-10-30","3138"),
("3110","112","100","2024-10-30","3139"),
("3111","411","100","2024-10-30","3140"),
("3112","639","100","2024-10-30","3141"),
("3113","412","100","2024-10-31","3142"),
("3114","113","100","2024-10-30","3143"),
("3115","114","100","2024-10-30","3144"),
("3116","115","100","2024-10-30","3145"),
("3117","381","100","2024-10-31","3146"),
("3118","413","100","2024-10-31","3147"),
("3119","414","100","2024-10-31","3148"),
("3120","598","100","2024-10-31","3149"),
("3121","382","100","2024-10-31","3150"),
("3122","538","100","2024-10-31","3151"),
("3123","540","100","2024-10-31","3152"),
("3124","543","100","2024-10-31","3153"),
("3125","445","100","2024-10-31","3154"),
("3126","384","100","2024-10-31","3155"),
("3127","599","100","2024-10-31","3156"),
("3128","546","100","2024-10-31","3157"),
("3129","600","100","2024-10-31","3158"),
("3130","244","100","2024-10-31","3159"),
("3131","416","100","2024-10-31","3160"),
("3132","116","100","2024-10-31","3161"),
("3133","386","100","2024-11-01","3162"),
("3134","387","100","2024-11-01","3163"),
("3135","645","0","2024-11-01","3164"),
("3136","603","100","2024-11-01","3165"),
("3137","417","100","2024-11-01","3166"),
("3138","246","100","2024-10-31","3167"),
("3139","550","100","2024-11-01","3168"),
("3140","554","100","2024-11-01","3169"),
("3141","119","100","2024-11-01","3170"),
("3142","118","100","2024-10-31","3171"),
("3143","653","100","2024-11-05","3172"),
("3144","654","100","2024-11-05","3173"),
("3145","393","100","2024-11-05","3174"),
("3146","395","100","2024-11-05","3175"),
("3147","555","100","2024-11-01","3176"),
("3148","121","100","2024-11-05","3177"),
("3149","401","100","2024-11-05","3178"),
("3150","564","100","2024-11-05","3179"),
("3151","245","100","2024-11-05","3180"),
("3152","254","100","2024-11-05","3181"),
("3154","421","100","2024-11-05","3183"),
("3155","424","100","2024-11-05","3184"),
("3156","425","100","2024-11-05","3185"),
("3157","424","100","2024-11-05","3186"),
("3158","427","100","2024-11-05","3187"),
("3159","601","100","2024-11-06","3188"),
("3160","612","100","2024-11-06","3189"),
("3161","665","100","2024-11-06","3190"),
("3162","661","100","2024-11-06","3191"),
("3163","662","100","2024-11-06","3192"),
("3164","663","100","2024-11-06","3193"),
("3165","664","100","2024-11-06","3194"),
("3166","257","100","2024-11-06","3195"),
("3167","429","100","2024-11-05","3196"),
("3168","432","100","2024-11-07","3197"),
("3169","433","100","2024-11-07","3198"),
("3170","666","100","2024-11-07","3199"),
("3171","613","100","2024-11-07","3200"),
("3172","614","100","2024-11-07","3201"),
("3173","657","100","2024-11-05","3202"),
("3174","260","100","2024-11-08","3203"),
("3175","403","100","2024-11-05","3204"),
("3176","404","100","2024-11-05","3205"),
("3177","405","100","2024-11-05","3206"),
("3178","408","50","2024-10-22","3207"),
("3179","410","100","2024-11-06","3208"),
("3180","565","100","2024-11-05","3209"),
("3181","569","100","2024-11-06","3210"),
("3182","411","100","2024-11-06","3211");
INSERT INTO stud_pay VALUES
("3183","414","100","2024-11-07","3212"),
("3184","570","100","2024-11-06","3213"),
("3185","126","100","2024-11-06","3214"),
("3186","127","100","2024-11-06","3215"),
("3187","128","100","2024-11-05","3216"),
("3188","129","100","2024-11-03","3217"),
("3189","130","100","2024-11-06","3218"),
("3190","131","100","2024-11-28","3219"),
("3191","133","100","2024-11-06","3220"),
("3192","134","100","2024-11-06","3221");




CREATE TABLE `stud_tb` (
  `id` int(100) NOT NULL AUTO_INCREMENT,
  `id_note` int(100) NOT NULL,
  `userID` int(100) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `age` varchar(100) NOT NULL,
  `sex` varchar(100) NOT NULL,
  `catg` varchar(100) NOT NULL,
  `datein` date NOT NULL,
  `p_name` varchar(250) DEFAULT NULL,
  `p_phone` varchar(250) NOT NULL,
  `loc` varchar(250) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `userID` (`userID`),
  CONSTRAINT `stud_tb_ibfk_1` FOREIGN KEY (`userID`) REFERENCES `users_tb` (`id_user`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3222 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


INSERT INTO stud_tb VALUES
("369","36","6","نبا محمد","2","انثى","حضانة","2024-09-10","محمد","00000000000","بلوك2"),
("391","58","6","رزان مهدي","4","انثى","تمهيدي","2024-09-03","مهدي","00000000000","بلوك2"),
("392","59","6","علي مهدي","2","ذكر","حضانة","2024-10-06","مهدي","00000000000","بلوك2"),
("398","65","6","ايهم ضرغام","5","ذكر","تمهيدي","2024-08-04","ضرغام","00000000000","بلوك2"),
("414","81","6","عباس اسامة","3","ذكر","تحضيري","2024-09-29","اسامه","00000000000","بلوك2"),
("415","82","6","حمزه اسامة","4","ذكر","روضة","2024-09-29","اسامة","00000000000","بلوك2"),
("436","103","7","رهف محمود","4","انثى","تمهيدي","2024-09-25","محمود","00000000000","بلوك3"),
("446","113","7","جمانه جاسم","2","انثى","روضة","2024-09-25","جاسم","00000000000","بلوك3"),
("447","114","7","علي نورس","5","ذكر","تمهيدي","2024-09-06","نورس","00000000000","بلوك3"),
("461","128","7","عمار ياسر","4","ذكر","روضة","2024-09-29","ياسر","00000000000","بلوك3"),
("467","134","7","جود طالب","4","انثى","روضة","2024-09-04","طالب","00000000000","بلوك3"),
("474","141","7","زين مصطفى حامد","2","ذكر","تحضيري","2024-09-08","مصطفى حامد","00000000000","بلوك3"),
("548","215","8","امير محمد","4","ذكر","روضة","2024-09-06","محمد","00000000000","بلوك4"),
("662","329","11","احمد منير","2","ذكر","حضانة","2024-09-25","منير","00000000000","بلوك7"),
("690","357","11","يحى وائل","4","ذكر","روضة","2024-10-06","وئل","00000000000","بلوك7"),
("691","358","11","غيث وائل","2","ذكر","حضانة","2024-10-06","وئل","00000000000","بلوك7"),
("719","386","10","امير علي عبد الكريم ","3","ذكر","تحضيري","2024-09-13","علي عبد الكريم","00000000000","بلوك6"),
("738","405","10","تاليا حسن علي","2","انثى","روضة","2024-10-01","حسن علي","00000000000","بلوك6"),
("748","415","10","ادم مصطفى سلام","5","ذكر","تمهيدي","2024-09-23","مصطفى سلام","00000000000","بلوك6"),
("758","425","10","علي احمد جلوب ","3","انثى","تحضيري","2024-09-06","احمد جلوب","00000000000","بلوك6"),
("762","429","10","لارا اسعد حامد ","2","انثى","روضة","2024-09-17","اسعد حامد","00000000000","بلوك6"),
("763","430","10","يوسف اسعد حامد","3","ذكر","روضة","2024-09-17","اسعد حامد","00000000000","بلوك6"),
("782","449","10","رهف ناضر جلوب","5","انثى","تمهيدي","2024-09-06","ناضر جلوب","00000000000","بلوك6"),
("783","450","10","فهد ناضر جلوب","2","ذكر","حضانة","2024-10-06","ناضر جلوب","00000000000","بلوك6"),
("790","457","10","ادم علي محمود","5","ذكر","تمهيدي","2024-09-11","علي محمود","00000000000","بلوك6"),
("791","458","10","در علي محمود","3","انثى","روضة","2024-09-11","علي محمود","00000000000","بلوك6"),
("794","461","10","كرم مصطفى سلام","6","ذكر","تمهيدي","2024-09-23","مصطفى سلام","00000000000","بلوك6"),
("797","464","10","لارين حسام سماعيل","4","انثى","تمهيدي","2024-09-20","حسام سماعيل","00000000000","بلوك6"),
("798","465","10","ريتال مضر خالد","2","انثى","تحضيري","2024-09-24"," مضر خالد","00000000000","بلوك6"),
("842","508","10","يمامة زيد مجيد","2","انثى","تحضيري","2024-09-22","زيد مجيد","07713795524","A6"),
("862","528","12","ملك محمد","4","انثى","روضة","2024-09-29","محمد","00000000000","A8"),
("874","540","6","جود حارث محمد","4","ذكر","روضة","2024-09-26","0000000000","00000000000","0000"),
("875","541","6","بحر حارث محمد","2","انثى","حضانة","2024-09-26","0000000000","00000000000","0000"),
("912","578","12","احمد راشد","3","ذكر","تحضيري","2024-09-26","راشد","00000000000","A8"),
("950","616","7","مريم نصر الله ","5","انثى","تمهيدي","2024-08-23","نصر الله","00000000000","A2"),
("1020","686","8","ايلا احمد","3","انثى","روضة","2024-09-14","احمد","00000000000","A2"),
("1021","687","8","سيلا احمد","3","انثى","روضة","2024-09-14","احمد","00000000000","A2"),
("1031","697","7","امير احمد خيري ","5","ذكر","روضة","2024-09-03","احمد خيري","00000000000","A3"),
("1047","713","6","محمد ليث","4","ذكر","روضة","2024-09-16","ليث محمد","07704569507","A1 110 شقة 604"),
("1048","714","6","مريم ليث","3","انثى","تحضيري","2024-09-16","ليث محمد","07704569507","A9 905 شقة 501"),
("1052","718","6","فاطمة احمد","54","انثى","تمهيدي","2024-09-15","0000000000","00000000000","0000"),
("1106","772","12","شهم علي حمزه","3","ذكر","روضة","2024-09-29","علي","00000000000","A8"),
("1128","794","6","علي احمد","5","ذكر","تمهيدي","2024-09-15","0000000000","00000000000","0000"),
("1182","848","6","تيم محمد","4","ذكر","روضة","2024-10-06","تيم محمد رعد","00000000000","A2"),
("1196","862","6","ابراهيم علي المرتضى","3","ذكر","تحضيري","2024-09-29","علي المرتضى","00000000000","A2"),
("1218","884","10","الياس محمد خير الله","4","ذكر","تمهيدي","2024-09-26","محمد خير الله","07748028759","A1"),
("1230","896","11","مينا ضيف","2","انثى","حضانة","2024-09-08","ضيف احمد","000000000","A7"),
("1277","943","12","انمار رافت","4","ذكر","روضة","2024-09-10","رافت","00000000000","A8"),
("1305","971","11","دره اسامه","4","انثى","تمهيدي","2024-09-05","اسامه احمد علي","07703306921","A7"),
("1306","972","11","ماسه اسامه احمد","4","انثى","تمهيدي","2024-09-05","اسامه احمد  علي","0773306921","A7"),
("1318","984","6","فهد علي عصام","4","ذكر","روضة","2024-10-01","0000000000","00000000000","0000"),
("1319","985","6","لانا محمد سالم","4","انثى","روضة","2024-10-02","0000000000","00000000000","0000"),
("1320","986","6","جنى محمد سالم","4","انثى","روضة","2024-10-02","0000000000","00000000000","0000"),
("1328","994","11","ليان ايهاب","2","انثى","حضانة","2024-09-30","ايهاب  قاسم جبار","07721824826","A7"),
("1377","1043","8","يمان احمد","2","ذكر","حضانة","2024-09-24","احمد","00000000000","a4"),
("1404","1070","12","الحسن محمد عبدالكريم","1","ذكر","حضانة","2024-09-16","محمد","00000000000","A8"),
("1407","1073","12","جود سامر حميد","3","ذكر","روضة","2024-10-06","سامر","00000000000","A8"),
("1442","1108","10","سيف حمزة جاسم ","1","ذكر","حضانة","2024-09-25","حمزة جاسم","07738324981","A9"),
("1459","1125","8","ادم","1","ذكر","حضانة","2024-09-04","هادي","00000000000","3"),
("1478","1144","9","لافينيا نورس","4","انثى","روضة","2024-09-22","نورس رياض ","00000000000","703"),
("1484","1150","7","محمد صفاء حسين احمد","-5","ذكر","تمهيدي","2024-10-06","صفاء حسين احمد","00000000","B2"),
("1485","1151","7","ايهم مشتاق طالب ","3","ذكر","تمهيدي","2024-10-04","مشتاق طالب ","00000000000","B3"),
("1507","1173","6","ليان ضرغام","2","انثى","حضانة","2024-08-04","0000000000","00000000000","0000"),
("1518","1184","12","سلا زيد","4","انثى","تمهيدي","2024-09-19","زيد","00000000000","A8"),
("1554","1220","8","يوسف","4","ذكر","روضة","2024-09-23","مهند","00000000000","A8"),
("1570","1236","7","قمر احمد ملاذ","-5","انثى","تمهيدي","2024-08-23","احمد ملاذ","00000000000","B2"),
("1591","1257","10","سيف علي عبد  الكريم","1","ذكر","حضانة","2024-09-13","علي عبد الكريم","00000000000","A1"),
("1593","1259","10","ايمن مهند مظهر","4","ذكر","تمهيدي","2024-10-01","مهند مظهر","00000000000","A1"),
("1617","1283","10","مصطفى ضياء مصطفى","1","ذكر","حضانة","2024-09-22","ضياء مصطفى","00000000000","A6"),
("1618","1284","10","مهيمن ضياء مصطفى","1","ذكر","حضانة","2024-09-22","ضياء مصطفى","00000000000","A6"),
("1619","1285","10","لارين ضياء مصطفى","5","انثى","تمهيدي","2024-09-22","ضياء مصطفى","00000000000","A6"),
("1621","1287","10","تيم محمد عادل","4","ذكر","تمهيدي","2024-09-10","محمد عادل","00000000000","A6"),
("1623","1289","10","ايليا سيف تركي","4","ذكر","تمهيدي","2024-09-08","سيف تركي","00000000000","A6"),
("1625","1291","10","ايلين هيثم هلال","3","انثى","روضة","2024-09-10","هيثم هلال","00000000000","A6"),
("1630","1296","6","حسن ايمن","4","ذكر","روضة","2024-10-08","0000000000","00000000000","0000"),
("1645","1311","7","علي ماجد علي جاسم","-2","ذكر","حضانة","2024-09-21","ماجد علي جاسم","00000000000","A3"),
("1652","1318","7","ليان احمد","-4","انثى","روضة","2024-08-06","احمد","00000000000","A3"),
("1668","1334","10","اجود مهند ناصر","3","ذكر","روضة","2024-09-13","مهند ناصر","00000000000","B1"),
("1697","1363","8","علي حيدر","5","ذكر","تمهيدي","2024-10-04","حيدر","00000000000","A8"),
("1699","1365","8","محمد فراس صادق","4","ذكر","روضة","2024-09-25","فراس","00000000000","a4"),
("1727","1393","10","محمد احمد كاظم ","3","ذكر","روضة","2024-08-23","احمد كاظم","00000000000","A2"),
("1728","1394","10","علي احمد كاظم","3","ذكر","روضة","2024-08-23","احمد كاظم","00000000000","A2"),
("1750","1416","6","يزن احمد عصام","2","ذكر","حضانة","2024-10-01","0000000000","00000000000","0000"),
("1781","1447","7","علي حسين هاشم ","3","ذكر","روضة","2024-09-10","حسين هاشم ","00000000000","A3"),
("1835","1501","6","سراج احمد","5","ذكر","تمهيدي","2024-09-02","0000000000","00000000000","0000"),
("1893","1559","9","دانه عمار اسعد","4","انثى","روضة","2024-09-28","عمار  اسعد ","00000000000","بلوك5"),
("1899","1565","11","ضي وسام","2","انثى","حضانة","2024-10-02","وسام","00000000000","A7"),
("1907","1573","11","محمد ايهاب","4","ذكر","روضة","2024-09-30","ايهاب","00000000000","A7"),
("1929","1595","11","عباس رسول رحمن","4","ذكر","روضة","2024-09-26","رسول رحمن","00000000000","A7"),
("1930","1596","11","جواد رسول رحمان","4","ذكر","تمهيدي","2024-09-26","رسول رحمن","","A7"),
("1933","1599","12","محمد مناف جواد","4","ذكر","روضة","2024-10-08","مناف","00000000000","A8"),
("1965","1631","12","منسه محمد مهدي","4","انثى","روضة","2024-10-06","محمد","00000000000","A8"),
("1980","1646","9","فضل الله سرمد","4","ذكر","روضة","2024-09-07","الله","00000000000","400"),
("2053","1719","12","رحمه  محمد مهدي","1","انثى","حضانة","2024-10-06","محمد","00000000000","A8"),
("2067","1733","11","فضل محمد قاسم","3","ذكر","حضانة","2024-09-30","محمد قاسم","","A7"),
("2068","1734","12","حيدر احمد ناظم","2","ذكر","تحضيري","2024-10-01","احمد","00000000000","A8"),
("2092","1758","10","ديما موسى مهدي","3","انثى","روضة","2024-09-06","موسى مهدي","00000000000","A7"),
("2101","1767","8","مياسين","3","انثى","تحضيري","2024-09-15","احمد","00000000000","a4"),
("2105","1771","12","جبل عبدالقادر","1","ذكر","حضانة","2024-09-29","عبد القادر","00000000000","A8"),
("2117","1783","6","لارين مصطفى حيدر","2","انثى","حضانة","2024-08-22","0000000000","00000000000","0000");
INSERT INTO stud_tb VALUES
("2125","1791","12","عبدالله محمد هادي","4","ذكر","روضة","2024-10-03","محمد","00000000000","A8"),
("2159","1825","11","فهد عمر احسان4","4","ذكر","روضة","2024-09-02","عمر احسان","","A7"),
("2166","1832","6","لافا نوزاد","1","انثى","حضانة","2024-09-17","0000000000","00000000000","0000"),
("2192","1858","12","هناء مصطفى","5","انثى","تمهيدي","2024-09-01","مصطفى","00000000000","A8"),
("2231","1897","6","علي  دريد","5","ذكر","تمهيدي","2024-09-04","0000000000","00000000000","0000"),
("2234","1899","7","حسن حيدرمحمود","5","ذكر","تمهيدي","2024-09-30","حيدرمحمود","00000000000","A3"),
("2235","1900","7","اية حيدرمحمود","1","انثى","حضانة","2024-09-30","حسن محمود","00000000000","A3"),
("2258","1923","10","ايه احمد فرج","1","انثى","حضانة","2024-09-10","احمد فرج","07726717217","A9"),
("2264","1929","7","علي حيدر حسين","3","ذكر","تحضيري","2024-10-08","حيدر حسين","00000000000","A3"),
("2265","1930","7","يوسف حيدر حسين عبد الله","3","ذكر","تحضيري","2024-10-08","حيدر حسين","00000000000","A3"),
("2269","1934","7","مريم روكان","4","انثى","تمهيدي","2024-10-01","روكان","00000000000","A3"),
("2277","1942","9","مصطفى سلمان","4","ذكر","روضة","2024-10-08","سلمان","00000000000","بلوك5"),
("2278","1943","9","تيم علي عصام","2","ذكر","حضانة","2024-09-24","علي ","00000000000","بلوك5"),
("2283","1948","7","مريم عدنان","1","انثى","حضانة","2024-09-26","عدنان","00000000000","A3"),
("2284","1949","7","رضا عدنان","4","ذكر","روضة","2024-09-26","عدنان","00000000000","A3"),
("2287","1952","7","جلنارعبد الحميد","4","انثى","تمهيدي","2024-09-16","عبد الحميد","00000000000","A3"),
("2290","1955","7","اصالة حسام","4","انثى","تمهيدي","2024-10-05","حسام","00000000000","A3"),
("2303","1968","10","ميلا حسين رزوقي","3","انثى","روضة","2024-10-02","حسين رزوقي","00000000000","B1"),
("2308","1973","7","الياس محمد فخري ","2","ذكر","حضانة","2024-09-08","محمد فخري سلمان ","07729918355","A3"),
("2328","1993","7","علي حيدر علي ","5","ذكر","تمهيدي","2024-09-30"," حيدر علي  ","07717194171","A3"),
("2329","1994","7","ميس حيدر علي 2","-1","انثى","حضانة","2024-09-30","حيدر  علي ","07717194171","A3"),
("2335","2000","10","ياسين مصطفى محمد ","2","ذكر","تحضيري","2024-09-04","مصطفى محمد","00000000000","A6"),
("2336","2001","10","زهراء مصطفى محمد","2","انثى","تحضيري","2024-09-04","مصطفى محمد","00000000000","A6"),
("2338","2003","11","ريتال علي","1","انثى","حضانة","2024-09-29","علي عماد ","","A7"),
("2345","2010","7","مسك سلال جبار ","1","انثى","حضانة","2024-09-05","سلال جبار","07810395091","a7 405 11"),
("2346","2011","7","مريم سلال جبار","-5","انثى","تمهيدي","2024-09-05","سلال جبار","07810395082","a7"),
("2359","2024","8","زينب احمد","5","انثى","تمهيدي","2024-09-15","احمد","00000000000","a4"),
("2377","2042","6","ادهم حسام جاسم","1","ذكر","حضانة","2024-10-06","حسام جاسم عودة","07704591910","A2 211"),
("2378","2043","8","علي الاكبر عادل","1","ذكر","حضانة","2024-09-15","عادل","","a4"),
("2379","2044","8","ريتال عادل","1","انثى","حضانة","2024-09-15","عادل","","a4"),
("2383","2048","6","علي مرتضى وسام","3","ذكر","تحضيري","2024-09-16","مرتضى وسام","07736770640","A2"),
("2385","2050","6","زين العابدين وسام عادل","4","ذكر","روضة","2024-10-06","وسام عادل عبد الرزاق","07713832360","A1 106"),
("2394","2059","8"," علي احمد هاشم","1","ذكر","حضانة","2024-09-01","احمد","","A7"),
("2401","2066","7","تمارا علي حسين ","5","انثى","تمهيدي","2024-08-23","علي حسين","07712296515","A3"),
("2404","2069","9","الياس مروان","2","ذكر","حضانة","2024-10-01","مروان","00000000000","بلوك5"),
("2405","2070","9","مجتبى مروان","2","ذكر","حضانة","2024-10-01","مروان","00000000000","بلوك5"),
("2420","2085","10","ابهر مهند ناصر","1","ذكر","حضانة","2024-09-16","مهند ناصر","00000000000","B1"),
("2422","2087","10","محمد عمر فلاح","3","ذكر","روضة","2024-09-16","عمر فلاح","00000000000","A8"),
("2423","2088","10","ايلينا عمر فلاح","3","انثى","روضة","2024-09-16","عمر فلاح","00000000000","A8"),
("2433","2098","11","عباس علي عماد","5","ذكر","تمهيدي","2024-09-29","علي عماد","00000000000","a7"),
("2443","2108","6","دانيال كرارحيدر","2","ذكر","حضانة","2024-09-16","0000000000","07730188852","A1"),
("2445","2110","9","سالي قاسم","3","انثى","حضانة","2024-09-22","قاسم","00000000000","بلوك5"),
("2460","2125","12","يمان علي","4","ذكر","روضة","2024-10-01","علي","00000000000","A8"),
("2463","2128","12","تيم سعيد","5","ذكر","تمهيدي","2024-10-02","سعيد","00000000000","A8"),
("2464","2129","12","الما سعيد","2","انثى","حضانة","2024-10-02","سعيد","00000000000","A8"),
("2467","2132","12","ميلا عصام","4","انثى","روضة","2024-09-02","عصام","00000000000","A8"),
("2468","2133","12","افنان ثائر","5","انثى","تمهيدي","2024-10-06","ثائر","00000000000","A8"),
("2470","2135","12","ادم حسام","4","ذكر","روضة","2024-10-06","حسام","00000000000","A8"),
("2471","2136","12","يوسف حسام","1","ذكر","حضانة","2024-10-06","حسام","00000000000","A8"),
("2473","2138","12","فهد محمد","4","ذكر","روضة","2024-09-10","محمد","00000000000","A8"),
("2480","2145","12","يزن غسان مطشر","4","ذكر","روضة","2024-09-12","غسان","00000000000","A8"),
("2481","2146","12","جنى راهب","2","انثى","حضانة","2024-09-09","راهب","00000000000","A8"),
("2483","2148","12","مريم حيدر كاظم","3","انثى","تحضيري","2024-10-02","حيدر","00000000000","A8"),
("2487","2152","12","انس علي","5","ذكر","تمهيدي","2024-09-10","علي","00000000000","A8"),
("2488","2153","12","ماس علي","3","انثى","تحضيري","2024-09-10","علي","00000000000","A8"),
("2491","2156","12","رضا محمد","4","ذكر","روضة","2024-10-01","محمد","00000000000","A8"),
("2505","2170","6","ادم مصطفى","3","ذكر","تحضيري","2024-09-24","0000000000","00000000000","A2"),
("2506","2171","6","محمد ماجد","3","ذكر","روضة","2024-09-23","0000000000","00000000000","0000"),
("2508","2173","10","ود سهيل نجم ","3","انثى","روضة","2024-09-23","سهيل نجم","00000000000","A6"),
("2514","2179","10","عمار ياسر جبار","4","ذكر","تمهيدي","2024-09-09","ياسر عمار","00000000000","B1"),
("2516","2181","10","ود صادق جعفر","4","انثى","روضة","2024-09-03","صادق جعفر","00000000000","A3"),
("2517","2182","10","زين العابدين صادق جعفر","3","ذكر","تحضيري","2024-09-03","صادق جعفر","00000000000","A8"),
("2521","2186","10","اوس زيد عبد  الحسين","4","ذكر","روضة","2024-09-16","زيد عبد الحسين","00000000000","B3"),
("2522","2187","10","روز سهيل نجم","1","انثى","حضانة","2024-09-23","سهيل نجم","00000000000","A7"),
("2525","2190","9","تاج الدين عماد","4","ذكر","روضة","2024-09-27","عماد","00000000000","بلوك5"),
("2529","2194","9","محمدرضا احمد ","3","ذكر","حضانة","2024-10-02","احمد","00000000000","900"),
("2535","2200","11","ايلان احمد","3","ذكر","حضانة","2024-10-06","احمد طارق","","A7"),
("2536","2201","10","جود عدي فاضل","3","ذكر","تحضيري","2024-08-08","عدي فاضل","00000000000","B1"),
("2540","2205","7","دانية رسول ","3","انثى","روضة","2024-10-02","رسول ","00000000000","A3"),
("2546","2211","7","كيان ضرغام اسماعيل ","6","ذكر","تمهيدي","2024-08-05","ضرغام اسماعيل","00000000000","A3"),
("2551","2216","6","بان محمد","5","انثى","تمهيدي","2024-09-02","0000000000","00000000000","0000"),
("2552","2217","6","جوان محمد","5","انثى","تمهيدي","2024-09-02","0000000000","00000000000","0000"),
("2554","2219","10","شغف حسين علاء ","1","انثى","حضانة","2024-10-06","حسين علاء","00000000000","A3"),
("2555","2220","10","ناي حسين علاء","4","انثى","تمهيدي","2024-10-06","حسين علاء","00000000000","A3"),
("2558","2223","10","نازا مصطفى فاضل ","1","انثى","حضانة","2024-08-07","مصطفى فاضل","07731210018","A2"),
("2567","2232","6","جوري مصطفى ","3","انثى","روضة","2024-10-08","0000000000","00000000000","0000"),
("2568","2233","6","جود مصطفى","2","ذكر","حضانة","2024-10-08","0000000000","00000000000","0000"),
("2572","2237","12","ايهم حسين ","3","ذكر","روضة","2024-10-01","حسين","00000000000","A8"),
("2573","2238","12","ادم حسين","1","ذكر","حضانة","2024-10-01","حسين","00000000000","A8"),
("2578","2243","12","امير انمار ","3","ذكر","تحضيري","2024-09-15","انمار","00000000000","A8"),
("2583","2248","6","كرم محمد فوزي","4","ذكر","روضة","2024-10-06","محمد فوزي","00000000000","000"),
("2586","2251","11","محمد فلاح","4","ذكر","روضة","2024-10-01","فلاح سالم","","A7"),
("2592","2257","6","نايا وائل","4","انثى","روضة","2024-09-26","وائل","","0000"),
("2593","2258","6","ادم وائل","1","ذكر","حضانة","2024-09-26","وائل","00000000000","000"),
("2594","2259","7","اسر احمد خيري","2","ذكر","حضانة","2024-09-29","احمد بلاسم","00000000000","A3"),
("2596","2261","8","ايلين","4","انثى","تحضيري","2024-08-30","اثير","00000000000","a4"),
("2600","2265","6","الياس مرتضى شاكر","2","ذكر","حضانة","2024-09-25","0000000000","00000000000","A2"),
("2601","2266","6","يحيى محمد","2","ذكر","حضانة","2024-10-06","0000000000","00000000000","A2"),
("2604","2269","6","حسن احمد باقر","1","ذكر","حضانة","2024-10-01","0000000000","00000000000","A2"),
("2606","2271","8","جود سلام ","6","ذكر","تمهيدي","2024-09-22","سلام عصام احمد ","00000000000","510/5/3"),
("2607","2272","6","يوسف معد نزار","4","ذكر","روضة","2024-10-02","0000000000","00000000000","A2"),
("2608","2273","6","شمس معد نزار","2","انثى","حضانة","2024-10-02","معد نزار","00000000000","A2"),
("2609","2274","9","عبد اللة سرمد","5","ذكر","تمهيدي","2024-09-07","سرمد","00000000000","5"),
("2610","2275","6","سراج منير","5","ذكر","تمهيدي","2024-08-26","0000000000","00000000000","A2"),
("2611","2276","10","ديمه سرمد نجم","4","انثى","تمهيدي","2024-09-16","سرمد نجم","00000000000","A6"),
("2614","2279","9","لجين علي ساهر ","7","انثى","تمهيدي","2024-09-24","علي ","00000000000","بلوك5"),
("2618","2282","7","حيدر محمد فالح ","5","ذكر","تمهيدي","2024-08-02","محمد فالح خير الله ","07734942196","904A/ع 405"),
("2621","2285","12","علي حسام","4","ذكر","روضة","2024-10-10","حسام","00000000000","A8"),
("2622","2286","8","ماريا","1","انثى","حضانة","2024-10-04","محمد موفق","07723821320","B3"),
("2623","2287","8","ريتال","3","انثى","تحضيري","2024-10-04","محمد موفق","07723821320","B3");
INSERT INTO stud_tb VALUES
("2625","2289","9","سديم محمد","3","انثى","حضانة","2024-10-06","محمد","00000000000","بلوك5"),
("2626","2290","10","صادق جعفر يوسف","2","ذكر","تحضيري","2024-10-02","جعفر يوسف ","00000000000","A6"),
("2628","2292","12","نور حميد","3","انثى","روضة","2024-09-03","حميد","00000000000","A8"),
("2629","2293","12","يقين حميد","5","انثى","تمهيدي","2024-09-03","حميد","00000000000","A8"),
("2630","2294","9","الياس اسامة","4","ذكر","روضة","2024-09-08","اسامة","00000000000","5"),
("2632","2296","9","اديم محمد","3","ذكر","حضانة","2024-10-06","محمد","00000000000","بلوك5"),
("2633","2297","12","سلطان هاشم","3","ذكر","تحضيري","2024-10-05","هاشم","00000000000","A8"),
("2638","2302","7","رزان مرتضى جاسم","5","انثى","روضة","2024-09-08","مرتضى جاسم","07715278070","a3-312"),
("2641","2305","11","امير عادل","5","ذكر","تمهيدي","2024-10-06","عادل","000000000","A7"),
("2649","2313","12","عبدالله معمر","4","ذكر","روضة","2024-09-22","معمر","00000000000","A8"),
("2652","2316","12","عبدالله حسين","3","ذكر","تحضيري","2024-10-01","حسين","00000000000","A8"),
("2655","2319","12","حسين غزوان","4","ذكر","تحضيري","2024-09-18","غزوان","00000000000","A8"),
("2657","2321","9","رقية سليم ","4","انثى","روضة","2024-09-24","سليم","00000000000","بلوك5"),
("2661","2325","7","ليان عبد عون جابر","1","انثى","حضانة","2024-09-24","عبد عون جابر ","","A3"),
("2662","2326","7","روز عبد عون جابر","1","انثى","حضانة","2024-09-24","عبد عون جابر","","A3"),
("2665","2329","7","فاطمه حسين علاء","4","انثى","روضة","2024-09-29"," حسين علاء","07732908184","A4404"),
("2674","2338","12","كرم حيدر","4","ذكر","روضة","2024-09-23","حيدر علي","00000000000","A8"),
("2675","2339","12","كارمن حيدر","4","انثى","روضة","2024-09-23","حيدر","00000000000","A8"),
("2676","2340","6","يحيى عباس خليل","5","ذكر","روضة","2024-09-01","0000000000","00000000000","A2"),
("2677","2341","9","مصطفى حسن","4","ذكر","روضة","2024-10-01","حسن","00000000000","A5"),
("2678","2342","6","الر نصير الدين","4","انثى","روضة","2024-10-06","0000000000","00000000000","A2"),
("2680","2344","7","زين الدين امين","3","ذكر","تحضيري","2024-08-02","امين جواد","07700828723","A1         104"),
("2682","2346","7","ايلا عبد القادر","2","انثى","حضانة","2024-09-03","عبد القادر عدنان","07722665828","B1"),
("2686","2350","6","زين رغيد سالم","1","ذكر","حضانة","2024-09-17","0000000000","00000000000","0000"),
("2687","2351","6","كرار حيدر سعد","2","ذكر","حضانة","2024-09-11","0000000000","00000000000","0000"),
("2689","2353","11","ديما صباح","6","انثى","تمهيدي","2024-09-08","صباح ثامر","","A7"),
("2690","2354","11","جنى صباح","4","انثى","روضة","2024-10-07","صباح ثامر","","A7"),
("2692","2356","10","الياس محمد رزاق","5","ذكر","تمهيدي","2024-09-01","محمد رزاق","07771286040","A6"),
("2693","2357","10","يوسف محمد رزاق ","5","ذكر","تمهيدي","2024-09-01","يوسف محمد رزاق","07771286040","A6"),
("2695","2359","9","حيدر علي ","4","ذكر","روضة","2024-09-22","علي ","00000000000","بلوك5"),
("2696","2360","9","ادهم علاء ","2","ذكر","حضانة","2024-09-08","علاء","00000000000","بلوك5"),
("2698","2362","11","ود نبراس","2","انثى","حضانة","2024-08-13","نبراس","000000000","A7"),
("2699","2363","12","عباس تقي","5","ذكر","تمهيدي","2024-09-17","تقي","00000000000","A8"),
("2700","2364","7","منتظر حيدر","1","ذكر","حضانة","2024-09-14","حيدر غلام","07707139016","A3308"),
("2701","2365","10","حسن محمد سامي","2021","ذكر","تحضيري","2024-09-12","محمد سامي","07817122289","A6"),
("2703","2367","8","مرتضى كاظم","4","ذكر","روضة","2024-10-02","كاظم معجن ","07512242334","a4"),
("2705","2369","6","لحسن يحيى","4","ذكر","روضة","2024-10-06","0000000000","00000000000","0000"),
("2709","2373","10","حسين احمد علي","3","ذكر","تحضيري","2024-08-16","احمد علي","07712281027","A6"),
("2710","2374","6","ادم احمد وائل","2","ذكر","حضانة","2024-09-18","احمد وائل","00000000000","A2"),
("2711","2375","12","ارام نورس علي ","3","ذكر","تحضيري","2024-09-16","نورس","00000000000","A8"),
("2712","2376","12","رقيه  مناف","5","ذكر","تمهيدي","2024-09-26","مناف","00000000000","A8"),
("2713","2377","12","علي مناف","4","ذكر","روضة","2024-09-26","مناف","00000000000","A8"),
("2718","2382","10","سمر قند علاء مهدي","2020","انثى","روضة","2024-09-21","علاء مهدي سلمان ","07712447557","A1"),
("2719","2383","10","لارين محمدصلاح","2023","انثى","حضانة","2024-09-21","محمد صلاح عبد","07856614764","a5"),
("2722","2386","10","ايهم منتظر هادي","1","ذكر","حضانة","2024-10-01","منتظر هادي","07724536819","A7"),
("2723","2387","11","حسين ثامر","4","ذكر","روضة","2024-09-25","ثامر","","A7"),
("2724","2388","11","ضحى ثامر","1","انثى","حضانة","2024-09-25","ثامر","","A7"),
("2725","2389","10","درة عبدالله","2020","انثى","تمهيدي","2024-09-24","عبدالله رعد","07711733446","a7"),
("2731","2395","12","عباس ايسر","5","ذكر","تمهيدي","2024-10-01","ايسر","00000000000","A8"),
("2732","2396","12","شمس محمد","3","انثى","تحضيري","2024-09-03","محمد","00000000000","A8"),
("2733","2397","9","فاطمة جعفر","3","انثى","حضانة","2024-09-25","جعفر","00000000000","بلوك5"),
("2735","2399","7"," لارين انمار","2","انثى","تحضيري","2024-09-30","انمار","07749891245","A3"),
("2737","2401","12","ابو الفضل جعفر","3","ذكر","تحضيري","2024-10-01","جعفر","00000000000","A8"),
("2739","2403","12","روز زيد غزوان","4","انثى","روضة","2024-10-01","زيد","00000000000","A8"),
("2741","2405","7","جمانه احمد","4","انثى","روضة","2024-10-02","احمد","07723372462","A3"),
("2743","2407","10","جاد مصطفى طارق","2021","ذكر","حضانة","2024-09-03","مصطفى طارق","07712084345","a4"),
("2744","2408","8","جود علي هيثم ","3","ذكر","تحضيري","2024-09-03","علي هيثم ","07733896626","a4"),
("2745","2409","9","تميم سيف ","3","ذكر","حضانة","2024-10-06","سيف","00000000000","بلوك5"),
("2747","2411","7","اسينات علي جعفر","4","انثى","تمهيدي","2024-10-06","علي جعفر","07703903582","A3"),
("2748","2412","6","غيث دريد ","4","ذكر","تمهيدي","2024-10-06","0000000000","00000000000","A2"),
("2749","2413","10","ياسين عمار ماجد","2021","ذكر","تمهيدي","2024-09-04","عمار ماجد","07712374927","A6"),
("2750","2414","10","علي محمد قاسم","2019","ذكر","روضة","2024-10-02","محمد قاسم ","","a2"),
("2752","2416","7","مريم مهند","5","انثى","تمهيدي","2024-08-04","مهند","07730719213","A3"),
("2754","2418","6","سيلين حسام","4","انثى","روضة","2024-10-08","0000000000","00000000000","0000"),
("2755","2419","8","احمد فلاح ","5","ذكر","تمهيدي","2024-10-05","فلاح حسن ","07715630246","a4"),
("2756","2420","7","زين العابدين محمد ","7","ذكر","تمهيدي","2024-08-06","محمد","07747921328","A3"),
("2757","2421","7","قمر محمد","4","انثى","روضة","2024-08-06","محمد","07747921328","A3"),
("2759","2423","10","محمد سجاد عقيل","2022","ذكر","تحضيري","2024-08-06","سجاد عقيل","0","A6"),
("2760","2424","10","عباس هشام صالح","2018","ذكر","تمهيدي","2024-08-07","هشام صالح","07708418025","A7"),
("2761","2425","10","محمد هشام صالح","2020","ذكر","روضة","2024-09-06","هشام صالح","07744457859","A7"),
("2762","2426","12","اسر هاني عقيل ","5","ذكر","روضة","2024-10-08","هاني","00000000000","A8"),
("2764","2428","6","فهد عبدالله","4","ذكر","روضة","2024-09-23","0000000000","00000000000","A2"),
("2765","2429","7","امير احمد صكر","4","ذكر","روضة","2024-09-10","احمد صكر","07733987049","A3"),
("2766","2430","12","نايا طلال عبد الحميد","4","انثى","روضة","2024-09-12","طلال","00000000000","A8"),
("2767","2431","6","همام محمود","4","ذكر","تحضيري","2024-09-15","0000000000","00000000000","0000"),
("2768","2432","7"," ازل احمد صكر","2","انثى","حضانة","2024-10-06","احمد صكر","00000000000","A3"),
("2769","2433","7","فضل علي سجاد","1","ذكر","حضانة","2024-08-13","علي سجاد","07709447721","A3"),
("2770","2434","9","ديما محمد","3","انثى","حضانة","2024-09-15","محمد","00000000000","بلوك5"),
("2771","2435","12","جود احمد ريشان","2","ذكر","حضانة","2024-09-14","احمد","00000000000","A8"),
("2772","2436","12","شهم احمد","5","ذكر","تمهيدي","2024-09-16","احمد","00000000000","A8"),
("2774","2438","7","سولين سرمد","4","انثى","روضة","2024-09-15","سرمد","07731074401","A3"),
("2775","2439","7","لارين سرمد","3","انثى","تحضيري","2024-09-15","سرمد","07731074401","A3"),
("2776","2440","11","يوسف وليد","5","ذكر","تمهيدي","2024-08-15","وليد خالد ","000000000","A7"),
("2777","2441","11","رقيه وليد","4","انثى","روضة","2024-08-15","وليد خالد ","000000000","A7"),
("2778","2442","6","سيلين محمد","2","انثى","حضانة","2024-09-17","محمد سماعيل ","00000000000","A2"),
("2780","2444","9","نجاة عبد الجبار","3","انثى","حضانة","2024-09-18","عبد الجبار","00000000000","بلوك5"),
("2781","2445","9","زينب عبد الجبار","5","انثى","تحضيري","2024-09-18","عبد الجبار","00000000000","بلوك5"),
("2783","2447","9","جنى حيدر ","4","انثى","روضة","2024-09-18","حيدر","00000000000","بلوك5"),
("2784","2448","9","ابراهيم مصطفى","4","ذكر","روضة","2024-09-17","مصطفى ","00000000000","بلوك5"),
("2786","2450","10","يامن عباس محمود","2022","ذكر","حضانة","2024-08-21","عباس محمود","07713657700","B3"),
("2787","2451","7"," رحيق سمير خالد","3","انثى","حضانة","2024-09-29","سمير خالد","07700000000","A3"),
("2788","2452","9","نرجس زكريا","5","انثى","تمهيدي","2024-08-26","زكريا","00000000000","بلوك5"),
("2789","2453","6","مسرة بهاء طه","6","انثى","تمهيدي","2024-10-01","بهاء طه","07770188655","A2"),
("2790","2454","7","اسماعيل حسام","3","ذكر","تحضيري","2024-08-27"," حسام","07722303200","A2"),
("2791","2455","9","قمر مصطفى ","4","انثى","روضة","2024-09-25","مصطفى ","00000000000","بلوك5"),
("2792","2456","12","وسام علاء حسين","3","ذكر","تحضيري","2024-09-28","علاء","00000000000","A8"),
("2793","2457","9","اصف محمد","5","ذكر","تمهيدي","2024-09-25","محمد","00000000000","بلوك5"),
("2794","2458","7","رحمه اسامه ","5","انثى","تمهيدي","2024-10-01","اسامه علي ","07703440467","A3"),
("2795","2459","12","نيثن يوسف","2","ذكر","حضانة","2024-09-30","يوسف","00000000000","A8"),
("2797","2461","12","ليث داوود","4","ذكر","روضة","2024-10-01","داوود","00000000000","A8");
INSERT INTO stud_tb VALUES
("2798","2462","10","مريم حسن اكرم","4","انثى","روضة","2024-10-01","حسن اكرم","07714353996","A6"),
("2799","2463","12","حسن عصام","4","ذكر","روضة","2024-10-06","عصام","00000000000","A8"),
("2800","2464","10","مصطفى محمد احمد","2021","ذكر","تحضيري","2024-09-01","محمد احمد","07733497263","A6"),
("2801","2465","9","ادريس حمزة","4","ذكر","روضة","2024-10-01","حمزة","00000000000","بلوك5"),
("2802","2466","10","علي وضاح منصور","2019","ذكر","تمهيدي","2024-10-02","وضاح منصور","07710581197","A1"),
("2803","2467","12","وتين احمد سمير","4","ذكر","روضة","2024-10-06","احمد","00000000000","A8"),
("2804","2468","12","ادم عمر حازم","5","ذكر","تمهيدي","2024-10-02","عمر","00000000000","A8"),
("2806","2470","12","علي وليد احمد","5","ذكر","تمهيدي","2024-10-06","وليد","00000000000","A8"),
("2807","2471","10","سلطان محمود شاكر","2023","ذكر","حضانة","2024-10-02","محمود شاكر","07708861326","A7"),
("2808","2472","12","محمد اثير علي","5","ذكر","تمهيدي","2024-10-08","اثير ","00000000000","A8"),
("2809","2473","10","يمان احمد صلاح","2021","ذكر","تحضيري","2024-09-03","احمد صلاح ","07812290229","A3"),
("2810","2474","10","فاطمه اسامه علي","2022","انثى","حضانة","2024-10-03","اسامه علي","07717089848","b8"),
("2811","2475","9","غنى علي ","4","انثى","روضة","2024-10-06","علي ","00000000000","A8"),
("2812","2476","12","سما حيدر علي","4","انثى","روضة","2024-10-08","حيدر","00000000000","A8"),
("2813","2477","12","نفس مصطفى","3","انثى","تحضيري","2024-09-03","مصطفى","00000000000","A8"),
("2814","2478","12","امير اسامه مكي","5","ذكر","تمهيدي","2024-10-06","اسامه","00000000000","A8"),
("2815","2479","8","علي","3","ذكر","حضانة","2024-09-02","مصطفى","07813922560","A2"),
("2817","2481","8","ود","3","انثى","تحضيري","2024-10-01","علي","07702184000","A3"),
("2818","2482","8","مسك  ","3","انثى","تحضيري","2024-10-01","عبد الرحمن","07710294709","a4"),
("2819","2483","8","وتين ","3","انثى","تحضيري","2024-09-01","بارق","07717752997","a4"),
("2820","2484","8","ميسم","4","انثى","تمهيدي","2024-10-03","معمر","07708635480","B2"),
("2823","2487","8","يوسف","4","ذكر","روضة","2024-10-01","عبد الرحمن","07710294709","a4"),
("2824","2488","8","هبة الله","5","انثى","تمهيدي","2024-10-03","مصطفى","07744157666","a4"),
("2825","2489","8","محمد","5","ذكر","تمهيدي","2024-08-26","احمد","07716424737","A8"),
("2826","2490","9","طيبة محمود ","3","انثى","حضانة","2024-10-06","محمود","00000000000","بلوك5"),
("2827","2491","12","ريتال وسام","5","انثى","تمهيدي","2024-10-06","وسام","00000000000","A8"),
("2828","2492","12","ناي محمد ابراهيم","5","انثى","تمهيدي","2024-09-08","محمد","00000000000","A8"),
("2829","2493","12","ابراهيم محمد","5","ذكر","تمهيدي","2024-09-08","محمد","00000000000","A8"),
("2830","2494","6","شهم حسين","1","ذكر","حضانة","2024-10-08","حسين محمد","00000000000","A2"),
("2831","2495","6","هيا هشام زهير","5","انثى","تمهيدي","2024-10-08","هشام زهير","07703882464","A1"),
("2832","2496","11","احمد صفاء","5","ذكر","تمهيدي","2024-09-08","صفاء","","A7"),
("2834","2498","12","ديار علاء جيهاد","5","ذكر","تمهيدي","2024-09-08","علاء","00000000000","A8"),
("2835","2499","12","ناي علاء جيهاد","4","انثى","روضة","2024-09-08","علاء","00000000000","A8"),
("2836","2500","7"," ليان عباس محمود","5","انثى","تمهيدي","2024-10-08","عباس محمود","07801099201","A3"),
("2837","2501","7","علي الاكبر عمار","5","ذكر","تمهيدي","2024-09-08","عمار","07700000000","A3"),
("2838","2502","11","لارين ابراهيم","3","انثى","حضانة","2024-09-09","ابراهيم نجيب","","A7"),
("2839","2503","12","نايا محمدصفاء","4","انثى","روضة","2024-09-09","محمد","00000000000","A8"),
("2840","2504","10","ادم حيدر جواد","2019","ذكر","تمهيدي","2024-09-10","حيدر جواد","07702915057","A6"),
("2841","2505","10","جلنار حيدر جواد","2021","انثى","روضة","2024-09-10","حيدر جواد","07800957522","A6"),
("2842","2506","7","ياسين احمد","3","ذكر","تحضيري","2024-09-09","احمد  محسن","07702768908"," A8"),
("2843","2507","12","ميرال علي","2","انثى","حضانة","2024-09-10","علي","00000000000","A8"),
("2844","2508","12","كرار علي","5","ذكر","تمهيدي","2024-09-10","علي","00000000000","A8"),
("2845","2509","12","جود عدي فاضل","3","ذكر","تحضيري","2024-10-08","عدي","00000000000","A8"),
("2846","2510","12","عباس محمد قاسم","5","ذكر","تمهيدي","2024-09-11","محمد","00000000000","A8"),
("2847","2511","8"," مهيمن","4","ذكر","تحضيري","2024-09-11","محمد","07710076269","a4"),
("2848","2512","10","ميرال علاء ماهر ","2023","انثى","حضانة","2024-09-04","علاء ماهر ","07706209914","A1"),
("2849","2513","7","ياسمين محمد سعدي ","5","انثى","تمهيدي","2024-09-11","محمد سعدي ","07705308414","A3"),
("2850","2514","7","زيد سيف خلف","1","ذكر","حضانة","2024-09-11","سيف خلف","07713711711","A3"),
("2851","2515","12","ادم مصطفى","4","ذكر","روضة","2024-09-11","مصطفى","00000000000","A8"),
("2852","2516","12","مهيمن مصطفى","5","ذكر","تمهيدي","2024-09-11","مصطفى","00000000000","A8"),
("2854","2517","8","الياس","5","ذكر","روضة","2024-09-04","هادي","00000000000","a4"),
("2855","2518","10","اسد ثائر عادل","2020","ذكر","تحضيري","2024-09-11","ثائر عادل","07716314252","A6"),
("2856","2519","11","الحسن حسين","2","ذكر","حضانة","2024-09-12","حسين","","A7"),
("2857","2520","6","داينا سلام علاء","1","انثى","حضانة","2024-09-16","سلام علاء","07768472530","A2"),
("2858","2521","6","نور صلاح","4","انثى","روضة","2024-09-16","0000000000","00000000000","A2"),
("2859","2522","6","رقية علي","5","انثى","تمهيدي","2024-09-16","0000000000","00000000000","A2"),
("2860","2523","8"," ماسة","2","انثى","حضانة","2024-09-16","نورس","00000000000","a4"),
("2861","2524","9","مشكاة خضر ","2","انثى","حضانة","2024-09-16","خضر","00000000000","بلوك5"),
("2862","2525","9","فاطمه جواد","5","انثى","تمهيدي","2024-09-16","جواد","00000000000","بلوك5"),
("2863","2526","6","مريم نورس","1","انثى","حضانة","2024-09-16","0000000000","00000000000","0000"),
("2864","2527","8","هيا","4","انثى","روضة","2024-09-16","وهب عماد","07717282961","a4"),
("2865","2528","12","سدن عبدالله","3","انثى","تحضيري","2024-09-16","عبدالله","00000000000","A8"),
("2866","2529","12","افار حيدر","3","ذكر","تحضيري","2024-09-16","حيدر","00000000000","A8"),
("2867","2530","12","زين الدين فضاء","4","ذكر","روضة","2024-09-16","فضاء","00000000000","A8"),
("2868","2531","12","قمر احمد بشير","2","انثى","حضانة","2024-09-16","احمد","00000000000","A8"),
("2869","2532","12","ايليا مصطفى","3","انثى","تحضيري","2024-09-16","مصطفى","00000000000","A8"),
("2870","2533","7","شهم عبدالله خليل","5","ذكر","تمهيدي","2024-09-16","عبدالله خليل"," 0770000000","A3"),
("2871","2534","7","رهف فهد فضل","5","انثى","تمهيدي","2024-09-12","فهد فضل","07700000000","A3"),
("2872","2535","7","فاطمه تركي","3","انثى","تحضيري","2024-09-16","تركي","07700000000","A3"),
("2873","2536","7","اسحاق تركي","2","ذكر","حضانة","2024-09-16","تركي","07700000000","A3"),
("2874","2537","6","ناز مصطفى فاضل ","2","انثى","حضانة","2024-09-16","مصطفى فاضل","07731210018"," A2 بلوك "),
("2875","2538","6","احمد مصطفى ","4","ذكر","روضة","2024-09-16","0000000000","00000000000","A1"),
("2876","2539","6","احمد مصطفى ","4","ذكر","روضة","2024-09-16","مصطفى سعدي","0770870907","A1"),
("2877","2540","6","زهراء مصطفى ","4","انثى","روضة","2024-09-16","مصطفى سعدي","07710972540","A1"),
("2878","2541","8","شام","5","انثى","تمهيدي","2024-09-17","زيد","07760269038","a4"),
("2879","2542","6","ملاك  ذوالفغار علاء","5","انثى","تمهيدي","2024-09-16","ذوالفغار علاء","07737112111","A2"),
("2880","2543","8","علي","2","ذكر","حضانة","2024-09-17"," هشام جبار","07723204230","a4"),
("2881","2544","10"," رضا علي عبدالجبار","2019","ذكر","تمهيدي","2024-09-17","رضا عبدالجبار","07901177536","A6"),
("2882","2545","10","رامي مروان عبداللطيف","2020","ذكر","تمهيدي","2024-09-17","مروان عبداللطيف","07700814383","A6"),
("2883","2546","12","شموخ احمد","4","ذكر","روضة","2024-09-16","احمد","00000000000","A8"),
("2884","2547","6","ريتاج احمد جواد","4","انثى","روضة","2024-09-17","احمد جواد","07726968820","A6"),
("2885","2548","6","اديان حسام هاشم","5","انثى","تمهيدي","2024-09-22","حسام هاشم","07731622854","A2"),
("2886","2549","12","ريتال احمد حمزة","5","انثى","تمهيدي","2024-09-17","احمد","00000000000","A8"),
("2887","2550","12","حسن احمد حمزة","2","ذكر","حضانة","2024-09-17","احمد","00000000000","A8"),
("2888","2551","12","اوس حسين","5","ذكر","تمهيدي","2024-09-17","حسين","00000000000","A8"),
("2889","2552","12","انس حسين","4","ذكر","روضة","2024-09-17","حسين","00000000000","A8"),
("2890","2553","9","ليان صبري","5","انثى","تمهيدي","2024-09-16","صبري","00000000000","بلوك5"),
("2891","2554","9","فاطمة عبد الامير","5","انثى","تمهيدي","2024-09-16","عبد الامير","00000000000","بلوك5"),
("2892","2555","6","كرم محمد  صبري","1","ذكر","حضانة","2024-09-17","محمد صبري","07724016775","A1"),
("2893","2556","10","حسن اسعد طارق","2019","ذكر","تمهيدي","2024-09-17","اسعد طارق","07706266151","A6"),
("2894","2557","10","مريم محمد عبدالرضا","2019","انثى","تمهيدي","2024-09-18","محمد عبدالرضا","07717873963","A6"),
("2895","2558","7","فضل علي","5","ذكر","تمهيدي","2024-09-18","علي ","07700000000","A3"),
("2896","2559","12","ايلين ناصر","5","انثى","تمهيدي","2024-09-18","ناصر","00000000000","A8"),
("2898","2561","7"," يوسف حيدر نبيل","3","ذكر","تحضيري","2024-09-18","حيدر نبيل هادي","07700000000","A3"),
("2899","2562","9","هيلين معتصم ","3","انثى","حضانة","2024-09-18","معتصم","00000000000","بلوك5"),
("2901","2564","8"," ادم","2","ذكر","حضانة","2024-09-18","غسان","00000000000","a4"),
("2904","2565","11","ريتاج علي","2","انثى","حضانة","2024-09-19","علي حسين","","بلوك 7"),
("2905","2566","11","حسين علي","6","ذكر","تمهيدي","2024-09-19","علي","","A7"),
("2906","2567","12","حسين مرتضى ","1","ذكر","حضانة","2024-09-19","مرتضى","00000000000","A8"),
("2907","2568","7","شجاع مصطفى","4","ذكر","روضة","2024-09-19","مصطفى","07700000000","A3");
INSERT INTO stud_tb VALUES
("2908","2569","11","ود علي جمعه ","5","انثى","تمهيدي","2024-09-19","علي جمعه ","","A7"),
("2909","2570","10","فاطمه عبدالخالق صكبان","2020","انثى","تمهيدي","2024-09-19","عبدالخالق صكبان","07809998993","A1"),
("2910","2571","6","دانيال محسن سعد","5","ذكر","تمهيدي","2024-09-19","محسن سعد","07740132000","A1"),
("2911","2572","12","لانا غزوان عبد ","4","انثى","روضة","2024-09-19","غزوان","00000000000","A8"),
("2912","2573","6","محمد قاسم جاسم","5","ذكر","تمهيدي","2024-09-19","قاسم جاسم","07733514163","A2"),
("2914","2575","12","محمد باقر عبدالله","2","ذكر","حضانة","2024-09-19","باقر","00000000000","A8"),
("2915","2576","12","ايهم زيد علي","5","ذكر","تمهيدي","2024-09-19","زيد","00000000000","A8"),
("2916","2577","7","ليان احمد","5","انثى","تمهيدي","2024-09-19"," احمد","07700000000","A3"),
("2917","2578","12","عسل داوود","5","انثى","تمهيدي","2024-09-19","داوود","00000000000","A8"),
("2918","2579","12","تيم داوود","2","ذكر","حضانة","2024-09-19","داوود","00000000000","A8"),
("2919","2580","8","ياسين اكرم","5","ذكر","تمهيدي","2024-09-18","اكرم","00000000000","a4"),
("2920","2581","8","مصطفى","5","ذكر","تمهيدي","2024-09-19"," لؤي","00000000000"," a1"),
("2921","2582","8","علي","4","ذكر","روضة","2024-09-19","احمد","00000000000","B2"),
("2922","2583","8","زيد","4","ذكر","تحضيري","2024-09-19","احمد","00000000000","B2"),
("2923","2584","10","روزان كاظم ناظم","2021","انثى","تحضيري","2024-09-22","كاظم ناظم","07722278303","B8"),
("2924","2585","10","علي احمد حميد","2020","ذكر","روضة","2024-09-22","احمد حميد","07706756150","A6"),
("2925","2586","6","مصطفى رائد  خالد","1","ذكر","حضانة","2024-09-22","رائد خالد","07712352797","A2"),
("2926","2587","6","منه كرار ميثم ","2","انثى","حضانة","2024-09-22","كرار ميثم ","07715812325","A2"),
("2927","2588","10","محمد حيدر محمود","2019","ذكر","تمهيدي","2024-09-22","حيدر محمود","07773143014","A6"),
("2928","2589","6","ريما مصعب سهيل","4","انثى","روضة","2024-09-22","مصعب سهيل","00000000000","A6"),
("2929","2590","10","ريناد حيدر عباس","2021","ذكر","تمهيدي","2024-09-22","حيدر عباس","07707935081","A1"),
("2930","2591","6","روسينا محمود ايوب","5","انثى","تمهيدي","2024-09-22","محمود ايوب","07703497984","A1"),
("2931","2592","10","ايه علي جارح","2019","انثى","تمهيدي","2024-09-22","علي جارح محمد"," ","A6"),
("2932","2593","6","رزان حسين علي","5","انثى","تمهيدي","2024-09-22","حسين علي","07733777449","A1"),
("2933","2594","11","سراج كريم","4","ذكر","روضة","2024-09-22","كريم","","A7"),
("2934","2595","6","رضا محمد نوري","4","ذكر","روضة","2024-09-22","محمد نوري","07714886585","A2"),
("2935","2596","10","علي فائز يونس","2019","ذكر","تمهيدي","2024-09-22","فائز يونس","07739139887","A6"),
("2936","2597","10","ريما زيد مجيد","2020","انثى","تمهيدي","2024-09-22","زيد مجيد ","07713795524","A6"),
("2937","2598","10","رهف فراس","2019","انثى","تمهيدي","2024-09-22","فراس","07712263341","A6"),
("2938","2599","9","فاطمه قيصر","5","انثى","تمهيدي","2024-09-22","قيصر","00000000000","بلوك5"),
("2939","2600","9","غيث الكرار","4","ذكر","روضة","2024-09-22","الكرار","00000000000","بلوك5"),
("2940","2601","12","يمان احمد خضر","3","ذكر","تحضيري","2024-09-22","احمد","00000000000","A8"),
("2941","2602","12","سامر ياسر ","5","ذكر","تمهيدي","2024-09-22","ياسر","00000000000","A8"),
("2942","2603","12","ريان عبدالوهاب","5","ذكر","تمهيدي","2024-09-22","عبد الوهاب","00000000000","A8"),
("2943","2604","12","محمد تقي","3","ذكر","تحضيري","2024-09-12","تقي","00000000000","A8"),
("2944","2605","12","ازل فائز","5","انثى","تمهيدي","2024-09-22","فائز","00000000000","A8"),
("2945","2606","12","علي محمد رضا","5","ذكر","تمهيدي","2024-09-22","محمد","00000000000","A8"),
("2946","2607","12","معصومة فلاح","5","انثى","تمهيدي","2024-09-22","فلاح","00000000000","A8"),
("2947","2608","7","اران حسين نوري ","5","ذكر","تمهيدي","2024-09-22","حسين نوري","07700000000","A3"),
("2948","2609","7","مريم غزوان","5","انثى","تمهيدي","2024-09-22","غزوان","07700000000","A3"),
("2949","2610","7","مسلم عبد الكريم طالب ","5","ذكر","تمهيدي","2024-09-22","كريم ","07700000000","A3"),
("2950","2611","7","ايلين بلال","4","انثى","روضة","2024-09-22","بلال ","07700000000","A3"),
("2951","2612","11","عباس علي احمد","6","ذكر","تمهيدي","2024-09-22","علي احمد","07709456342","بلوك 7"),
("2952","2613","11","دينا احمد","3","انثى","حضانة","2024-09-22","احمد","00000000000","بلوك 7"),
("2953","2614","6","اليتا محمد قاسم","4","انثى","روضة","2024-09-22","محمد قاسم","07712265484","A2"),
("2954","2615","6","ايهم محمد قاسم","2","ذكر","حضانة","2024-09-22","محمد قاسم","07712265484","A2"),
("2955","2616","9","ادم سالم","3","ذكر","حضانة","2024-09-22","سالم","00000000000","بلوك5"),
("2956","2617","9","ابراهيم غزوان  ","5","ذكر","تمهيدي","2024-09-22","غزوان ","00000000000","بلوك5"),
("2957","2618","9","سما مثنى ","5","انثى","تمهيدي","2024-09-22","مثنى","00000000000","بلوك5"),
("2958","2619","9","ريم رعد","3","انثى","حضانة","2024-09-22","رعد","00000000000","بلوك5"),
("2959","2620","10","موسى جعفر هادي","2019","ذكر","تمهيدي","2024-09-22","جعفر هادي"," ","A6"),
("2960","2621","10","علي رحمن عبد علي","2019","ذكر","تمهيدي","2024-09-22","رحمن عبد علي ","07702545394","A6"),
("2961","2622","10","رهف رحمن عبد علي","2020","انثى","روضة","2024-09-22","رحمن عبد علي","07721199142","A6"),
("2962","2623","11","ايليا معتصم","5","ذكر","تمهيدي","2024-09-22","معتصم","","A7"),
("2963","2624","12","فيروز عمر","5","انثى","تمهيدي","2024-09-22","عمر","00000000000","A8"),
("2964","2625","12","احمد حارث","5","ذكر","تمهيدي","2024-09-22","حارث","00000000000","A8"),
("2965","2626","12","مصطفى علي كريم","5","ذكر","تمهيدي","2024-09-22","علي","00000000000","A8"),
("2966","2627","12","مصطفى امير","3","ذكر","تحضيري","2024-09-22","امير","00000000000","A8"),
("2967","2628","12","كوثر امير","5","انثى","تمهيدي","2024-09-22","امير","00000000000","A8"),
("2968","2629","9","يزن محمد","5","ذكر","تمهيدي","2024-09-22","محمد","00000000000","بلوك5"),
("2969","2630","10","جوليا محمد عبد الحسين","2021","انثى","روضة","2024-09-22","محمد عبد الحسين","07736182383","A6"),
("2970","2631","7","بيداء صلاح ","4","انثى","روضة","2024-09-22","صلاح ","07700000000","A3"),
("2971","2632","6","الين اسامة منيف","2","انثى","حضانة","2024-09-22","اسامة منيف","07725395390","A1"),
("2972","2633","10","داود مصعب لازم","2019","ذكر","تمهيدي","2024-09-22","مصعب لازم","07713983233","A6"),
("2973","2634","8","يارا","5","انثى","تمهيدي","2024-09-22","علي","00000000000","a4"),
("2974","2635","8","ليث","3","ذكر","تحضيري","2024-09-22","رائد","00000000000","a4"),
("2975","2636","8","ليان","3","انثى","حضانة","2024-09-22","سفيان","00000000000","a4"),
("2976","2637","8","ليان","4","انثى","روضة","2024-09-22","علي","00000000000","a4"),
("2977","2638","6","احمد فراس الكريم","5","ذكر","تمهيدي","2024-09-23","فراس  الكريم","07707701108","A1"),
("2978","2639","6","ديما علي فوزي","5","انثى","تمهيدي","2024-09-23","علي فوزي","07717928382","A2"),
("2979","2640","6","هيفيار رواد سهيل","4","انثى","روضة","2024-09-23","رواد  سهيل","07719459199","A2"),
("2980","2641","6","محمد عبدالله  بسام","5","ذكر","تمهيدي","2024-09-23","عبدالله بسام","00000000000","A1"),
("2981","2642","6","محمد احمد حنون","4","ذكر","روضة","2024-09-23","احمد حنون","07722457614","A2"),
("2982","2643","10","علي الاكبر سجاد يوسف","2020","ذكر","روضة","2024-09-23","سجاد يوسف","07732032223","A2"),
("2983","2644","10","عيسى ابو بكر عادل","2022","ذكر","تحضيري","2024-09-23","ابو بكر عادل","07727714454","A6"),
("2984","2645","7","جود حيدر طالب","5","ذكر","تمهيدي","2024-09-23","حيدر  ","","A3"),
("2985","2646","9","ملك علي سعد","4","انثى","روضة","2024-09-23","علي سعد ","00000000000","بلوك5"),
("2986","2647","9","فاطمة علي ","3","انثى","تحضيري","2024-09-23","علي ","00000000000","بلوك5"),
("2987","2648","9","روح الله احمد","3","ذكر","تحضيري","2024-09-23","احمد","00000000000","بلوك5"),
("2988","2649","9","لارين امجد","4","انثى","روضة","2024-09-23","امجد ","00000000000","بلوك5"),
("2989","2650","6","كرار حيدر حمزة","5","ذكر","تمهيدي","2024-09-23","حيدر حمزة","07724505020","A1"),
("2990","2651","9","عمار خالد فيصل ","3","ذكر","تحضيري","2024-09-23","خالد فيصل ","00000000000","بلوك5"),
("2991","2652","8","مريم حسام","4","انثى","تحضيري","2024-09-22","حسام","00000000000","a4"),
("2992","2653","8","لوريان جمال","4","انثى","تحضيري","2024-09-22","جمال","00000000000","a4"),
("2993","2654","8","محمد سعد","4","ذكر","روضة","2024-09-23","سعد","00000000000","a4"),
("2994","2655","6","جود علي كريم","4","انثى","روضة","2024-09-23","علي كريم","07730352281","A1"),
("2995","2656","6","شهد علي كريم","3","انثى","تحضيري","2024-09-23","علي كريم","07730352281","A1"),
("2996","2657","10","رزان سامر علي","2020","انثى","روضة","2024-09-23","سامر علي","07855516668","B8"),
("2997","2658","12","ماسة مصطفى ","2","انثى","حضانة","2024-09-22","مصطفى","00000000000","A8"),
("2998","2659","10","محمد حسن","2020","ذكر","روضة","2024-09-23","حسن","077619939","A1"),
("2999","2660","12","زينه حسين","5","انثى","تمهيدي","2024-09-23","حسين","00000000000","A8"),
("3000","2661","12","ادم زيدصلاح","4","ذكر","روضة","2024-09-23","زيد","00000000000","A8"),
("3001","2662","12","علي رسول علي","5","ذكر","تمهيدي","2024-09-23","رسول","00000000000","A8"),
("3002","2663","12","همام علي بسام","5","ذكر","تمهيدي","2024-09-23","علي","00000000000","A8"),
("3003","2664","12","زين الدين امين ","2","ذكر","حضانة","2024-09-23","امين","00000000000","A8"),
("3005","2666","12","علي الاكبر احمد خليل","5","ذكر","تمهيدي","2024-09-23","امين","00000000000","A8"),
("3006","2667","12","ليان احمد خليل","4","انثى","روضة","2024-09-23","احمد","00000000000","A8"),
("3007","2668","7","قمر حيدره","2","انثى","حضانة","2024-09-23","حيدره ","07700000000","A3"),
("3008","2669","8"," فهد","2","ذكر","حضانة","2024-09-23","اركان","00000000000","a4"),
("3009","2670","8","علي","4","ذكر","تحضيري","2024-09-23","مهند","00000000000","a4");
INSERT INTO stud_tb VALUES
("3010","2671","8","يسر","2","انثى","حضانة","2024-09-23","علي","00000000000","a4"),
("3011","2672","11","زين العابدين سيف","5","ذكر","تمهيدي","2024-09-23","سيف","00000000000","A7"),
("3012","2673","7","ايلين ازهر ","4","انثى","روضة","2024-09-22","ازهر","","A3"),
("3013","2674","7","  حيدر  احمد شاكر ","5","ذكر","تمهيدي","2024-09-23","احمد","07700000000","A3"),
("3014","2675","11","دانيه احمد","5","انثى","تمهيدي","2024-09-23","احمد","","A7"),
("3015","2676","11","علي فريد","2","ذكر","حضانة","2024-09-24","فريد","","A7"),
("3016","2677","10","ليث عبد الله اياد","2019","ذكر","تمهيدي","2024-09-24","عبد الله اياد","07736307493","A6"),
("3017","2678","12","رضا سدير","5","ذكر","تمهيدي","2024-09-24","سدير","00000000000","A8"),
("3018","2679","6","غزال انمار عبود","5","انثى","تمهيدي","2024-09-24","انمار عبود","07719079777","A1"),
("3019","2680","7","مريم حيدر غني ","5","انثى","تمهيدي","2024-09-24","حيدر غني ","07700000000","A2"),
("3020","2681","11","حسن بارق","5","ذكر","تمهيدي","2024-09-24","بارق","00000000000","A7"),
("3021","2682","12","حسن محمد باسم","2","ذكر","حضانة","2024-09-23","محمد","00000000000","A8"),
("3022","2683","10","محمد ايهاب اكرم","2018","ذكر","تمهيدي","2024-09-24","ايهاب اكرم","07735223279","A1"),
("3023","2684","9","ادريس حيدر فليح ","4","ذكر","روضة","2024-09-24","حيدر فليح","00000000000","بلوك5"),
("3024","2685","6","سيف باسم خلف","4","ذكر","روضة","2024-09-24","باسم خلف","07835931445","A1"),
("3025","2686","9","حسين ماهر عباس","5","ذكر","تمهيدي","2024-09-24","ماهر عباس ","00000000000","بلوك5"),
("3026","2687","9","مجتبى علي عبد الزهرة","3","ذكر","تحضيري","2024-09-24","علي عبد الزهرة","00000000000","بلوك5"),
("3027","2688","9","مؤمل مهند ","2","ذكر","حضانة","2024-09-24","مهند","00000000000","بلوك5"),
("3028","2689","7","غيث عدنان ","4","ذكر","روضة","2024-09-24","عدنان","07700000000","A3"),
("3029","2690","9","الحسن علي","5","ذكر","تمهيدي","2024-09-23","علي ","00000000000","بلوك5"),
("3030","2691","10","رحمه باسم جلوب","2021","انثى","تحضيري","2024-09-24","باسم جلوب","07701894882","A6"),
("3031","2692","11","حسن محمد جواد ","5","ذكر","تمهيدي","2024-09-24","محمد جواد","00000000000","A7"),
("3033","2694","7","امير سلمان ","4","ذكر","روضة","2024-09-24","سلمان ","07700000000","A3"),
("3034","2695","6","ميرا ليث هيثم ","4","انثى","روضة","2024-09-24","ليث  هيثم","07719944332","A2"),
("3035","2696","7","ادم سلمان ","3","ذكر","حضانة","2024-09-24","سلمان ","07700000000","A2"),
("3036","2697","10","ماسه مشتاق جمال","2020","انثى","حضانة","2024-09-24","مشتاق جمال","","B2"),
("3037","2698","10","حسن حيدر حافظ","2022","ذكر","حضانة","2024-09-24","حيدر حافظ","07706220832","A6"),
("3038","2699","6","قسور مصطفى","6","ذكر","تمهيدي","2024-09-24","0000000000","00000000000","A2"),
("3039","2700","6","شاهين مصطفى ","6","ذكر","تمهيدي","2024-09-24","0000000000","00000000000","A2"),
("3040","2701","7","ماسه حيدر  محمد ","4","انثى","روضة","2024-09-25","حيدر  ","07700000000","A3"),
("3041","2702","7","سلطان حيدر محمد ","3","ذكر","تحضيري","2024-09-25","حيدر  ","","A3"),
("3042","2703","7","جمانة جاسم ","4","انثى","روضة","2024-09-25","جاسم ","07700000000","A3"),
("3043","2704","6","نايا مصطفى فوزي","3","انثى","تحضيري","2024-09-25","مصطفى فوزي","07737979704","A2"),
("3044","2705","6","حسين هشام علي","5","ذكر","تمهيدي","2024-09-25","هشام علي","07702561345","A2"),
("3045","2706","6","لوجين علي عبد الزهرة","4","انثى","روضة","2024-09-24","علي عبد الزهرة","00000000000","A2"),
("3046","2707","6","سلطان عمار","4","ذكر","روضة","2024-09-24","عمار","00000000000","A9"),
("3047","2708","10","علي سيف هيثم","2021","ذكر","تحضيري","2024-09-25","سيف هيثم","07803658292","A6"),
("3048","2709","12","ماري احمد","3","انثى","تحضيري","2024-09-25","احمد","00000000000","A8"),
("3049","2710","12","تميم حسين","4","ذكر","روضة","2024-09-23","حسين","00000000000","A8"),
("3050","2711","10","مياسه محمد محسن","2020","انثى","روضة","2024-09-24","محمد محسن"," ","A6"),
("3051","2712","7","شهم عدنان ","5","ذكر","تمهيدي","2024-09-25","عدنان","07700000000","A3"),
("3052","2713","7","عباس محمد ","5","ذكر","تمهيدي","2024-09-25","محمد","07700000000","A3"),
("3053","2714","12","يوسف باقر محمد","4","ذكر","روضة","2024-09-25","باقر","00000000000","A8"),
("3054","2715","7","نور محمد","5","انثى","تمهيدي","2024-09-23","محمد","07700000000","A3"),
("3055","2716","10","ايلين احمد نعيمه","2022","انثى","تمهيدي","2024-09-22","احمد نعيمه","","A6"),
("3056","2717","12","دانة معتز","3","انثى","تحضيري","2024-09-25","معتز","00000000000","A8"),
("3057","2718","7","يوسف حسن ","4","ذكر","روضة","2024-09-23","حسن","07700000000","A3"),
("3058","2719","7","ابراهيم محمود ","2","ذكر","حضانة","2024-09-25","محمود","07700000000","A3"),
("3059","2720","7","علي ليث ","2","ذكر","حضانة","2024-09-23","ليث","07700000000","A3"),
("3060","2721","10","عباس اوس علي","2020","ذكر","حضانة","2024-09-22","اوس علي"," ","A6"),
("3061","2722","10","لنا مثنى ","3","انثى","تحضيري","2024-09-23","مثنى","00000000000","A6"),
("3062","2723","11","علي ياسر يعقوب","3","ذكر","حضانة","2024-09-19","ياسر","","A7"),
("3063","2724","11","امير منتظر","4","ذكر","روضة","2024-09-22","منتظر","","A7"),
("3064","2725","7","محمد سيف الدين","3","ذكر","تحضيري","2024-09-22","سيف الدين","07700000000","A3"),
("3065","2726","7","اسلا سرمد","2","انثى","حضانة","2024-09-23","سرمد","07700000000","A3"),
("3066","2727","7","يزن محمد","5","ذكر","تمهيدي","2024-09-23","محمد ","00000000000","a3"),
("3067","2728","8","امير","2","ذكر","حضانة","2024-09-24","عامر","00000000000","a4"),
("3068","2729","8","بنيامين","5","ذكر","تمهيدي","2024-09-22","منتظر","00000000000","a4"),
("3069","2730","11","يسر عقيل","5","انثى","تمهيدي","2024-09-25","عقيل","","A7"),
("3070","2731","11","يوسف عقيل","6","ذكر","تمهيدي","2024-09-25","عقيل","00000000000","A7"),
("3071","2732","7","محمد معتز","2","ذكر","حضانة","2024-09-26","معتز","07700000000","A3"),
("3072","2733","6","علي اسعد عدنان","4","ذكر","روضة","2024-09-26","اسعد عدنان","07771934382","A2"),
("3073","2734","7","ماس علاء","3","انثى","تحضيري","2024-09-26","علاء","07700000000","A2"),
("3074","2735","9","الياسمين فراس","3","انثى","حضانة","2024-09-25","فراس ","00000000000","بلوك5"),
("3075","2736","9","علي مصطفى","3","ذكر","حضانة","2024-09-23","مصطفى ","00000000000","بلوك5"),
("3076","2737","9","افنان سراج","5","انثى","تمهيدي","2024-09-26","سراج ","00000000000","بلوك5"),
("3077","2738","11","زمرد محمود","5","انثى","تمهيدي","2024-09-25","محمود","","A7"),
("3078","2739","6","ود مصطفى داود","4","انثى","روضة","2024-09-26","مصطفى داود","07813901462","A2"),
("3079","2740","6","لانا ليث جبار","5","انثى","تمهيدي","2024-09-26","ليث جبار","07715101039","A2"),
("3080","2741","7","ميلان نمير سمير","4","انثى","روضة","2024-09-26","نمير سمير ","07700000000","A3"),
("3081","2742","7","رزان حسن علي","4","انثى","روضة","2024-09-26","حسن","07700000000","A3"),
("3082","2743","8"," علي الاكبر","4","ذكر","روضة","2024-09-29","حسين"," 0770982585"," b3"),
("3083","2744","8"," محمد الجواد","3","ذكر","تحضيري","2024-09-29","حسين","00000000000","B3"),
("3084","2745","11","فهد حسين","3","ذكر","حضانة","2024-09-29","حسين","","A7"),
("3085","2746","10","غدير ليث حكمت ","2018","انثى","تمهيدي","2024-09-29","ليث حكمت","07709267108","A6"),
("3086","2747","6","جود مصطفى  جاسب","4","انثى","روضة","2024-09-29","مصطفى جاسب","00000000000","A2"),
("3087","2748","6","ريان رياض كاظم","5","ذكر","تمهيدي","2024-09-29","رياض كاظم","00000000000","A2"),
("3088","2749","6","عباس رحيم","5","ذكر","تمهيدي","2024-09-29","0000000000","07722669586","A2"),
("3089","2750","10","روعه عمار صفر","2020","انثى","روضة","2024-09-29","عمار صفر","07711951980","A6"),
("3090","2751","10","علي فاضل","2019","ذكر","تمهيدي","2024-09-29","فاضل","07713836964","A6"),
("3091","2752","6","غنى ذنون يونس","5","انثى","تمهيدي","2024-09-26","ذنون يونس","07712824802","A2"),
("3092","2753","7","ادم ديار","4","ذكر","روضة","2024-09-29","ديار","07700000000","A3"),
("3093","2754","11","زين العابدين عبد الرسول","5","ذكر","تمهيدي","2024-09-29","عبد الرسول","00000000000","A7"),
("3094","2755","7","ميار علاء ياركه ","5","انثى","تمهيدي","2024-09-29","علاء","07700000000","A3"),
("3095","2756","7","محمد احمد كاظم ","4","ذكر","روضة","2024-09-29","احمد","07700000000","A3"),
("3096","2757","7","علي احمد كاظم","4","ذكر","روضة","2024-09-29","احمد","07700000000","A3"),
("3097","2758","7","قمر جميل","5","انثى","تمهيدي","2024-09-29","جميل","07700000000","A3"),
("3098","2759","12","ازل يوسف","4","انثى","روضة","2024-09-26","يوسف","00000000000","A8"),
("3099","2760","12","فرح محمد هادي","4","انثى","روضة","2024-09-26","محمد","00000000000","A8"),
("3100","2761","12","حسين علاء","5","ذكر","تمهيدي","2024-09-29","علاء","00000000000","A8"),
("3101","2762","12","حرير انس احسان","3","انثى","تحضيري","2024-09-29","انس","00000000000","A8"),
("3102","2763","12"," زين العابدين زيد ابراهيم","5","ذكر","تمهيدي","2024-09-29","زيد","00000000000","A8"),
("3103","2764","12","در احمد طه","4","انثى","روضة","2024-09-29","احمد","00000000000","A8"),
("3104","2765","12","في احمد طه","1","انثى","حضانة","2024-09-29","احمد","00000000000","A8"),
("3105","2766","12","ماري علي خليل","4","انثى","روضة","2024-09-29","علي","00000000000","A8"),
("3106","2767","12","لارين حسين محمد","4","انثى","روضة","2024-09-29","حسين","00000000000","A8"),
("3107","2768","12","ادم رياض حسين","4","ذكر","روضة","2024-09-29","رياض","00000000000","A8"),
("3108","2769","12","لارين مصطفى حازم","4","انثى","روضة","2024-09-29","مصطفى","00000000000","A8"),
("3109","2770","12","فهد كرم محمد","4","ذكر","روضة","2024-09-29","كرم","00000000000","A8"),
("3110","2771","9","ميار رياض","5","انثى","تمهيدي","2024-09-22","رياض","00000000000","بلوك5");
INSERT INTO stud_tb VALUES
("3111","2772","9","مسك فاروق","4","انثى","روضة","2024-09-25","فاروق","00000000000","بلوك5"),
("3112","2773","9","عباس محمد عيسى","5","ذكر","تمهيدي","2024-09-26","محمد عيسى","00000000000","بلوك5"),
("3113","2774","9","مريم نهاد","4","انثى","روضة","2024-09-29","نهاد","00000000000","بلوك5"),
("3114","2775","9","يوسف عبد الرحمن","4","ذكر","روضة","2024-09-25","عبد الرحمن","00000000000","بلوك5"),
("3115","2776","9","كوثر علي","5","انثى","تمهيدي","2024-09-29","علي ","00000000000","بلوك5"),
("3116","2777","9","لمار قصي","5","انثى","تمهيدي","2024-09-29","قصي ","00000000000","بلوك5"),
("3117","2778","9","مريم حسن نعمان","4","انثى","روضة","2024-09-29","حسن نعمان","00000000000","بلوك5"),
("3118","2779","6","محمد ايام شاكر ","5","ذكر","تمهيدي","2024-09-29","ايام شاكر ","07706606400","A2"),
("3119","2780","6","فدك ايام شاكر","4","انثى","روضة","2024-09-29","ايام شاكر","07706606400","A2"),
("3120","2781","11","زهراء عباس","5","انثى","تمهيدي","2024-09-30","عباس شفاف","","A7"),
("3121","2782","6","غزل عزت سالم ","5","انثى","تمهيدي","2024-09-30","عزت سالم","07722605660","A2"),
("3122","2783","6","اوسم محمد عبد الكريم","2","ذكر","حضانة","2024-09-30","محمد عبد الكريم","07737942221","A2"),
("3123","2784","7","يزن حيدر ","2","ذكر","حضانة","2024-09-30","حيدر  ","07700000000","A3"),
("3124","2785","7","تاتيانا عسيى","3","انثى","تحضيري","2024-09-30","عيسى","07700000000","A3"),
("3125","2786","9","علي فاروق","3","ذكر","تحضيري","2024-09-29","فاروق","00000000000","بلوك5"),
("3126","2787","9","مريم فاروق","3","انثى","تحضيري","2024-09-29","فاروق","00000000000","بلوك5"),
("3127","2788","11","رقيه رحيم","5","انثى","تمهيدي","2024-09-30","رحيم كاظم","","A7"),
("3128","2789","6","لينا محمد عبد المنعم ","1","انثى","حضانة","2024-09-30","محمد عبد المنعم ","07733446130","A2"),
("3129","2790","12","قمر امجد حميد","2","انثى","حضانة","2024-09-29","امجد","00000000000","A8"),
("3130","2791","12","انمار عبد القادر","5","ذكر","تمهيدي","2024-09-29","عبد القادر","00000000000","A8"),
("3131","2792","12","نرجس علي عبدالحسين","1","انثى","حضانة","2024-09-30","علي","00000000000","A8"),
("3132","2793","12","كرار علي هيثم","4","ذكر","روضة","2024-09-30","علي","00000000000","A8"),
("3133","2794","12","جمان اكرم سعدون","3","انثى","تحضيري","2024-09-30","اكرم","00000000000","A8"),
("3134","2795","12","ليا حسن هادي","4","انثى","روضة","2024-09-30","حسن","00000000000","A8"),
("3135","2796","10","لارين محمود محمد","2019","انثى","تمهيدي","2024-09-30","محمود محمد","07715150320","A6"),
("3136","2797","7","رحيق سمير ","3","انثى","تحضيري","2024-09-30","سمير","07700000000","A3"),
("3137","2798","6","علي الاكبر  حسين وارد","5","ذكر","تمهيدي","2024-09-30","حسين وارد","07835095139","A2"),
("3138","2799","8"," لامار","5","انثى","تمهيدي","2024-09-30","علي ","00000000000","a4"),
("3139","2800","8","يوسف","5","ذكر","تمهيدي","2024-09-30","محمود","00000000000","a4"),
("3140","2801","7","تيم ماهر ","4","ذكر","روضة","2024-09-30","ماهر","07700000000","A3"),
("3141","2802","10","نيلاي محمد شكير","2021","انثى","تحضيري","2024-09-30","محمد شكير"," ","A6"),
("3142","2803","7","علي احمد جلوب","3","ذكر","تحضيري","2024-10-01","احمد","07700000000","A6"),
("3143","2804","8"," يوسف","4","ذكر","روضة","2024-09-30","عمر","00000000000","a4"),
("3144","2805","8","مصطفى","4","ذكر","روضة","2024-09-30","محمد","00000000000","a4"),
("3145","2806","8"," احمد","2","ذكر","حضانة","2024-09-30","محمد","00000000000","a4"),
("3146","2807","6","موسى زياد قيس","5","ذكر","تمهيدي","2024-10-01","زياد قيس","0770604270","A2"),
("3147","2808","7","رضا سيف مهدي ","3","ذكر","تحضيري","2024-10-01","مهدي","07700000000","A3"),
("3148","2809","7","الما مشتاق عباس ","3","انثى","تحضيري","2024-10-01","مشتاق ","07700000000","A3"),
("3149","2810","11","مجتبى صادق","3","ذكر","حضانة","2024-10-01","صادق","","A7"),
("3150","2811","6","مريم باسم خضير","4","انثى","روضة","2024-10-01","باسم خضير","07746558119","A2"),
("3151","2812","12","عامر علي عامر","5","ذكر","تمهيدي","2024-10-01","علي","00000000000","A8"),
("3152","2813","12","غزل سيف كامل","4","انثى","روضة","2024-10-01","سيف","00000000000","A8"),
("3153","2814","12","مصطفى احمد مصطفى","4","ذكر","روضة","2024-10-01","احمد","00000000000","A8"),
("3154","2815","12","عسل حيدر علي","2","انثى","حضانة","2024-10-01","حيدر","00000000000","A8"),
("3155","2816","6","ريتال همام علي","5","انثى","تمهيدي","2024-10-01","همام علي","07714074282","A2"),
("3156","2817","11","يونس عمر","4","ذكر","روضة","2024-10-01","عمر ","00000000000","A7"),
("3157","2818","12","ماريا محمد حلمي","4","انثى","روضة","2024-10-01","محمد","00000000000","A8"),
("3158","2819","11","لينا سيف ","4","انثى","روضة","2024-10-01","سيف","00000000000","A7"),
("3159","2820","9","سنا ذو الافقار ","2","انثى","حضانة","2024-10-01","ذو الفقار","00000000000","بلوك5"),
("3160","2821","7","لمار احمد خيري ","3","انثى","تحضيري","2024-10-01","احمد","07700000000","A3"),
("3161","2822","8"," ريم","3","انثى","تحضيري","2024-10-01","عمر ","00000000000","a4"),
("3162","2823","6","محمد عمار","2","ذكر","حضانة","2024-10-02","0000000000","00000000000","A2"),
("3163","2824","6","ادم ياسر محمد","4","ذكر","روضة","2024-10-02","ياسر محمد","07736041081","A2"),
("3164","2825","10","علي احمد عبدالله","2021","ذكر","تحضيري","2024-10-02","احمد عبدالله","07710570815","A6"),
("3165","2826","11","ديما مصطفى","5","انثى","تمهيدي","2024-10-02","مصطفى","00000000000","A7"),
("3166","2827","7","فاطمه محمد خيري ","5","انثى","تمهيدي","2024-10-02","محمد","07700000000","A3"),
("3167","2828","9","يوسف مهدي ","4","ذكر","روضة","2024-10-01","مهدي","00000000000","بلوك5"),
("3168","2829","12","رامي رائد","2","ذكر","حضانة","2024-10-02","رائد","00000000000","A8"),
("3169","2830","12","العز محمد ","1","ذكر","حضانة","2024-10-02","محمد","00000000000","A8"),
("3170","2831","8","لوليا","3","انثى","تحضيري","2024-10-02","براء","00000000000","a4"),
("3171","2832","8","اسراء","3","انثى","تحضيري","2024-10-01","وهب","00000000000","a4"),
("3172","2833","10","يوسف مهدي فاروق","2020","ذكر","تمهيدي","2024-10-06","مهدي فاروق","07759500398","A1"),
("3173","2834","10","ماري بيل مضر بيشاني","2020","انثى","روضة","2024-10-06","مضر بيشاني","07768400593","A6"),
("3174","2835","6","رينان حيدر عباس","4","انثى","روضة","2024-10-06","حيدر عباس","07734002320","A1"),
("3175","2836","6","مهيمن علي حكمت","2","ذكر","حضانة","2024-10-06","علي حكمت ","07710943834","A1"),
("3176","2837","12","وسيم حازم","4","ذكر","روضة","2024-10-02","حازم","00000000000","A8"),
("3177","2838","8","ماس","4","انثى","روضة","2024-10-06","عمار","00000000000","a4"),
("3178","2839","6","ايان سمير قيس","5","ذكر","تمهيدي","2024-10-06","سمير  قيس","07713769756","A2"),
("3179","2840","12","علي الاكبر الحر","5","ذكر","تمهيدي","2024-10-06","الحر حيدر","00000000000","A8"),
("3180","2841","9","ياسين احمد ياسين ","5","ذكر","تمهيدي","2024-10-06","احمد ياسين","00000000000","بلوك5"),
("3181","2842","9","ياسر امير","2","ذكر","حضانة","2024-10-06","امير","00000000000","بلوك5"),
("3183","2844","7","مسك عباس ياسر","2","انثى","حضانة","2024-10-06","عباس","07700000000","A3"),
("3184","2845","7","ابراهيم احمد ","3","ذكر","تحضيري","2024-10-06","علي","07700000000","A3"),
("3185","2846","7","احمد علي ","3","ذكر","تحضيري","2024-10-06","علي","07700000000","A3"),
("3186","2847","7","ابراهيم علي ","3","ذكر","تحضيري","2024-10-06","علي","07700000000","A3"),
("3187","2848","7","علي حسين هاشم ","4","ذكر","روضة","2024-10-06","حسين","07700000000","A3"),
("3188","2849","11","يزن اياد","5","ذكر","تمهيدي","2024-10-07","اياد طلال علي","00000000000","A7"),
("3189","2850","11","علي رضا حيدر","3","ذكر","حضانة","2024-10-07","رضا حيدر","00000000000","A7"),
("3190","2851","10","يوسف جعفر يوسف","2023","ذكر","حضانة","2024-10-07","جعفر يوسف ","07708030560","A6"),
("3191","2852","10","زمرد عبدالكريم صادق","2019","انثى","تمهيدي","2024-10-07","عبدالكريم صادق","07718604812","A1"),
("3192","2853","10","علي حسن حيدر","2021","ذكر","روضة","2024-10-07","حسن حيدر","07714002421","A5"),
("3193","2854","10","امير احمد شوقي","2021","ذكر","تمهيدي","2024-10-07","احمد شوقي","07711270603","A6"),
("3194","2855","10","فهد بلال محمد","2021","ذكر","تحضيري","2024-10-07","بلال محمد","07716661817","A6"),
("3195","2856","9","حسين علي محمد","5","ذكر","تمهيدي","2024-10-07","علي  محمد","00000000000","بلوك5"),
("3196","2857","7","معصومة جلال","2","انثى","حضانة","2024-10-06","جلال","07700000000","A3"),
("3197","2858","7","ليان عباس فلحي ","3","انثى","تحضيري","2024-10-08","عباس","07700000000","A3"),
("3198","2859","7","سلطان ساري ","5","ذكر","روضة","2024-10-08","ساري تحسين ","07700000000","A3"),
("3199","2860","10","ايلين وميض احمد","2019","انثى","تمهيدي","2024-10-08","وميض احمد","07740266163","A7"),
("3200","2861","11","ضياء راغب اياد","4","ذكر","روضة","2024-10-08","راغب اياد","00000000000","a8"),
("3201","2862","11","بسام راغب اياد","3","ذكر","حضانة","2024-10-08","راغب اياد","00000000000","a8"),
("3202","2863","10","يزن مصطفى تحسين","2021","ذكر","تحضيري","2024-10-06","مصطفى تحسين","07710014585","A6"),
("3203","2864","9","ارام فؤاد","2","ذكر","حضانة","2024-10-09","فؤاد","00000000000","بلوك5"),
("3204","2865","6","فاطمة احمد حسين","4","انثى","روضة","2024-10-06","0000000000","00000000000","A2"),
("3205","2866","6","ياسين مصطفى","3","ذكر","تحضيري","2024-10-06","مصطفى محمد","07708703883","A2"),
("3206","2867","6","زهراء مصطفى","3","انثى","تحضيري","2024-10-06","مصطفى محمد","07708703883","A2"),
("3207","2868","6","فاطمة كرار ميثم","4","انثى","روضة","2024-09-22","0000000000","00000000000","0000"),
("3208","2869","6","يوسف علي عبد","4","ذكر","روضة","2024-10-07","علي عبد الكاظم","07701703082","A2"),
("3209","2870","12","مودة اكرم عبد الرضا","2","انثى","حضانة","2024-10-06","اكرم","00000000000","A8"),
("3210","2871","12","علي عصام مجيد","1","ذكر","حضانة","2024-10-07","عصام","00000000000","A8"),
("3211","2872","6","علي اسامة","5","ذكر","تمهيدي","2024-10-07","اسامة هاني","07722434511","0000");
INSERT INTO stud_tb VALUES
("3212","2873","6","جود وسام سمير ","2","ذكر","حضانة","2024-10-08","وسام سمير","07709647508","0000"),
("3213","2874","12","جنات عصام مجيد","4","انثى","روضة","2024-10-07","عصام","00000000000","A8"),
("3214","2875","8","زين العابدين","4","ذكر","روضة","2024-10-07","سرور"," 078 068484","a4"),
("3215","2876","8","الياس","5","ذكر","تمهيدي","2024-10-07","محمد خيرالله","07738139227","a1"),
("3216","2877","8"," ميرال","4","ذكر","تحضيري","2024-10-06","مصطفى","07703795655","a4"),
("3217","2878","8","علي","5","ذكر","تمهيدي","2024-10-04","حيدر","07725881266","a4"),
("3218","2879","8","غياث","5","ذكر","تمهيدي","2024-10-07","عمر","07722422066","A3"),
("3219","2880","8"," ايلين","4","انثى","تحضيري","2024-10-29","اثير","07800101888","a4"),
("3220","2881","8","امير","4","ذكر","روضة","2024-10-07","علي","00000000000","a4"),
("3221","2882","8","قائد","3","ذكر","تحضيري","2024-10-07","زيد","00000000000","a4");




CREATE TABLE `users_tb` (
  `id_user` int(100) NOT NULL AUTO_INCREMENT,
  `user_name` varchar(250) NOT NULL,
  `user_pass` varchar(250) NOT NULL,
  `role` varchar(100) NOT NULL,
  PRIMARY KEY (`id_user`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


INSERT INTO users_tb VALUES
("1","admin","20172017","Admin"),
("6","A2","1234","User"),
("7","A3","1234","User"),
("8","A4","1234","User"),
("9","A5","1234","User"),
("10","A6","1234","User"),
("11","A7","1234","User"),
("12","A8","1234","User"),
("19","m1","12341234","Mod"),
("20","B1","1234","User"),
("23","B2","1234","User");


