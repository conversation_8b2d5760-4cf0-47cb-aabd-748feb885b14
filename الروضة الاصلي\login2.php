<?php
$user='kidzrcle_rwda';
$pass='kidzrcle_rwda';

$pdo= new PDO ('mysql:host=localhost;dbname=kidzrcle_rwda',$user,$pass);
$login=$pdo->prepare("SELECT * FROM users_tb WHERE user_name=:u_user AND user_pass=:u_pass");
$login->bindParam('u_user',$_POST['u_user']);
$login->bindParam('u_pass',$_POST['u_pass']);
$login->execute();
if(isset($_POST['login'])){
if($login->rowCount()===1){
$user=$login->fetchObject();
session_start();
$_SESSION['user']=$user;
if($user->role==='Admin')
{
   
    echo "<h3 class=g14>الادمن .  تم تسجيل الدخول اهلا بك</h3>";
    header("Refresh:3;url=Admin/home.php");
}elseif($user->role==='User'){
    echo "<h3 class=g12>مستخدم .  تم تسجيل الدخول اهلا بك</h3>";
    header("Refresh:3;url=Users/home.php");
}elseif($user->role==="Mod"){
    echo "<h3 class=g12>مدقق .  تم تسجيل الدخول اهلا بك</h3>";
    header("Refresh:3;url=Moda/home.php");
}

}else{
    echo "<h3 class=g13>الرجاء التأكد من المعلومات</h3>";

}
}


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="Admin/css/styles.css">
    <link rel="stylesheet" href="Admin/css/bootstrap.min.css">
    <link rel="stylesheet" href="Admin/css/datatables.min.css">
    <link rel="stylesheet" href="Admin/css/all.min.css">
    <link rel="icon" href="icon.ico">
    <title>تسجيل دخول</title>
</head>
<body>
<form method="POST">
<div class='continC'>
 <div class='input-box'>
        <label for="user"> اسم اليوزر <label>
        <input type="text" placeholder="  USER NAME " name="u_user" required>
        <label for="pass">كلمة المرور <label>
        <input type="password" placeholder=" Password " name="u_pass" required>
        <button class=btn name="login">تسجيل دخول </button>
        <div class=icons>
        
 </dvi>  
 </div>

 </div>
</div>
</form>
<footer class=footer_p>
    <p>جميع الحقوق محفوظة لمؤسسة كيدز اكادمي </p>
  
</footer>
</html>