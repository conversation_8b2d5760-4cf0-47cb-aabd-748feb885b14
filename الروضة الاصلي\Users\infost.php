<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معلومات الطلاب</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.dataTables.min.js"></script>
    <link rel="stylesheet" href="css/jquery.dataTables.min.css">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
   </head>
    <body>
    <form action="" metho="POST">
       <div class="search">
   <input type="text" id="live_search" placeholder="يمكنك البحث عن معلومات الطالب هنا">
  <button class="btn btn-success text-light" name="sub"  ><a href="../Admin/addon/exportStud.php">تحميل اكسل</a> </button>
    
    
  </div>
  </form>
  <div class="wrapper2" id="tost_info">
        <div id="toast2">
            <div class="container-11">
            <i class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-22">
                <p id="info">عدد طلاب الحضانة الفعالين </p>
                <p id="numberstud">عدد طلاب الحضانة الفعالين  </p>
            </div>
        </div>
    </div>
  


    <table class="table" id="tb" >
  <thead>
    <tr>
    <th scope="col">  العمليات  </th>
    <th scope="col"> التسجيل</th>
    <th scope="col"> الايام المتبقية </th>
      <th scope="col">حالة الاشتراك</th>
      <th scope="col">قيمة الاشتراك</th>
      <th scope="col">تاريخ النفاذ </th>
      <th scope="col">تاريخ الاشتراك</th>
      <th scope="col">رقم ولي الامر</th>
      <th scope="col">صنف التسجيل</th>
      <th scope="col">الجنس</th>
      <th scope="col"> العمر</th>
      <th scope="col">السكن</th>
      <th scope="col">اسم الطالب  </th>
      <th scope="col">رقم  الوصل </th>
      
    </tr>
  </thead>
  <tbody id="myTable">
  
   
  </tbody>
</table>
<section id="deletmodle">
      <div class="modal-box">
        <h2>! تنبيه </h2>
        <h3>هل انته متأكد من عملية الحذف كون البيانات لايمكن استرجاعها</h3>

        <div class="buttons-method">
          <button class="close-btn">اغلاق</button>
         <button name="remov" id="rm" class="remove-btn">حذف</button>
        </div>
      </div>
    </section>
    <script>
      function deletdata(id){
        $("#deletmodle").addClass("active");
        $(".table").addClass("active");
        $(".close-btn").click(function () { 
          $(".table").removeClass("active")
          $("#deletmodle").removeClass("active")
        });
        $("#rm").click(function () { 
          $(".table").removeClass("active")
          $.ajax({url:'addon/reomves.php',
          method:"POST",
          data:({removeid:id}),
          success:function(response){
            console.log(id)
          $("#deletmodle").removeClass("active");
          jQuery("#tr_"+id).css("background","#eaeaea");
          jQuery("#tr_"+id).hide(2000);
          setInterval(function () {
            window.location.reload();
           },2000);
         
        }
        });
        });
        }
    </script>
  
    <script >
      let x;

      let toast = document.getElementById("toast2"),
      studcount = document.getElementById("numberstud"),
      info=document.getElementById("info")
      function showToast2(ss){
      clearTimeout(x);
      studcount.innerText=ss
      studcount.style.fontSize="25px"
      studcount.style.marginTop="-20px"
      info.style.marginTop="-27px"
      toast.style.transform = "translateX(0)";
      toast.style.transition='1.5s';
    
}
function reomtost(){
  x = setTimeout(()=>{
    toast.style.transition='0.5s';
        toast.style.transform = "translateX(-500px)"
    }, 500);
}

    </script>
  
   </body>
   
   <script>
      var selc =<?php echo $_SESSION['user']->id_user?>;
        $.ajax({
          method: "POST",
          url: "addon/infostudF.php",
          data: {id:selc},
          success: function (data) {
            $("#myTable").html(data);
             $("#tb").DataTable();
            
          }
        
        })
</script>
<script>
      $(document).ready(function () {
        $("#live_search").keyup(function(){
          var input = $(this).val();
          
          if(input != ""){
            $.ajax({
              method: "POST",
              url: "addon/searchF.php",
              data: {input:input},
              success:function (data) {
                reomtost()
                $("#tb").html(data);
              }
            });

          }else{
            location.reload(true)
          }
         
        })
      });
    </script>
</html>