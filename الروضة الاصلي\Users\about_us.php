<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ماذا عنا</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
   </head>
   <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Cairo', sans-serif;
        direction: rtl;
    }

    .about-container {
        max-width: 800px;
        margin: 50px auto;
        padding: 20px;
    }

    .developer-info {
        margin-bottom: 40px;
    }

    .developer-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 40px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .developer-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
    }

    .developer-avatar {
        width: 120px;
        height: 120px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .developer-avatar i {
        font-size: 50px;
        color: white;
    }

    .developer-name {
        color: #2c3e50;
        font-size: 28px;
        font-weight: bold;
        margin: 20px 0 10px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .developer-title {
        color: #7f8c8d;
        font-size: 18px;
        margin-bottom: 20px;
        font-weight: 500;
    }

    .description {
        color: #34495e;
        line-height: 1.8;
        font-size: 16px;
    }

    .description p {
        margin: 10px 0;
    }

    .contact-section {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .contact-title {
        color: #2c3e50;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .contact-info {
        margin-bottom: 30px;
    }

    .phone-number {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        font-size: 20px;
        color: #2c3e50;
        font-weight: bold;
        background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        padding: 15px 25px;
        border-radius: 15px;
        margin: 0 auto;
        max-width: 300px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .phone-number i {
        color: #667eea;
        font-size: 24px;
    }

    .social-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .contact-btn {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 15px 30px;
        border-radius: 50px;
        text-decoration: none;
        font-weight: bold;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        min-width: 150px;
        justify-content: center;
    }

    .call-btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
    }

    .call-btn:hover {
        background: linear-gradient(45deg, #5a6fd8, #6a4190);
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .whatsapp-btn {
        background: linear-gradient(45deg, #25D366, #128C7E);
        color: white;
    }

    .whatsapp-btn:hover {
        background: linear-gradient(45deg, #20b358, #0e7a6e);
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(37, 211, 102, 0.4);
        color: white;
    }

    .contact-btn i {
        font-size: 20px;
    }

    @media (max-width: 768px) {
        .about-container {
            margin: 20px;
            padding: 10px;
        }

        .developer-card, .contact-section {
            padding: 20px;
        }

        .social-buttons {
            flex-direction: column;
            align-items: center;
        }

        .contact-btn {
            width: 100%;
            max-width: 250px;
        }
    }
   </style>
   <body>
   <div class="about-container">
       <div class="developer-info">
           <div class="developer-card">
               <div class="developer-avatar">
                   <i class="fas fa-user-tie"></i>
               </div>
               <h2 class="developer-name">م.عبدالرحمن حسن الخفاجي</h2>
               <p class="developer-title">مطور ومصمم النظام</p>
               <div class="description">
                   <p>تم بناء وتصميم هذا البرنامج من قبل المبرمج</p>
                   <p>مبني البرنامج على متطلبات خاصة وبالإمكان التطوير على بيئة النظام حسب طلب المستخدم</p>
               </div>
           </div>
       </div>

       <div class="contact-section">
           <h3 class="contact-title">يمكنك التواصل معنا</h3>
           <div class="contact-info">
               <div class="phone-number">
                   <i class="fas fa-phone"></i>
                   <span>07719992716</span>
               </div>
           </div>

           <div class="social-buttons">
               <a href="tel:07719992716" class="contact-btn call-btn">
                   <i class="fas fa-phone"></i>
                   <span>اتصال</span>
               </a>
               <a href="https://wa.me/9647719992716" class="contact-btn whatsapp-btn">
                   <i class="fab fa-whatsapp"></i>
                   <span>واتساب</span>
               </a>
           </div>
       </div>
   </div>
</body>
<script src="js/all.js" crossorigin="anonymous"></script>
</body>
</html>