<?php
session_start();
if (isset($_SESSION['user'])) {
  if ($_SESSION['user']->role === "User") {
  } else {
    header("location:../login.php", true);
    die("");
    echo "dont work";
  }
} else {
  header("location:../login.php", true);
  die("");
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>عرض حضور الموظفين</title>
  <link rel="stylesheet" href="css/bootstrap.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/all.min.css">
  <script src="js/all.min.js"></script>
  <link rel="icon" href="css/icon.ico">
  <script src="js/jquery.min.js"></script>
  <script src="js/jquery.dataTables.min.js"></script>
  <link rel="stylesheet" href="css/jquery.dataTables.min.css">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <?php include "addon/topbar.php" ?>
  <?php include "addon/dbcon.php" ?>
  
  <style>
    /* تنسيق الـ tooltip */
    .leave-tooltip {
      position: relative;
      cursor: pointer;
    }
    
    .leave-tooltip .tooltiptext {
      visibility: hidden;
      width: 200px;
      background-color: #333;
      color: #fff;
      text-align: center;
      border-radius: 6px;
      padding: 8px;
      position: absolute;
      z-index: 1;
      bottom: 125%;
      left: 50%;
      margin-left: -100px;
      opacity: 0;
      transition: opacity 0.3s;
      font-size: 12px;
    }
    
    .leave-tooltip .tooltiptext::after {
      content: "";
      position: absolute;
      top: 100%;
      left: 50%;
      margin-left: -5px;
      border-width: 5px;
      border-style: solid;
      border-color: #333 transparent transparent transparent;
    }
    
    .leave-tooltip:hover .tooltiptext {
      visibility: visible;
      opacity: 1;
    }
  </style>
</head>

<body id="td">
<div class="search">
    <button class="btn btn-secondary text-light ml-10" id="show" type="submit"> تسجيل حضور الموظفين</button> 
</div>

<div class="search d-flex align-items-center mb-3">
   <form action="" method="POST" class="d-flex">
     <button name="show_data" type="submit" class="btn btn-secondary">إظهار البيانات</button>
   </form>
</div>

  <div class="wrapper2" id="tost_info">
    <div id="toast2">
      <div class="container-11">
        <i id="icon" class="fa-solid fa-circle-info"></i>
      </div>
      <div class="container-22">
        <p class="p1">Done !</p>
        <p class="p2">تم اضافة البينانات</p>
      </div>
    </div>
  </div>

  <table class="table" id="Table">
    <thead>
      <tr>
        <th scope="col">عمليات الحضور والغياب</th>
        <th scope="col">اسم المستخدم</th>
        <th scope="col">التاريخ</th>
        <th scope="col">اسم الموظف</th>
      </tr>
    </thead>

    <tbody id="myTable">

<?php
$datenow = date('Y-m-d');

// إذا تم الضغط على زر "إظهار البيانات"
if (isset($_POST['show_data'])) {
  // الحصول على ID المستخدم الحالي من الجلسة
  $current_user_id = $_SESSION['user']->id_user;
  
  // جلب جميع سجلات الحضور والغياب للمستخدم الحالي فقط
  $sql = "SELECT stat2.data_stat, stat2.id_employee, stat2.stat_employee, stat2.leave_start_date, stat2.leave_end_date,
                 employ_tb.f_name, users_tb.user_name, employ_tb.id_employ
          FROM stat2 
          INNER JOIN employ_tb ON stat2.id_employee = employ_tb.id_employ
          LEFT JOIN users_tb ON employ_tb.userID = users_tb.id_user 
          WHERE employ_tb.userID = '$current_user_id'
          ORDER BY stat2.data_stat DESC";
  
  $result = mysqli_query($con, $sql);
  
  if ($result && mysqli_num_rows($result) > 0) {
    while ($row = mysqli_fetch_assoc($result)) {
      $id = $row['id_employee'];
      $attendance_status = $row['stat_employee'];
      $leave_start = $row['leave_start_date'];
      $leave_end = $row['leave_end_date'];
?>

      <tr id="tr_<?php echo $id ?>_<?php echo $row['data_stat'] ?>" class="sm" value="<?php echo $attendance_status ?>">
        <td>
          <?php 
          if($attendance_status == 'حاضر') { 
            echo '<p style="color: green; font-weight: bold;">تم تسجيل الحضور</p>';
          } elseif($attendance_status == 'غائب') {
            echo '<p style="color: red; font-weight: bold;">تم تسجيل الغياب</p>';
          } elseif($attendance_status == 'إجازة') {
            if($leave_start && $leave_end) {
              echo '<div class="leave-tooltip">
                      <p style="color: orange; font-weight: bold;">إجازة</p>
                      <span class="tooltiptext">فترة الإجازة:<br>من: ' . $leave_start . '<br>إلى: ' . $leave_end . '</span>
                    </div>';
            } else {
              echo '<p style="color: orange; font-weight: bold;">إجازة</p>';
            }
          }
          ?>
          <i id="remove" class="fa-solid fa-trash-can" onclick="removeEmployee('<?php echo $id ?>_<?php echo $row['data_stat'] ?>', <?php echo $id ?>, '<?php echo $row['data_stat'] ?>')" style="color: #dc3545; cursor: pointer; margin-left: 10px;"></i>
        </td>
        <td><?php echo $row['user_name'] ?></td>
        <td><?php echo $row['data_stat'] ?></td>
        <td><?php echo $row['f_name'] ?></td>
      </tr>

<?php
    }
  } else {
    // عرض رسالة إذا لم توجد سجلات
    echo "<tr><td colspan='4' style='text-align: center; color: #666;'>لا توجد سجلات حضور</td></tr>";
  }
} else {
  // عرض رسالة توضيحية قبل الضغط على زر إظهار البيانات
  echo "<tr><td colspan='4' style='text-align: center; color: #666;'>اضغط على زر 'إظهار البيانات' لعرض سجلات الحضور</td></tr>";
}
?>
    </tbody>
  </table>

<script>
$("#show").click(function () { 
    location.href = "employee_attendance.php";
});

function removeEmployee(rowId, employeeId, date) {
  // تأكيد الحذف
  if(confirm('هل أنت متأكد من حذف هذا السجل؟')) {
    $.ajax({
      method: "post",
      url: "./addon/employee_code2.php",
      data: {
        employee_id: employeeId,
        record_date: date
      },
      success: function (data) {
        console.log('Response:', data);
        if(data == "done") {
          jQuery("#tr_" + rowId).css("background","#dc354554");
          jQuery("#tr_" + rowId).hide(2000);
          // عرض رسالة نجاح
          StudToast('8px solid rgb(3, 188, 77)','rgb(3, 188, 77)'," ! تم الحذف ","   تم حذف السجل بنجاح ","fa fa-circle-check");
        } else if(data == "not_found") {
          StudToast('8px solid rgb(220, 53, 69)','rgb(220, 53, 69)'," ! خطأ ","   السجل غير موجود ","fa-solid fa-circle-exclamation");
        } else {
          StudToast('8px solid rgb(220, 53, 69)','rgb(220, 53, 69)'," ! خطأ ","   حدث خطأ في الحذف ","fa-solid fa-circle-exclamation");
        }
      },
      error: function() {
        StudToast('8px solid rgb(220, 53, 69)','rgb(220, 53, 69)'," ! خطأ ","   حدث خطأ في الاتصال ","fa-solid fa-circle-exclamation");
      }
    });
  }
}

// Toast function
let x;
let toast = document.getElementById("toast2");
p1 = document.querySelector(".p1");
p2 = document.querySelector(".p2");

function StudToast(ts, ic, tx1, tx2, icC) {
    let icon = document.getElementById("icon");
    clearTimeout(x);
    icon.className = icC;
    toast.style.borderRight = ts;
    icon.style.color = ic;
    p1.innerText = tx1;
    p2.innerText = tx2;
    toast.style.transition = '1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition = '1s';
    x = setTimeout(() => {
        toast.style.transform = "translateX(-500px)";
    }, 4200);
}
</script>

<script>
$(document).ready(function() {
    // التأكد من وجود الجدول قبل تطبيق DataTable
    if($("#Table tbody tr").length > 0 && !$("#Table tbody tr td").first().attr('colspan')) {
        $("#Table").DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "order": [[ 2, "desc" ]], // ترتيب حسب التاريخ (الأحدث أولاً) - العمود الثالث الآن
            "columnDefs": [
                { "orderable": false, "targets": 0 } // منع ترتيب عمود العمليات
            ]
        });
    }
});
</script>

</body>
</html>

 