<?php
ob_start();
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}


?>
<table>
  <tr>
      <td scope="col">قيمة الاشتراك</td>
      <td scope="col">تاريخ النفاذ </td>
      <td scope="col">تاريخ الاشتراك</td>
      <td scope="col">رقم ولي الامر</td>
      <td scope="col">اسم ولي الامر</td>
      <td scope="col">صنف التسجيل</td>
      <td scope="col">السكن</td>
      <td scope="col">الجنس</td>
      <td scope="col"> العمر</td>
      <td scope="col">اسم الطالب  </td>
      <td scope="col">رقم   الوصل </td>
      <td scope="col">ايدي  الطالب </td>
      <td scope="col">المستخدم </td>
  </tr>
<?php
include 'dbcon.php';
$dates=$_GET['dates'];
$datee=$_GET['datee'];


$rows = mysqli_query($con, $query="SELECT * FROM stud_tb,stud_pay,users_tb WHERE  stud_pay.id_stud=stud_tb.id AND stud_tb.userID=users_tb.id_user AND DATE(stud_tb.datein) BETWEEN '$dates' AND '$datee'");
foreach($rows as $row) :
?><tr>
  
  <td> <?php echo number_format($row['cash_stud']); ?> </td>
  <td> <?php echo $row['date_exp']; ?> </td>
  <td> <?php echo $row['datein']; ?> </td>
  <td> <?php echo $row['p_phone']; ?> </td>
  <td> <?php echo $row['p_name']; ?> </td>
  <td> <?php echo $row['catg']; ?> </td>
  <td> <?php echo $row['loc']; ?> </td>
  <td> <?php echo $row['sex']; ?> </td>
  <td> <?php echo $row['age']; ?> </td>
  <td> <?php echo $row['name']; ?> </td>
  <td> <?php echo $row['id_pay']; ?> </td>
  <td> <?php echo $row['id_note']; ?> </td>
  <td> <?php echo $row['user_name']; ?> </td>
    
    
    
  </tr>
<?php endforeach;

header("Content-Type: application/vnd.ms-excel");
header("Content-Disposition: attachment; Filename = DataStud.xls");
ob_end_flush()

?>