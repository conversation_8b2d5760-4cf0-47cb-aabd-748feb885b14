-- جدول الاحتياجات
CREATE TABLE IF NOT EXISTS needs_requests (
    id_need INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    need_name VARCHAR(255) NOT NULL,
    request_date DATE NOT NULL,
    need_type ENUM('احتياج', 'صيانة') NOT NULL,
    need_details TEXT NOT NULL,
    status ENUM('قيد المراجعة', 'تم التوفير', 'لم يتوفر') DEFAULT 'قيد المراجعة',
    admin_response TEXT NULL,
    response_date DATETIME NULL,
    response_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users_tb(id_user),
    FOREIGN KEY (response_by) REFERENCES users_tb(id_user)
);

-- جدول طلبات الإجازة
CREATE TABLE IF NOT EXISTS leave_requests (
    id_leave INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    employee_name VARCHAR(255) NOT NULL,
    request_date DATE NOT NULL,
    leave_type ENUM('مرضية', 'عرضية', 'طارئة', 'زمنية', 'ظروف أخرى') NOT NULL,
    leave_details TEXT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    days_count INT NOT NULL,
    status ENUM('قيد المراجعة', 'موافق', 'غير موافق') DEFAULT 'قيد المراجعة',
    admin_response TEXT NULL,
    response_date DATETIME NULL,
    response_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users_tb(id_user),
    FOREIGN KEY (response_by) REFERENCES users_tb(id_user)
);
