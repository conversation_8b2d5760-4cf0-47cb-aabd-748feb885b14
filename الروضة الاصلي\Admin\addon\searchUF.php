<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:http://localhost/roda/login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:http://localhost/roda/login.php",true);
        die("");
}

include "dbcon.php";
$sm=$_GET['input'];
echo $sm;
      if(isset($_GET['input'])){
        $search=$_GET['input'];
      
        $query="SELECT * FROM users_tb WHERE  CONCAT(user_name,user_pass,role) LIKE '%$search%'";
        $query_run=mysqli_query($con,$query);
        if(mysqli_num_rows($query_run)>0){
          foreach($query_run as $items){
            $id=$items['id_user'];
        
            ?>
            <tr id="tr_<?php echo $id ?>">
            <td><button type="button" class="btn btn-secondary mb-1"id="edit_bnt" style="text-decoration: none;color:aliceblue;"  >  <a href="../Admin/edit_user.php?id=<?php echo $id; ?>" style="text-decoration: none;color:aliceblue;">تعديل</a></button> 
            <button type="button" class="btn btn-secondary mb-1" onclick="deletdata(<?php echo $id ?>)" >حذف </button></td>
            <td><?= $items['role'];?></td>
            <td><?= $items['user_pass'];?></td>
            <td><?= $items['user_name'];?></td> 
          </tr>
      <?php
          }
        }else{
         
          echo "<td colspan=4 style='font-size: 25px;'>لاتوجد   معلومات بهذا الوصف </td>";
  
        }
      }
  
  
  
      ?>