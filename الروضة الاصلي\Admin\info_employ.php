<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معلومات الموظفين</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
   </head>
    <body>
    <form action="" metho="POST">
  <div class="search">
  <select name="users" id="selc" placeholder='اختر مستخدم'>
        <option value="0" selected >اختر احد المستخدمين</option>
            <?php
            $sql="SELECT * FROM users_tb ";
            $result=mysqli_query($con,$sql);
            if($result){
                while($row=mysqli_fetch_assoc($result)){
                    $user_name=$row['user_name'];
                    $id=$row['id_user'];
                echo '<option value='.$id.'>'.$user_name.'</option>';
                }
            }
            ?>   
        </select>
  <button class="btn btn-success text-light" name="sub"  ><a href="../Admin/addon/expor_employ.php">تحميل اكسل</a> </button>
  <button class="btn btn-secondary text-light" name="sub"  ><a href="search_employ.php">بحث </a> </button>
  <button class="btn btn-secondary text-light" name="sub"  ><a href="addemploy.php">اضافة موظف</a> </button>
  
    
    
  </div>
  </form>

    <table class="table">
  <thead>
    <tr>
    
      <th scope="col"> العمليات</th>
      <th scope="col"> تابع الى</th>
      <th scope="col"> الراتب الشهري</th>
      <th scope="col"> السكن</th>
      <th scope="col">تاريخ المباشرة</th>
      <th scope="col"> العنوان الوظيفي   </th>
      <th scope="col">  تاريخ الميلاد</th>
      <th scope="col"> اسم الموظف الثلاثي</th>
   
    </tr>
  </thead>
  <tbody id="myTable">

   
  </tbody>
</table>
<section id="deletmodle">
      <div class="modal-box">
        <h2>! تنبيه </h2>
        <h3>هل انته متأكد من عملية الحذف كون البيانات لايمكن استرجاعها</h3>

        <div class="buttons-method">
          <button class="close-btn">اغلاق</button>
         <button name="remov" id="rm" class="remove-btn">حذف</button>
        </div>
      </div>
    </section>


   </body>
   <script>
        function deletdata(id){
        $("#deletmodle").addClass("active");
        //console.log(id)
        $(".table").addClass("active");
        $(".close-btn").click(function () { 
          $(".table").removeClass("active")
          $("#deletmodle").removeClass("active")
        });
        $("#rm").click(function () { 
          $(".table").removeClass("active")
           //console.log(id)
          $.ajax({url:'addon/reomve_employ.php',
          method:"POST",
          data:({name:id}),
          success:function(response){
            console.log(response)
          $("#deletmodle").removeClass("active");
          jQuery("#tr_"+id).css("background","#eaeaea");
          jQuery("#tr_"+id).hide(2000);
          
          
        

         }
        });
        });
      }
    
    </script>
   <script>
    $("#selc").change(function(){
      var selc =$(this).val();
     // console.log(selc)
        $.ajax({
          method: "POST",
          url: "addon/infoemployF.php",
          data: {id:selc},
          success: function (data) {
            $("#myTable").html(data);
          }
        
        })
      });
</script>

</html>