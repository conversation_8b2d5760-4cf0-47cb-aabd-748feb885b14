<?php
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:http://localhost/roda/login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:http://localhost/roda/login.php",true);
        die("");
}
?>
<?php
    if(isset($_POST['myInput']))
    { 
        $id=$_POST['acout_type'];
        $dates=$_POST['dates'];
        $datee=$_POST['datee'];
      $query2="SELECT * FROM users_tb,depit_tb WHERE users_tb.id_user=$id AND depit_tb.userID=users_tb.id_user AND DATE(depit_tb.depit_date2) BETWEEN '$dates' AND '$datee'";
      $query_run2=mysqli_query($con,$query2);
      $count2=0;
      foreach($query_run2 as $items2){
         $depit_cash= $items2['depit_cash'];
         $count2=$count2+$depit_cash;
      }echo '
      <div class="acount">
      <label for="" > IQD '.number_format($count2).'  : مجموع المصروفات من الحضانة </label>
      </div>
      
      
      ';
         
      $query=$query="SELECT * FROM stud_tb,stud_pay,users_tb WHERE users_tb.id_user=stud_tb.userID AND stud_pay.id_stud=stud_tb.id AND users_tb.id_user=$id AND DATE(stud_tb.datein) BETWEEN '$dates' AND '$datee'";
      $query_run=mysqli_query($con,$query);
      $count=0;
      foreach($query_run as $items){
          $cash_stud= $items['cash_stud'];
          $count=$count+$cash_stud;
          
           }echo '
            <div class="acount2">
            <label for="" >  IQD '.number_format($count).': مجموع الايرادات من الحضانة </label>
            </div>
           
           ';
        
        }
        $query=$query="SELECT * FROM employ_tb,users_tb WHERE users_tb.id_user=employ_tb.userID ";
        $query_run=mysqli_query($con,$query);
        $count=0;
        foreach($query_run as $items){
            $cash_stud= $items['salary'];
            $count=$count+$cash_stud;
            
             }echo '
              <div class="acount2">
              <label for="" >  IQD '.number_format($count).': مجموع رواتب الموظفين للحضانة </label>
              </div>
             
             ';
          
          
  
?>



 