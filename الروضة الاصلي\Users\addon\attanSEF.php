<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

include "dbcon.php";
$datenow = date('Y-m-d');
$Date2 = date('Y-m-d', strtotime($datenow . ' + 1 days'));
$Date4 = date('Y-m-d', strtotime($datenow . ' + 365 days'));   
 if(isset($_GET['input']))
    {
      $Filter=$_GET['input'];
      $query="SELECT * FROM stud_tb,stud_pay,users_tb WHERE DATE(stud_pay.date_exp) BETWEEN date('$Date2')AND Date('$Date4') AND stud_tb.id=stud_pay.id_stud AND users_tb.id_user={$_SESSION['user']->id_user} AND CONCAT(name,p_name) LIKE '%$Filter%'";
      $query_run=mysqli_query($con,$query);
      ?>
     <thead>
    <tr>
      <th scope="col">  عمليات الحضور  </th>
      <th scope="col"> مستخدم الحضانة  </th>
      <th scope="col">صنف التسجيل</th>
      <th scope="col">اسم ولي الامر</th>
      <th scope="col">اسم الطالب  </th>
      
    </tr>
  </thead>
  <?php
      if(mysqli_num_rows($query_run)>0){
        foreach($query_run as $items){
          $id=$items['id'];
          $id = $items['id'];
          $id_pay = $items['id_pay'];
          $id_note = $items['id_note'];
          $name = $items['name'];
          $age = $items['age'];
          $sex = $items['sex'];
          $catg = $items['catg'];
          $datein = $items['datein'];
          $p_name = $items['p_name'];
          $p_phone = $items['p_phone'];
          $loc = $items['loc'];
          $date_exp = $items['date_exp'];
          $cash_stud = number_format($items['cash_stud']);
          $user_name = $items['user_name'];
          $id_pay = $items['id_pay'];
          $date_in = strtotime(date('y-m-d'));
          $date_out = strtotime($date_exp);
          $stat = $date_out - $date_in;
          $cek = floor($stat / (60 * 60 * 24));
          ?>
           <tr>
            <td>
                <div class="attandes">
                    <input type="checkbox" name="out" id="out<?php echo $id ?>" value="غائب" onclick="chekedout(<?php echo $id ?>)"><label for="">غائب</label>
                    <input type="checkbox" name="late" id="late<?php echo $id ?>" value="متأخر" onclick="chekedlate(<?php echo $id ?>)"><label for="">متأخر</label>
                    <input type="checkbox" name="in" id="in<?php echo $id ?>" value="حاضر" onclick="chekedin(<?php echo $id ?>)"><label for="">حاضر</label>
                </div>
            </td>
            <td> <?php echo $user_name ?> </td>
            <td><?php echo $catg ?></td>
            <td><?php echo $p_name ?></td>
            <td><?php echo $name ?></td>
        </tr>

        <?php
 

        }
    }else{
        echo "<td colspan=4 style='font-size: 25px;'>لاتوجد بيانات بهذا الوصف</td>";
    }
    }


?>