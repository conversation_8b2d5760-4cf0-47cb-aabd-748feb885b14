<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title> تقارير الفعالين </title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
      

   </head>
   <body>
   <form action="" method="POST">
  <div class="search_ac">
    <input name="datee" type="date" required>
    <label for=""> الى تاريخ</label>
    <input name="dates" type="date" required>
    <label for="">اختر من تاريخ</label>
    <button class="btn btn-warning" name='myInput'  > اظهار</button> 
    <?php
    if(isset($_POST['myInput'])){
      $dates=$_POST['dates'];
      $datee=$_POST['datee'];
   echo ' <button class="btn btn-success text-light" name="sub"  > <a href=addon/exportStudAc.php?dates='.$dates.'&datee='.$datee.' class="text-light">تحميل اكسل</a></button>';
   echo ' <button class="btn btn-primary text-light" onclick="printReportsTable()">طباعة</button>';
  }

  ?>
    </div>


    <?php
      $SM="SELECT * FROM users_tb ";
      $RM=mysqli_query($con,$SM);
      while($items=mysqli_fetch_assoc($RM)){
       $id= $items['id_user'];
      
      }
      #$HM=mysqli_num_rows($RM);
     
    if(isset($_POST['myInput']))
    {
      
      for($connn=0; $connn<=$id ;$connn++){
       
      $dates=$_POST['dates'];
      $datee=$_POST['datee'];
      $query="SELECT * FROM stud_tb,stud_pay,users_tb WHERE  stud_pay.id_stud=stud_tb.id AND stud_tb.userID=$connn AND DATE(stud_tb.datein) BETWEEN '$dates' AND '$datee'AND users_tb.id_user=$connn";
      $query_run=mysqli_query($con,$query);
      if(mysqli_num_rows($query_run)>0){

        
        ?>
        <table  id="Table<?php echo $connn ?>" class="table">
        <thead>
          <tr>
          <th scope="col"> عدد الطلاب : <?php echo mysqli_num_rows($query_run); ?></th>
            <th scope="col"> مستخدم الحضانة </th>
            <th scope="col">تاريخ  التسجيل</th>
            <th scope="col">قيمة الاشتراك</th>
            <th scope="col">اسم  الطالب</th>
            <th scope="col">رقم   الوصل </th>
            
          </tr>
          </thead>
        <tbody>
          <?php
          
          while($items=mysqli_fetch_assoc($query_run)){

          ?>
          <td disabled>فعال</td>
          <td><?= $items['user_name'];?></td>
          <td><?= $items['datein'];?></td>
          <td> IQD <?= number_format($items['cash_stud']);?></td>
          <td><?= $items['name'];?></td>
          <td><?= $items['id_pay'];?></td>
          
         </tbody>
 
 
         
        <?php
 
        
        }
      }
    }
    
    
  }   
     /*   echo "<h3 class=aades>IQD مجموع الايرادات لهذه الفترة : ".number_format($count)."  </h3>";
       
      }else{
       
        echo "<h3 class=aades-note> لاتوجد ايرادات  لهذا التاريخ</h3>";
      }
    }*/
  
    ?>

<script>
function printReportsTable(){
    // إنشاء نافذة جديدة للطباعة
    var printWindow = window.open('', '_blank');

    // الحصول على البيانات من الجدول
    var tableContent = document.getElementById('tb').outerHTML;

    // إنشاء محتوى HTML للطباعة
    var printContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تقرير الطلاب حسب التاريخ</title>
        <style>
            @font-face {
                font-family:"LamaSans-Medium";
                src: url(css/JannaLT.ttf);
            }

            body {
                font-family: "LamaSans-Medium", Arial, sans-serif;
                direction: rtl;
                margin: 20px;
                background: white;
            }

            .print-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #333;
                padding-bottom: 20px;
            }

            .print-header h1 {
                color: #333;
                margin-bottom: 10px;
                font-size: 24px;
            }

            .print-header p {
                color: #666;
                margin: 5px 0;
                font-size: 14px;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                font-size: 12px;
            }

            table th, table td {
                border: 1px solid #333;
                padding: 8px;
                text-align: center;
            }

            table thead {
                background-color: #f8f9fa;
                font-weight: bold;
            }

            table thead th {
                background-color: #333;
                color: white;
                font-weight: bold;
            }

            .still {
                color: green;
                font-weight: bold;
            }

            .exp {
                color: red;
                font-weight: bold;
            }

            .soon {
                color: orange;
                font-weight: bold;
            }

            .print-footer {
                margin-top: 30px;
                text-align: center;
                font-size: 12px;
                color: #666;
                border-top: 1px solid #ccc;
                padding-top: 10px;
            }

            @media print {
                body { margin: 0; }
                .print-header { page-break-inside: avoid; }
                table { page-break-inside: auto; }
                tr { page-break-inside: avoid; page-break-after: auto; }
            }
        </style>
    </head>
    <body>
        <div class="print-header">
            <h1>تقرير الطلاب حسب التاريخ</h1>
            <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</p>
            <p>وقت الطباعة: ${new Date().toLocaleTimeString('ar-EG')}</p>
        </div>

        ${tableContent}

        <div class="print-footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الروضة</p>
        </div>
    </body>
    </html>
    `;

    // كتابة المحتوى في النافذة الجديدة
    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى ثم طباعة
    printWindow.onload = function() {
        printWindow.print();
        printWindow.close();
    };
}
</script>

   </body>
</html>