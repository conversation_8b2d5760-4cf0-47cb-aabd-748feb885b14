<?php
session_start();
if (isset($_SESSION['user'])) {
    if ($_SESSION['user']->role === "User") {
    } else {
        header("location:../login.php", true);
        die("");
        echo "dont work";
    }
} else {
    header("location:../login.php", true);
    die("");
}

include "dbcon.php";
$datenow = date('Y-m-d');
$Date2 = date('Y-m-d', strtotime($datenow . ' + 1 days'));
$Date4 = date('Y-m-d', strtotime($datenow . ' + 365 days'));
$idGet = $_GET['id'];
$sql = "SELECT * FROM stud_tb,stud_pay,users_tb WHERE DATE(stud_pay.date_exp) BETWEEN date('$Date2')AND Date('$Date4') AND stud_tb.id=stud_pay.id_stud AND users_tb.id_user=$idGet ";
$result = mysqli_query($con, $sql);
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $id = $row['id'];
        $id_pay = $row['id_pay'];
        $id_note = $row['id_note'];
        $name = $row['name'];
        $age = $row['age'];
        $sex = $row['sex'];
        $catg = $row['catg'];
        $datein = $row['datein'];
        $p_name = $row['p_name'];
        $p_phone = $row['p_phone'];
        $loc = $row['loc'];
        $date_exp = $row['date_exp'];
        $cash_stud = number_format($row['cash_stud']);
        $user_name = $row['user_name'];
        $id_pay = $row['id_pay'];
        $date_in = strtotime(date('y-m-d'));
        $date_out = strtotime($date_exp);
        $stat = $date_out - $date_in;
        $cek = floor($stat / (60 * 60 * 24));
        if ($cek <= 0) {
            $mes = '<h3 class=exp>منتهي</h3>';
        } elseif ($cek <= 10 & $cek > 0) {
            $mes = '<h3 class=soon >قريبا</h3>';
        } else {
            $mes = '<h3 class=still >فعال</h3>';
        }

?>
        <tr>
            <td>
                <div class="attandes">
                    <input type="checkbox" name="out" id="out<?php echo $id ?>" value="غائب" onclick="chekedout(<?php echo $id ?>)"><label for="">غائب</label>
                    <input type="checkbox" name="late" id="late<?php echo $id ?>" value="متأخر" onclick="chekedlate(<?php echo $id ?>)"><label for="">متأخر</label>
                    <input type="checkbox" name="in" id="in<?php echo $id ?>" value="حاضر" onclick="chekedin(<?php echo $id ?>)"><label for="">حاضر</label>
                </div>
            </td>
            <td> <?php echo $user_name ?> </td>
            <td><?php echo $catg ?></td>
            <td><?php echo $p_name ?></td>
            <td><?php echo $name ?></td>
        </tr>

<?php

    }
}
?>
<script>
    function chekedout(id) {
        if ($("#out" + id).prop('checked') == true) {
            $("#in" + id).prop('disabled', true)
            $("#late" + id).prop('disabled', true)
        } else {
            $("#in" + id).prop('disabled', false)
            $("#late" + id).prop('disabled', false)
        }
    }
    function chekedlate(id) {
        if ($("#late" + id).prop('checked') == true) {
            $("#in" + id).prop('disabled', true)
            $("#out" + id).prop('disabled', true)
        } else {
            $("#in" + id).prop('disabled', false)
            $("#out" + id).prop('disabled', false)
        }
    }
    function chekedin(id) {
        if ($("#in" + id).prop('checked') == true) {
            $("#out" + id).prop('disabled', true)
            $("#late" + id).prop('disabled', true)
        } else {
            $("#out" + id).prop('disabled', false)
            $("#late" + id).prop('disabled', false)
        }
    }
</script>
<?php
?>