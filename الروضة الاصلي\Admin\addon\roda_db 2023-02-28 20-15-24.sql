
-- Database Backup --
-- Ver. : 1.0.1
-- Host : 127.0.0.1
-- Generating Time : Feb 28, 2023 at 20:15:24:PM



CREATE TABLE `depit_tb` (
  `id` int(100) NOT NULL AUTO_INCREMENT,
  `userID` int(100) NOT NULL,
  `depit_note` varchar(250) NOT NULL,
  `depit_date` date NOT NULL,
  `depit_date2` date NOT NULL,
  `depit_cash` float NOT NULL,
  PRIMARY KEY (`id`),
  KEY `userID` (`userID`),
  CONSTRAINT `depit_tb_ibfk_1` FOREIGN KEY (`userID`) REFERENCES `users_tb` (`id_user`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4;


INSERT INTO depit_tb VALUES
("22","9","مسواك +لبن +ماء","2023-01-22","2023-01-22","17500"),
("24","7","مسواك-لبن -ماء","2023-01-19","2023-01-23","20000"),
("25","11","مسولك - مي - وصل مسح - طباخ - بند ورق - اقلام - فوامه - ديتول - مناديل رطبه - فواكه ","2023-01-10","2023-01-13","138000"),
("27","11","زيت الدار - اكياس نفايات - اصباغ تلوين- بند ورق - مسواك ","2023-01-19","2023-01-26","36750");




CREATE TABLE `employ_tb` (
  `id_employ` int(11) NOT NULL AUTO_INCREMENT,
  `f_name` varchar(250) NOT NULL,
  `b_date` date NOT NULL,
  `job` varchar(250) NOT NULL,
  `date_start` date NOT NULL,
  `location` varchar(250) NOT NULL,
  `salary` float NOT NULL,
  `userID` int(100) NOT NULL,
  PRIMARY KEY (`id_employ`),
  KEY `userID` (`userID`),
  CONSTRAINT `employ_tb_ibfk_1` FOREIGN KEY (`userID`) REFERENCES `users_tb` (`id_user`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4;






CREATE TABLE `stat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_stud` int(11) NOT NULL,
  `stat_stud` varchar(250) NOT NULL,
  `data_stat` date NOT NULL,
  PRIMARY KEY (`id`),
  KEY `id_stud` (`id_stud`)
) ENGINE=InnoDB AUTO_INCREMENT=433 DEFAULT CHARSET=utf8mb4;


INSERT INTO stat VALUES
("337","670","حاضر","2023-02-23"),
("338","685","حاضر","2023-02-23"),
("339","681","حاضر","2023-02-23"),
("340","674","","2023-02-27"),
("341","659","","2023-02-27"),
("342","661","","2023-02-27"),
("343","660","","2023-02-27"),
("344","670","","2023-02-27"),
("345","685","","2023-02-27"),
("346","678","","2023-02-27"),
("347","676","","2023-02-27"),
("348","669","","2023-02-27"),
("349","671","","2023-02-27"),
("350","690","","2023-02-27"),
("351","679","","2023-02-27"),
("352","696","","2023-02-27"),
("353","699","","2023-02-27"),
("354","700","","2023-02-27"),
("355","701","","2023-02-27"),
("356","809","حاضر","2023-02-27"),
("357","807","حاضر","2023-02-27"),
("358","805","حاضر","2023-02-27"),
("359","802","حاضر","2023-02-27"),
("360","662","حاضر","2023-02-27"),
("361","686","حاضر","2023-02-27"),
("362","691","حاضر","2023-02-27"),
("363","698","حاضر","2023-02-27"),
("364","695","حاضر","2023-02-27"),
("365","684","حاضر","2023-02-27"),
("366","702","حاضر","2023-02-27"),
("367","706","حاضر","2023-02-27"),
("368","697","حاضر","2023-02-27"),
("369","707","حاضر","2023-02-27"),
("370","804","حاضر","2023-02-27"),
("371","803","حاضر","2023-02-27"),
("372","714","حاضر","2023-02-27"),
("373","712","حاضر","2023-02-27"),
("374","654","حاضر","2023-02-27"),
("375","655","حاضر","2023-02-27"),
("376","657","حاضر","2023-02-27"),
("377","658","حاضر","2023-02-27"),
("378","663","حاضر","2023-02-27"),
("379","668","حاضر","2023-02-27"),
("380","675","حاضر","2023-02-27"),
("381","687","حاضر","2023-02-27"),
("382","683","حاضر","2023-02-27"),
("383","694","حاضر","2023-02-27"),
("384","705","حاضر","2023-02-27"),
("385","708","حاضر","2023-02-27"),
("386","711","حاضر","2023-02-27"),
("387","713","حاضر","2023-02-27"),
("388","808","حاضر","2023-02-27"),
("389","679","","2023-02-28"),
("390","807","","2023-02-28"),
("391","660","حاضر","2023-02-28"),
("392","659","حاضر","2023-02-28"),
("393","809","","2023-02-28"),
("394","699","","2023-02-28"),
("395","802","","2023-02-28"),
("396","685","","2023-02-28"),
("397","690","","2023-02-28"),
("398","696","","2023-02-28"),
("399","700","","2023-02-28"),
("400","701","","2023-02-28"),
("401","669","حاضر","2023-02-28"),
("402","661","حاضر","2023-02-28"),
("403","670","حاضر","2023-02-28"),
("404","671","حاضر","2023-02-28"),
("405","674","حاضر","2023-02-28"),
("406","824","","2023-02-28"),
("407","710","","2023-02-28"),
("408","823","","2023-02-28"),
("409","676","حاضر","2023-02-28"),
("410","662","حاضر","2023-02-28"),
("411","686","حاضر","2023-02-28"),
("412","691","حاضر","2023-02-28"),
("413","695","حاضر","2023-02-28"),
("414","689","حاضر","2023-02-28"),
("415","697","حاضر","2023-02-28"),
("416","698","حاضر","2023-02-28"),
("417","706","حاضر","2023-02-28"),
("418","712","حاضر","2023-02-28"),
("419","707","حاضر","2023-02-28"),
("420","803","حاضر","2023-02-28"),
("421","804","حاضر","2023-02-28"),
("422","714","حاضر","2023-02-28"),
("423","687","حاضر","2023-02-28"),
("424","675","حاضر","2023-02-28"),
("425","668","حاضر","2023-02-28"),
("426","688","حاضر","2023-02-28"),
("427","694","حاضر","2023-02-28"),
("428","705","حاضر","2023-02-28"),
("429","711","حاضر","2023-02-28"),
("430","713","حاضر","2023-02-28"),
("431","808","حاضر","2023-02-28"),
("432","709","حاضر","2023-02-28");




CREATE TABLE `stud_pay` (
  `id_P` int(100) NOT NULL AUTO_INCREMENT,
  `id_pay` varchar(250) NOT NULL,
  `cash_stud` float NOT NULL,
  `date_exp` date NOT NULL,
  `id_stud` int(11) NOT NULL,
  PRIMARY KEY (`id_P`),
  KEY `id_stud` (`id_stud`),
  CONSTRAINT `stud_pay_ibfk_1` FOREIGN KEY (`id_stud`) REFERENCES `stud_tb` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=797 DEFAULT CHARSET=utf8mb4;


INSERT INTO stud_pay VALUES
("305","418","100000","2023-03-09","334"),
("306","428","100000","2023-03-15","335"),
("307","327","100000","2023-02-14","336"),
("308","457","75000","2023-03-22","337"),
("309","458","75000","2023-03-22","338"),
("310","453","100000","2023-03-21","339"),
("311","444","100000","2023-03-21","340"),
("312","477","75000","2023-03-24","341"),
("313","478","75000","2023-03-24","342"),
("314","433","100000","2023-03-17","343"),
("315","335","75000","2023-02-14","344"),
("316","341","75000","2023-02-14","345"),
("317","473","75000","2023-03-24","346"),
("318","474","75000","2023-03-24","347"),
("319","338","100000","2023-02-14","348"),
("320","339","100000","2023-02-14","349"),
("321","495","75000","2023-03-29","350"),
("322","460","100000","2023-03-22","351"),
("323","461","100000","2023-03-22","352"),
("324","344","100000","2023-02-14","353"),
("325","345","75000","2023-02-11","354"),
("326","346","75000","2023-02-11","355"),
("327","446","100000","2023-03-21","356"),
("328","452","100000","2023-03-21","357"),
("329","349","50000","2023-02-16","358"),
("330","469","75000","2023-03-23","359"),
("331","468","75000","2023-03-23","360"),
("332","470","50000","2023-03-23","361"),
("333","471","50000","2023-03-23","362"),
("334","489","100000","2023-03-30","363"),
("335","490","75000","2023-03-28","364"),
("336","491","75000","2023-03-28","365"),
("337","475","100000","2023-03-21","366"),
("338","485","75000","2023-03-21","367"),
("339","486","75000","2023-03-21","368"),
("340","496","100000","2023-03-24","369"),
("341","431","75000","2023-03-17","370"),
("342","432","75000","2023-03-17","371"),
("343","363","100000","2023-02-21","372"),
("344","482","100000","2023-03-25","373"),
("345","365","50000","2023-02-14","374"),
("346","436","100000","2023-03-21","375"),
("347","367","100000","2023-02-22","376"),
("348","467","75000","2023-03-24","377"),
("349","466","75000","2023-03-24","378"),
("350","370","100000","2023-02-14","379"),
("351","481","75000","2023-03-22","380"),
("352","480","75000","2023-03-22","381"),
("353","373","100000","2023-02-24","382"),
("354","494","100000","2023-03-29","383"),
("355","375","100000","2023-02-24","384"),
("356","479","100000","2023-03-24","385"),
("357","488","100000","2023-03-28","386"),
("358","493","100000","2023-03-28","387"),
("359","379","100000","2023-02-28","388"),
("360","380","75000","2023-02-28","389"),
("361","381","75000","2023-02-28","390"),
("362","384","75000","2023-03-01","391"),
("363","385","75000","2023-03-01","392"),
("364","386","100000","2023-03-01","393"),
("365","386","75000","2023-02-28","394"),
("366","388","75000","2023-02-28","395"),
("367","389","100000","2023-03-01","396"),
("368","390","100000","2023-02-27","397"),
("369","391","100000","2023-03-03","398"),
("370","392","100000","2023-02-24","399"),
("371","393","100000","2023-03-03","400"),
("372","394","100000","2023-03-03","401"),
("373","395","100000","2023-03-03","402"),
("374","396","100000","2023-03-03","403"),
("375","397","100000","2023-03-03","404"),
("376","398","100000","2023-03-03","405"),
("377","399","100000","2023-03-04","406"),
("378","400","100000","2023-03-01","407"),
("379","401","75000","2023-03-03","408"),
("380","402","75000","2023-03-03","409"),
("381","403","75000","2023-03-03","410"),
("382","404","100000","2023-03-04","411"),
("383","383","100000","2023-02-28","412"),
("384","405","100000","2023-03-07","413"),
("385","406","75000","2023-03-07","414"),
("386","407","75000","2023-03-07","415"),
("387","408","100000","2023-03-04","416"),
("388","409","75000","2023-03-07","417"),
("389","410","75000","2023-03-07","418"),
("390","411","75000","2023-03-07","419"),
("391","413","100000","2023-03-07","420"),
("392","414","75000","2023-03-11","421"),
("393","414","75000","2023-03-11","422"),
("394","416","100000","2023-03-09","423"),
("395","417","100000","2023-03-10","424"),
("396","418","100000","2023-03-09","425"),
("397","419","100000","2023-03-10","426"),
("398","420","100000","2023-03-09","427"),
("399","421","100000","2023-03-14","428"),
("400","422","100000","2023-03-14","429"),
("402","423","100000","2023-03-10","431"),
("403","424","100000","2023-03-17","432"),
("404","181","100000","2023-03-29","433"),
("405","87","100000","2023-02-24","434");
INSERT INTO stud_pay VALUES
("406","88","100000","2023-02-24","435"),
("407","89","100000","2023-02-24","436"),
("408","90","100000","2023-02-24","437"),
("409","91","100000","2023-02-24","438"),
("410","161","100000","2023-03-24","439"),
("411","93","75000","2023-02-21","440"),
("412","93","75000","2023-02-24","441"),
("413","94","100000","2023-02-24","442"),
("414","95","75000","2023-02-24","443"),
("415","169","100000","2023-03-28","444"),
("416","97","100000","2023-02-21","445"),
("417","98","100000","2023-02-24","446"),
("418","99","100000","2023-02-24","447"),
("419","100","100000","2023-02-24","448"),
("420","101","75000","2023-02-23","449"),
("421","101","75000","2023-02-23","450"),
("422","102","100000","2023-02-24","451"),
("423","103","100000","2023-02-24","452"),
("424","104","100000","2023-02-24","453"),
("425","105","100000","2023-02-24","454"),
("426","106","100000","2023-02-24","455"),
("427","177","75000","2023-03-28","456"),
("428","177","75000","2023-03-28","457"),
("429","108","100000","2023-02-24","458"),
("430","109","100000","2023-02-24","459"),
("431","110","100000","2023-02-24","460"),
("432","111","100000","2023-02-24","461"),
("433","112","100000","2023-02-24","462"),
("434","113","100000","2023-02-24","463"),
("435","114","100000","2023-02-24","464"),
("436","115","75000","2023-03-03","465"),
("437","115","75000","2023-03-03","466"),
("438","116","100000","2023-03-03","467"),
("439","117","75000","2023-02-24","468"),
("440","117","75000","2023-02-24","469"),
("441","118","100000","2023-02-24","470"),
("442","119","100000","2023-02-24","471"),
("443","120","100000","2023-02-24","472"),
("444","121","100000","2023-02-24","473"),
("445","122","100000","2023-03-07","474"),
("446","123","100000","2023-02-24","475"),
("447","124","100000","2023-03-07","476"),
("448","125","100000","2023-02-24","477"),
("449","126","100000","2023-02-24","478"),
("450","127","75000","2023-03-07","479"),
("451","127","75000","2023-02-24","480"),
("452","129","100000","2023-02-24","481"),
("453","130","100000","2023-02-24","482"),
("454","131","100000","2023-02-24","483"),
("455","132","100000","2023-02-24","484"),
("456","133","75000","2023-03-08","485"),
("457","133","75000","2023-02-24","486"),
("458","133","75000","2023-02-24","487"),
("460","134","100000","2023-02-24","489"),
("461","135","100000","2023-02-24","490"),
("462","128","100000","2023-02-24","491"),
("463","136","75000","2023-02-24","492"),
("464","136","75000","2023-03-11","493"),
("465","137","100000","2023-02-24","494"),
("466","138","100000","2023-03-14","495"),
("467","139","100000","2023-03-14","496"),
("468","373","100000","2023-02-24","497"),
("469","374","75000","2023-02-11","498"),
("470","374","75000","2023-02-11","499"),
("471","375","100000","2023-02-14","500"),
("472","377","100000","2023-02-16","501"),
("473","378","100000","2023-02-24","502"),
("474","379","100000","2023-02-24","503"),
("475","380","75000","2023-02-17","504"),
("476","380","75000","2023-02-17","505"),
("477","382","100000","2023-02-24","506"),
("478","383","100000","2023-02-22","507"),
("479","383","100000","2023-02-22","508"),
("480","384","100000","2023-02-19","509"),
("481","385","75000","2023-02-24","510"),
("482","385","75000","2023-02-24","511"),
("483","386","100000","2023-02-24","512"),
("484","288","75000","2023-02-24","513"),
("485","389","100000","2023-02-24","514"),
("486","388","75000","2023-02-24","515"),
("487","389","100000","2023-02-24","516"),
("488","390","100000","2023-02-28","517"),
("489","391","75000","2023-02-24","518"),
("490","391","75000","2023-02-24","519"),
("491","392","100000","2023-02-24","520"),
("493","393","100000","2023-02-24","522"),
("494","394","100000","2023-02-24","523"),
("495","395","100000","2023-02-24","524"),
("496","396","100000","2023-02-24","525"),
("497","397","100000","2023-03-09","526"),
("498","398","75000","2023-02-24","527"),
("499","398","75000","2023-02-24","528"),
("500","399","75000","2023-02-24","529"),
("501","399","75000","2023-02-24","530"),
("503","400","75000","2023-03-08","532"),
("504","400","75000","2023-03-08","533"),
("505","400","75000","2023-03-08","534"),
("506","503","100000","2023-02-24","535"),
("507","504","75000","2023-02-24","536"),
("508","504","75000","2023-02-24","537");
INSERT INTO stud_pay VALUES
("509","505","75000","2023-02-24","538"),
("510","505","75000","2023-02-24","539"),
("511","506","100000","2023-02-24","540"),
("512","507","75000","2023-02-24","541"),
("513","507","75000","2023-02-24","542"),
("514","508","100000","2023-02-24","543"),
("515","509","100000","2023-02-24","544"),
("516","510","100000","2023-02-24","545"),
("517","511","100000","2023-02-24","546"),
("518","513","100000","2023-02-24","547"),
("519","514","100000","2023-02-24","548"),
("520","576","100000","2023-03-17","549"),
("521","515","100000","2023-02-24","550"),
("522","516","100000","2023-02-24","551"),
("523","517","75000","2023-02-24","552"),
("524","586","75000","2023-03-22","553"),
("525","518","100000","2023-02-21","554"),
("526","519","100000","2023-02-24","555"),
("527","520","75000","2023-02-24","556"),
("528","520","75000","2023-02-24","557"),
("530","531","75000","2023-02-24","559"),
("531","530","75000","2023-02-24","560"),
("532","532","100000","2023-02-24","561"),
("533","533","100000","2023-02-24","562"),
("534","534","100000","2023-02-24","563"),
("535","535","100000","2023-02-24","564"),
("536","536","100000","2023-02-24","565"),
("537","537","75000","2023-02-24","566"),
("538","537","75000","2023-02-24","567"),
("539","587","75000","2023-03-24","568"),
("540","539","100000","2023-02-24","569"),
("541","540","100000","2023-02-24","570"),
("542","541","100000","2023-02-24","571"),
("543","542","100000","2023-02-24","572"),
("544","543","75000","2023-02-24","573"),
("545","543","75000","2023-02-24","574"),
("546","544","75000","2023-02-24","575"),
("547","544","75000","2023-02-24","576"),
("548","545","75000","2023-02-24","577"),
("549","545","75000","2023-02-24","578"),
("550","546","100000","2023-02-24","579"),
("551","547","75000","2023-02-24","580"),
("552","547","75000","2023-02-24","581"),
("553","547","75000","2023-02-24","582"),
("554","548","100000","2023-02-24","583"),
("555","549","100000","2023-02-24","584"),
("557","550","75000","2023-02-24","586"),
("558","550","75000","2023-02-24","587"),
("559","551","75000","2023-02-24","588"),
("560","551","75000","2023-02-24","589"),
("561","552","75000","2023-02-24","590"),
("562","552","75000","2023-02-24","591"),
("563","553","75000","2023-02-24","592"),
("564","553","75000","2023-02-24","593"),
("565","554","50000","2023-02-24","594"),
("566","555","75000","2023-02-24","595"),
("567","555","75000","2023-02-24","596"),
("569","556","100000","2023-02-24","598"),
("570","557","75000","2023-02-24","599"),
("571","557","75000","2023-02-24","600"),
("572","558","100000","2023-02-24","601"),
("573","521","75000","2023-02-24","602"),
("574","521","75000","2023-02-24","603"),
("576","523","75000","2023-02-24","605"),
("577","523","75000","2023-02-24","606"),
("578","524","75000","2023-02-24","607"),
("579","524","75000","2023-02-24","608"),
("580","525","100000","2023-02-24","609"),
("581","526","75000","2023-02-24","610"),
("582","526","75000","2023-02-24","611"),
("583","579","100000","2023-03-22","612"),
("584","528","75000","2023-02-24","613"),
("585","528","75000","2023-02-24","614"),
("586","529","75000","2023-02-24","615"),
("587","529","75000","2023-02-24","616"),
("588","530","100000","2023-02-24","617"),
("589","147","100000","2023-02-08","618"),
("590","148","75000","2023-02-10","619"),
("591","149","75000","2023-02-07","620"),
("592","149","75000","2023-02-07","621"),
("593","138","100000","2023-02-15","622"),
("594","150","75000","2023-02-10","623"),
("595","150","75000","2023-02-10","624"),
("596","150","75000","2023-02-10","625"),
("597","151","75000","2023-02-10","626"),
("598","151","75000","2023-02-10","627"),
("599","152","100000","2023-02-10","628"),
("600","153","100000","2023-02-11","629"),
("601","154","100000","2023-02-11","630"),
("602","157","75000","2023-02-04","631"),
("603","158","100000","2023-02-11","632"),
("604","156","100000","2023-02-05","633"),
("605","159","100000","2023-02-14","634"),
("606","160","100000","2023-02-11","635"),
("607","161","75000","2023-02-04","636"),
("608","161","75000","2023-02-04","637"),
("609","162","100000","2023-02-15","638"),
("610","163","100000","2023-02-15","639"),
("611","164","100000","2023-02-15","640"),
("612","165","100000","2023-02-16","641");
INSERT INTO stud_pay VALUES
("613","166","100000","2023-02-13","642"),
("614","167","100000","2023-02-16","643"),
("615","169","100000","2023-02-17","644"),
("616","170","100000","2023-02-21","645"),
("617","171","75000","2023-02-21","646"),
("618","172","100000","2023-02-21","647"),
("619","173","75000","2023-02-21","648"),
("620","173","75000","2023-02-21","649"),
("621","174","75000","2023-02-20","650"),
("622","174","75000","2023-02-20","651"),
("623","177","100000","2023-02-21","652"),
("624","178","100000","2023-01-13","653"),
("625","547","100","2023-03-13","654"),
("626","548","100","2023-03-14","655"),
("628","552","75","2023-03-14","657"),
("629","553","75","2023-03-14","658"),
("630","555","100","2023-03-21","659"),
("631","558","100","2023-03-21","660"),
("632","575","100","2023-03-21","661"),
("633","560","100","2023-03-19","662"),
("634","559","100","2023-03-21","663"),
("635","565","100","2023-03-23","664"),
("638","500","100000","2023-02-21","667"),
("639","562","75","2023-03-23","668"),
("640","564","75","2023-03-23","669"),
("641","563","75000","2023-03-23","670"),
("642","563","75000","2023-03-23","671"),
("643","505","75000","2023-02-22","672"),
("644","506","75000","2023-02-22","673"),
("645","567","75000","2023-03-25","674"),
("646","566","75000","2023-03-25","675"),
("647","561","100","2023-03-22","676"),
("648","510","100000","2023-02-24","677"),
("649","551","100","2023-03-19","678"),
("650","568","100","2023-03-25","679"),
("651","513","75000","2023-02-25","680"),
("652","514","75000","2023-02-25","681"),
("653","515","100000","2023-02-22","682"),
("654","516","75000","2023-02-28","683"),
("655","517","75000","2023-02-28","684"),
("656","571","75000","2023-03-30","685"),
("657","572","75000","2023-03-30","686"),
("658","520","100000","2023-03-01","687"),
("659","578","75000","2023-03-26","688"),
("660","579","75000","2023-03-26","689"),
("661","524","75000","2023-03-03","690"),
("662","525","75000","2023-03-03","691"),
("663","526","100000","2023-03-03","692"),
("664","527","100000","2023-03-02","693"),
("665","528","75000","2023-03-01","694"),
("666","529","75000","2023-03-01","695"),
("667","530","75000","2023-03-03","696"),
("668","531","75000","2023-03-03","697"),
("669","532","75000","2023-03-07","698"),
("670","533","75000","2023-03-07","699"),
("671","534","75000","2023-03-07","700"),
("672","535","75000","2023-03-07","701"),
("673","523","100000","2023-03-06","702"),
("674","546","75000","2023-03-10","703"),
("675","537","75000","2023-03-10","704"),
("676","538","75000","2023-03-10","705"),
("677","539","75000","2023-03-10","706"),
("678","539","75000","2023-03-10","707"),
("679","540","100000","2023-03-10","708"),
("680","541","75000","2023-03-11","709"),
("681","542","75000","2023-03-11","710"),
("682","543","75000","2023-03-11","711"),
("683","544","75000","2023-03-11","712"),
("684","545","75000","2023-03-11","713"),
("685","546","75000","2023-03-11","714"),
("686","720","100000","2023-03-15","715"),
("687","721","75000","2023-03-15","716"),
("688","722","75000","2023-02-24","717"),
("689","723","100000","2023-03-16","718"),
("690","721","100000","2023-03-20","719"),
("691","725","100000","2023-03-21","720"),
("692","726","100000","2023-03-21","721"),
("693","727","100000","2023-03-21","722"),
("694","728","100000","2023-03-21","723"),
("695","729","100000","2023-03-21","724"),
("696","730","75000","2023-03-21","725"),
("697","731","75000","2023-03-21","726"),
("698","732","100000","2023-03-21","727"),
("699","733","100000","2023-03-21","728"),
("700","743","75000","2023-03-21","729"),
("701","735","75000","2023-02-24","730"),
("702","736","100000","2023-03-23","731"),
("703","737","75000","2023-03-21","732"),
("704","000000","0","2023-03-21","733"),
("705","738","100000","2023-03-21","734"),
("706","739","100000","2023-03-21","735"),
("707","739","100000","2023-03-21","736"),
("708","740","100000","2023-03-21","737"),
("709","741","100000","2023-02-24","738"),
("710","742","100000","2023-03-21","739"),
("711","743","100000","2023-03-23","740"),
("712","744","75000","2023-03-20","741"),
("713","745","75000","2023-02-24","742"),
("714","746","100000","2023-02-24","743"),
("715","747","100000","2023-03-22","744");
INSERT INTO stud_pay VALUES
("716","748","100000","2023-03-22","745"),
("717","749","100000","2023-02-24","746"),
("718","750","100000","2023-03-27","747"),
("719","750","100000","2023-03-27","748"),
("720","1","100000","2023-03-28","749"),
("721","2","75000","2023-03-25","750"),
("722","3","75000","2023-02-24","751"),
("723","3","75000","2023-03-25","752"),
("724","3","75000","2023-03-25","753"),
("725","4","75000","2023-02-24","754"),
("726","5","75000","2023-03-25","755"),
("727","6","75000","2023-03-21","756"),
("728","7","75000","2023-02-24","757"),
("729","677","100000","2023-03-26","758"),
("730","678","100000","2023-02-22","759"),
("731","680","75000","2023-02-24","760"),
("732","681","75000","2023-02-24","761"),
("733","682","75000","2023-02-23","762"),
("734","683","75000","2023-02-24","763"),
("735","685","100000","2023-03-09","764"),
("736","686","100000","2023-02-25","765"),
("737","687","50000","2023-03-01","766"),
("738","688","100000","2023-02-26","767"),
("739","689","100000","2023-03-03","768"),
("740","690","100000","2023-02-24","769"),
("741","691","100000","2023-03-04","770"),
("742","692","100000","2023-03-07","771"),
("743","693","75000","2023-02-28","772"),
("744","694","75000","2023-02-28","773"),
("745","695","100000","2023-02-24","774"),
("746","696","100000","2023-03-06","775"),
("747","697","100000","2023-03-04","776"),
("748","698","75000","2023-03-08","777"),
("749","699","75000","2023-03-08","778"),
("750","700","100000","2023-03-05","779"),
("751","701","100000","2023-03-04","780"),
("752","702","100000","2023-03-10","781"),
("753","703","75000","2023-03-09","782"),
("754","704","75000","2023-03-09","783"),
("755","705","75000","2023-03-10","784"),
("756","706","75000","2023-02-24","785"),
("757","707","100000","2023-02-24","786"),
("758","708","50000","2023-03-11","787"),
("759","709","100000","2023-03-10","788"),
("760","710","100000","2023-02-24","789"),
("761","711","75000","2023-03-12","790"),
("762","712","75000","2023-03-12","791"),
("763","713","100000","2023-03-13","792"),
("764","714","50000","2023-02-24","793"),
("765","715","50000","2023-03-14","794"),
("766","717","50000","2023-03-12","795"),
("767","716","100000","2023-03-11","796"),
("768","718","100000","2023-02-24","797"),
("769","719","100000","2023-03-26","798"),
("771","178","100000","2023-02-21","800"),
("773","549","75","2023-03-15","802"),
("774","550","75000","2023-03-15","803"),
("775","554","100","2023-03-21","804"),
("776","556","100","2023-03-21","805"),
("777","557","100","2023-03-21","806"),
("778","569","100","2023-03-28","807"),
("779","570","100","2023-03-31","808"),
("780","573","75000","2023-03-28","809"),
("781","559","100000","2023-03-14","810"),
("782","560","560","2023-03-10","811"),
("783","561","100000","2023-03-21","812"),
("784","562","75000","2023-03-21","813"),
("785","562","75000","2023-03-21","814"),
("786","566","100000","2023-03-21","815"),
("787","492","100000","2023-03-28","816"),
("788","487","100000","2023-03-28","817"),
("789","570","100000","2023-03-21","818"),
("790","569","100","2023-03-21","819"),
("791","568","100000","2023-03-21","820"),
("792","574","75000","2023-03-21","821"),
("793","573","75000","2023-03-21","822"),
("794","576","100","2023-03-29","823"),
("795","577","100","2023-03-29","824"),
("796","578","100000","2023-03-21","825");




CREATE TABLE `stud_tb` (
  `id` int(100) NOT NULL AUTO_INCREMENT,
  `id_note` int(100) NOT NULL,
  `userID` int(100) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8 NOT NULL,
  `age` varchar(100) NOT NULL,
  `sex` varchar(100) NOT NULL,
  `catg` varchar(100) NOT NULL,
  `datein` date NOT NULL,
  `p_name` varchar(250) NOT NULL,
  `p_phone` varchar(250) NOT NULL,
  `loc` varchar(250) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `userID` (`userID`),
  CONSTRAINT `stud_tb_ibfk_1` FOREIGN KEY (`userID`) REFERENCES `users_tb` (`id_user`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=826 DEFAULT CHARSET=utf8mb4;


INSERT INTO stud_tb VALUES
("334","1","6","فهد محمد","5","ذكر","تمهيدي","2023-02-07","محمد","00000000000","بلوك2"),
("335","2","6","سيف محمد وهاب","4","ذكر","روضة","2023-02-13","محمد وهاب","00000000000","بلوك2"),
("336","3","1","ريان انس","1","ذكر","روضة","2023-01-15","انس","00000000000","بلوك2"),
("337","4","6","ايلا زيد","5","ذكر","تمهيدي","2023-02-20","زيد","00000000000","بلوك2"),
("338","5","6","عبدالله زيد","2","ذكر","حضانة","2023-02-20","زيد","00000000000","بلوك2"),
("339","6","6","حسين بلال ","1","ذكر","روضة","2023-02-19","بلال","00000000000","بلوك2"),
("340","7","6","حسين علي اكبر","1","ذكر","روضة","2023-02-19","علي اكبر","00000000000","بلوك2"),
("341","8","6","حسن سرمد","1","ذكر","روضة","2023-02-22","سرمد","00000000000","بلوك2"),
("342","9","6","سيلين سرمد","4","انثى","روضة","2023-02-22","سرمد","00000000000","بلوك2"),
("343","10","6","لمار مرتضى ","1","انثى","روضة","2023-02-15","مرتضى","00000000000","بلوك 2"),
("344","11","6","عسل محمد","4","انثى","روضة","2023-01-15","محمد","00000000000","بلوك2"),
("345","12","6","ازل محمد","2","انثى","حضانة","2023-01-15","محمد","00000000000","بلوك2"),
("346","13","6","الامير علي محمد","1","ذكر","روضة","2023-02-22","علي محمد","00000000000","بلوك2"),
("347","14","6","ادم محمد جواد","2","ذكر","روضة","2023-02-22","محمد جواد","00000000000","بلوك2"),
("348","15","6","امير علي ","2","ذكر","حضانة","2023-01-15","علي","00000000000","بلوك2"),
("349","16","6","ريتاج يوسف","5","انثى","تمهيدي","2023-01-15","يوسف","00000000000","بلوك2"),
("350","17","6","ابراهيم عمار ","4","ذكر","روضة","2023-02-27","عمار","00000000000","بلوك2"),
("351","18","6","حسين محمد","5","ذكر","تمهيدي","2023-02-20","محمد","00000000000","بلوك2"),
("352","19","6","رقيه علي ","2","انثى","حضانة","2023-02-20","علي","00000000000","بلوك2"),
("353","20","6","يزن علي","2","ذكر","روضة","2023-01-15","علي","00000000000","بلوك2"),
("354","21","6","حسن حيدر","2","ذكر","روضة","2023-01-12","حيدر","00000000000","بلوك2"),
("355","22","6","ملاك حيدر","5","انثى","تمهيدي","2023-01-12","حيدر","00000000000","بلوك2"),
("356","23","6","احمد مصطفى ","1","ذكر","روضة","2023-02-19","مصطفى","00000000000","بلوك2"),
("357","24","6","يحى علاء ","5","ذكر","تمهيدي","2023-02-19","علاء","00000000000","بلوك2"),
("358","25","6","امير حسن","6","ذكر","تمهيدي","2023-01-17","حسن","00000000000","بلوك2"),
("359","26","6","كرار احمد","5","ذكر","تمهيدي","2023-02-21","حسن","00000000000","بلوك2"),
("360","27","6","حيدر احمد","2","ذكر","حضانة","2023-02-21","احمد","00000000000","بلوك2"),
("361","28","6","سما احمد","6","انثى","تمهيدي","2023-02-21","احمد","00000000000","بلوك2"),
("362","29","6","جنى احمد","6","انثى","تمهيدي","2023-02-21","احمد","00000000000","بلوك2"),
("363","30","6","ميلا اسعد","3","انثى","حضانة","2023-02-28","اسعد","00000000000","بلوك2"),
("364","31","6","جود محمد","3","ذكر","روضة","2023-02-26","محمد","00000000000","بلوك2"),
("365","32","6","جومان محمد","4","انثى","روضة","2023-02-26","محمد","00000000000","بلوك2"),
("366","33","6","ريحانة مصطفلى","5","انثى","تمهيدي","2023-02-19","مصطفى ","00000000000","بلوك2"),
("367","34","6","يمان ياسر","4","انثى","روضة","2023-02-19","ياسر","00000000000","بلوك2"),
("368","35","6","الياس ياسر","4","ذكر","روضة","2023-02-19","ياسر","00000000000","بلوك2"),
("369","36","6","نبا محمد","2","انثى","حضانة","2023-02-22","محمد","00000000000","بلوك2"),
("370","37","6","نرجس سجاد","3","انثى","حضانة","2023-02-15","سجاد","00000000000","بلوك2"),
("371","38","6","احمد سجاد ","2","ذكر","حضانة","2023-02-15","سجاد","00000000000","بلوك2"),
("372","39","6","شهم مصطفى","4","ذكر","روضة","2023-01-22","مصطفى","00000000000","بلوك2"),
("373","40","6","ليا اياد هاشم","4","انثى","روضة","2023-02-23","اياد هاشم","00000000000","بلوك2"),
("374","41","6","الين اسامه ","3","انثى","حضانة","2023-01-15","اسامه","00000000000","بلوك2"),
("375","42","6","احمد الرضا محمد","5","ذكر","تمهيدي","2023-02-19","الرضا محمد","00000000000","بلوك2"),
("376","43","6","الياس مصطفى","2","ذكر","حضانة","2023-01-23","مصطفى","00000000000","بلوك2"),
("377","44","6","نجات عبد الجبار ","2","انثى","حضانة","2023-02-22","عبد الجبار","00000000000","بلوك2"),
("378","45","6","زينب عبد الجبار","2","انثى","حضانة","2023-02-22","عبد الجبار","00000000000","بلوك2"),
("379","46","6","ابا الحسن ضرغام","4","ذكر","روضة","2023-01-15","ضرغام","00000000000","بلوك2"),
("380","47","6","اليان عبد القادر","5","انثى","تمهيدي","2023-02-20","عبد القادر","00000000000","بلوك2"),
("381","48","6","اسد عبد القادر","5","ذكر","تمهيدي","2023-02-20","عبد القادر","00000000000","بلوك2"),
("382","49","6","صفا حيدر","5","انثى","تمهيدي","2023-01-25","حيدر","00000000000","بلوك2"),
("383","50","6","فاطمه سيف ","4","انثى","روضة","2023-02-27","سيف","00000000000","بلوك2"),
("384","51","6","اسامه زيد","5","ذكر","تمهيدي","2023-01-25","زيد","00000000000","بلوك2"),
("385","52","6","ادم حيدر ","2","ذكر","حضانة","2023-02-22","حيدر","00000000000","بلوك2"),
("386","53","6","ايليا سيف صلاح","4","ذكر","روضة","2023-02-26","سيف صلاح","00000000000","بلوك2"),
("387","54","6","افين صلاح ","4","انثى","روضة","2023-02-26","صلاح","00000000000","بلوك2"),
("388","55","6","علي كرار ","4","ذكر","روضة","2023-01-29","كرار","00000000000","بلوك2"),
("389","56","6","مسرة اسعد","4","انثى","روضة","2023-01-29","اسعد","00000000000","بلوك2"),
("390","57","6","منتضر اسعد","4","ذكر","روضة","2023-01-29","اسعد","00000000000","بلوك2"),
("391","58","6","رزان مهدي","4","انثى","روضة","2023-01-30","مهدي","00000000000","بلوك2"),
("392","59","6","علي مهدي","2","ذكر","حضانة","2023-01-30","مهدي","00000000000","بلوك2"),
("393","60","6","عباس مقداد","5","ذكر","تمهيدي","2023-01-30","مقداد","00000000000","بلوك2"),
("394","61","6","تميم حسين","2","ذكر","حضانة","2023-01-29","حسين","00000000000","بلوك2"),
("395","62","6","متين حسين","4","ذكر","روضة","2023-01-29","حسين","00000000000","بلوك2"),
("396","63","6","حسين خالد","4","ذكر","روضة","2023-01-30","خالد","00000000000","بلوك2"),
("397","64","6","علي حيدر ","4","ذكر","روضة","2023-01-28","حيدر","00000000000","بلوك2"),
("398","65","6","ايهم ضرغام","4","ذكر","روضة","2023-02-01","ضرغام","00000000000","بلوك2"),
("399","66","6","ديمه سعيد","5","انثى","تمهيدي","2023-01-25","سعيد","00000000000","بلوك2"),
("400","67","6","جنى محمد سلطان","4","انثى","روضة","2023-02-01","محمد سلطان","00000000000","بلوك2"),
("401","68","6","حسام صفاء","5","ذكر","تمهيدي","2023-02-01","صفاء","00000000000","بلوك2"),
("402","69","6","الين محمد عبد الكريم","2","انثى","روضة","2023-02-01","محمد عبد الكريم","00000000000","بلوك2"),
("403","70","6","علي احمد نعمه","4","ذكر","روضة","2023-02-01","علي نعمة","00000000000","بلوك2"),
("404","71","6","ماس مهند ","5","انثى","تمهيدي","2023-02-01","مهند","00000000000","بلوك2"),
("405","72","6","حوراء مصعب","4","انثى","روضة","2023-02-01","مصعب","00000000000","بلوك2"),
("406","73","6","حسن علي عبد الحسين","5","ذكر","تمهيدي","2023-02-02","علي عبد الحسين","00000000000","بلوك2"),
("407","74","6","ادم علي ","5","ذكر","تمهيدي","2023-01-30","علي","00000000000","بلوك2"),
("408","75","6","شدن عبد الله","5","انثى","تمهيدي","2023-02-01","عبدالله","00000000000","بلوك2"),
("409","76","6","محمد عبدالله","2","ذكر","حضانة","2023-02-01","عبدالله","00000000000","بلوك2"),
("410","77","6","ميان عبد الله","2","انثى","حضانة","2023-02-01","عبدالله","00000000000","بلوك2"),
("411","78","6","علي نزار","5","ذكر","تمهيدي","2023-02-02","نزار","00000000000","بلوك2"),
("412","79","6","جود عمار","4","ذكر","روضة","2023-01-29","عمار","00000000000","بلوك2"),
("413","80","6","قسور عبد الرضا قاسم","2","انثى","روضة","2023-02-05","عبدالرضا قاسم","00000000000","بلوك2"),
("414","81","6","عباس اسامة","2","ذكر","حضانة","2023-02-05","اسامه","00000000000","بلوك2"),
("415","82","6","حمزه اسامة","2","ذكر","حضانة","2023-02-05","اسامة","00000000000","بلوك2"),
("416","83","6","علي احمد صلاح","4","ذكر","روضة","2023-02-02","احمد صلاح","00000000000","بلوك2"),
("417","84","6","در احمد","4","انثى","روضة","2023-02-05","احمد","00000000000","بلوك2"),
("418","85","6","قاسم احمد","2","ذكر","روضة","2023-02-05","احمد","00000000000","بلوك2"),
("419","86","6","زينب مصطفى جاسم","2","انثى","حضانة","2023-02-05","مصطفى جاسم","00000000000","بلوك2"),
("420","87","6","حسن علي ","5","ذكر","تمهيدي","2023-02-05","علي","00000000000","بلوك2"),
("421","88","6","انس علي ","4","ذكر","روضة","2023-02-09","علي","00000000000","بلوك2"),
("422","89","6","ماس علي","2","انثى","حضانة","2023-02-09","علي","00000000000","بلوك2"),
("423","90","6","كرار حيدر","5","ذكر","تمهيدي","2023-02-07","حيدر","00000000000","بلوك2"),
("424","91","6","ادم اوس","2","ذكر","حضانة","2023-02-08","اوس","00000000000","بلوك2"),
("425","92","6","فهد محمد","2","ذكر","روضة","2023-02-07","محمد","00000000000","بلوك2"),
("426","93","6","قيصر محمد","2","ذكر","حضانة","2023-02-08","محمد","00000000000","بلوك2"),
("427","94","6","علي حسين","2","ذكر","روضة","2023-02-07","حسين","00000000000","بلوك2"),
("428","95","6","مهيمن صادق","5","ذكر","تمهيدي","2023-02-12","صادق","00000000000","بلوك2"),
("429","96","6","زين العابدين علي","2","ذكر","حضانة","2023-02-12","علي","00000000000","بلوك2"),
("431","98","6","تميم محمد","2","ذكر","روضة","2023-02-08","محمد","00000000000","بلوك2"),
("432","99","6","ليمار حيدر","4","انثى","روضة","2023-02-15","حيدر","00000000000","بلوك2"),
("433","100","7","علي صلاح","1","ذكر","روضة","2023-02-27","صلاح","00000000000","بلوك3"),
("434","101","7","حسين علي","5","ذكر","تمهيدي","2023-01-25","علي","00000000000","بلوك3");
INSERT INTO stud_tb VALUES
("435","102","7","فهد حسين اياد","2","ذكر","حضانة","2023-01-25","حسين اياد","00000000000","بلوك3"),
("436","103","7","رهف محمود","4","انثى","روضة","2023-01-25","محمود","00000000000","بلوك3"),
("437","104","7","جود علي قاسم","2","ذكر","حضانة","2023-01-25","علي قاسم","00000000000","بلوك3"),
("438","105","7","محمد حسين حازم","4","ذكر","روضة","2023-01-25","حسين حازم","00000000000","بلوك3"),
("439","106","7","اساور محمد","1","انثى","روضة","2023-02-22","محمد","00000000000","بلوك3"),
("440","107","7","جنى احمد","1","ذكر","روضة","2023-01-22","احمد","00000000000","بلوك3"),
("441","108","7","ود احمد","2","انثى","حضانة","2023-01-25","احمد","00000000000","بلوك3"),
("442","109","7","كيان ضرغام","4","ذكر","روضة","2023-01-25","ضرغام","00000000000","بلوك3"),
("443","110","7","سيرين محمد محسن","5","انثى","تمهيدي","2023-01-25","محمد","00000000000","بلوك3"),
("444","111","7","محمد فأق","1","ذكر","روضة","2023-02-26","فاق","00000000000","بلوك3"),
("445","112","7","ديما محمد","1","انثى","روضة","2023-01-22","محمد","00000000000","بلوك3"),
("446","113","7","جمانه جاسم","2","انثى","حضانة","2023-01-25","جاسم","00000000000","بلوك3"),
("447","114","7","علي نورس","4","ذكر","روضة","2023-01-25","نورس","00000000000","بلوك3"),
("448","115","7","ايلين محمد ضياء","4","انثى","روضة","2023-01-25","محمد ضياء","00000000000","بلوك3"),
("449","116","7","احمد شهاب ","1","ذكر","روضة","2023-01-24","شهاب","00000000000","بلوك3"),
("450","117","7","ادم شهاب","1","ذكر","روضة","2023-01-24","شهاب","00000000000","بلوك3"),
("451","118","7","مسره خالد","4","انثى","روضة","2023-01-25","خالد","00000000000","بلوك3"),
("452","119","7","يوسف مهند","4","انثى","روضة","2023-01-25","مهند","00000000000","بلوك3"),
("453","120","7","زيد يحى طلال","2","ذكر","حضانة","2023-01-25","يحى طلال","00000000000","بلوك3"),
("454","121","7","ليان مصطفى","4","انثى","روضة","2023-01-25","مصطفى ","00000000000","بلوك3"),
("455","122","7","فضل علي مؤيد","2","ذكر","حضانة","2023-01-25","علي مؤيد","00000000000","بلوك3"),
("456","123","7","رضا نادر","1","ذكر","روضة","2023-02-26","نادر","00000000000","بلوك3"),
("457","124","7","تولين نادر","1","ذكر","روضة","2023-02-26","نادر","00000000000","بلوك3"),
("458","125","7","علي لاكبر عمار","2","ذكر","حضانة","2023-01-25","عمار","00000000000","بلوك3"),
("459","126","7","يوسف علي ستار","4","ذكر","روضة","2023-01-25","علي ستار","00000000000","بلوك3"),
("460","127","7","مصطفى حسن مجيد","2","ذكر","حضانة","2023-01-25","حسن مجيد","00000000000","بلوك3"),
("461","128","7","عمار ياسر","2","ذكر","حضانة","2023-01-25","ياسر","00000000000","بلوك3"),
("462","129","7","ذوالفقار علي","5","ذكر","تمهيدي","2023-01-25","علي","00000000000","بلوك3"),
("463","130","7","ياسمين محمد","2","انثى","حضانة","2023-01-25","محمد","00000000000","بلوك3"),
("464","131","7","جهاد حيدر","4","ذكر","روضة","2023-01-25","حيدر","00000000000","بلوك3"),
("465","132","7","علي صادق ","1","ذكر","روضة","2023-02-01","صادق","00000000000","بلوك3"),
("466","133","7","عباس صادق","1","ذكر","روضة","2023-02-01","صادق","00000000000","بلوك3"),
("467","134","7","جود طالب","1","ذكر","روضة","2023-02-01","طالب","00000000000","بلوك3"),
("468","135","7","محمد سيف الدين","2","ذكر","حضانة","2023-01-25","سيف الدين","00000000000","بلوك3"),
("469","136","7","جنه سيف الدين","5","انثى","تمهيدي","2023-01-25","سيف الدين","00000000000","بلوك3"),
("470","137","7","جعفر محمد فهد","4","ذكر","روضة","2023-01-25","محمد فهد","00000000000","بلوك3"),
("471","138","7","مريم روكان","4","انثى","روضة","2023-01-25","روكان","00000000000","بلوك3"),
("472","139","7","كرار ياسر نورس","5","ذكر","تمهيدي","2023-01-25","ياسر نورس","00000000000","بلوك3"),
("473","140","7","ذي يزن علي ","4","ذكر","روضة","2023-01-25","علي","00000000000","بلوك3"),
("474","141","7","زين مصطفى حامد","1","ذكر","روضة","2023-02-05","مصطفى حامد","00000000000","بلوك3"),
("475","142","7","ابا الفضل محسن","5","ذكر","تمهيدي","2023-01-25","محسن","00000000000","بلوك3"),
("476","143","7","امير احمد علي","1","ذكر","روضة","2023-02-05","احمد علي","00000000000","بلوك3"),
("477","144","7","ناي علي صالح","4","انثى","روضة","2023-01-25","علي صالح","00000000000","بلوك3"),
("478","145","7","امير ضياء حسين","5","ذكر","تمهيدي","2023-01-25","ضياء حسين","00000000000","بلوك3"),
("479","146","7","مينا علي مهدي","1","انثى","روضة","2023-02-05","علي مهدي","00000000000","بلوك3"),
("480","147","7","احمد علي مهدي","5","ذكر","تمهيدي","2023-01-25","علي مهدي","00000000000","بلوك3"),
("481","148","7","ايليا نزار","5","ذكر","تمهيدي","2023-01-25","نزار","00000000000","بلوك3"),
("482","149","7","فاطمة حيدر","4","انثى","روضة","2023-01-25","حيدر","00000000000","بلوك3"),
("483","150","7","قمر احمد منال","4","انثى","روضة","2023-01-25","احمد منال","00000000000","بلوك3"),
("484","151","7","رزان مرتضى","2","انثى","حضانة","2023-01-25","مرتضى","00000000000","بلوك3"),
("485","152","7","جوان احمد","1","انثى","روضة","2023-02-06","احمد","00000000000","بلوك3"),
("486","153","7","علي احمد","2","ذكر","حضانة","2023-01-25","احمد","00000000000","بلوك3"),
("487","154","7","دانيه احمد","2","انثى","حضانة","2023-01-25","احمد","00000000000","بلوك3"),
("489","156","7","ارزه محمد","4","انثى","روضة","2023-01-25","محمد","00000000000","بلوك3"),
("490","157","7","ياسر احمد خضير","5","ذكر","تمهيدي","2023-01-25","محمد خضير","00000000000","بلوك3"),
("491","158","7","عباس علي سمير","2","ذكر","حضانة","2023-01-25","علي سمير","00000000000","بلوك3"),
("492","159","7","فيروز كريم","5","انثى","تمهيدي","2023-01-25","كريم","00000000000","بلوك3"),
("493","160","7","دره كريم","1","انثى","روضة","2023-02-09","كريم","00000000000","بلوك3"),
("494","161","7","علي محمد حسين","5","ذكر","تمهيدي","2023-01-25","محمد حسين","00000000000","بلوك3"),
("495","162","7","علي محمد عبد الامير ","1","ذكر","روضة","2023-02-12","محمد عبد الامير","00000000000","بلوك3"),
("496","163","7","جنى علي","1","انثى","روضة","2023-02-12","علي","00000000000","بلوك3"),
("497","164","9","حسين علي لفته","1","ذكر","حضانة","2023-01-25","علي لفته","00000000000","بلوك5"),
("498","165","9","يمام علي","1","ذكر","روضة","2023-01-12","علي","00000000000","بلوك5"),
("499","166","9","ميار علي","1","انثى","روضة","2023-01-12","علي","00000000000","بلوك5"),
("500","167","9","ايليا ارشد","1","انثى","روضة","2023-01-15","ارشد","00000000000","بلوك5"),
("501","168","9","رزان محمد نجم","4","انثى","روضة","2023-01-17","محمد نجم","00000000000","بلوك5"),
("502","169","9"," علي غزوان علاء","2","ذكر","حضانة","2023-01-25","غزوان","00000000000","بلوك5"),
("503","170","9","ملك ضياء","5","انثى","تمهيدي","2023-01-25","ضياء","00000000000","بلوك5"),
("504","171","9","جعفر يوسف","1","ذكر","روضة","2023-01-18","يوسف","00000000000","بلوك5"),
("505","172","9","عبد الله يوسف","1","ذكر","روضة","2023-01-18","يوسف","00000000000","بلوك5"),
("506","173","9","عراق وقاص","4","ذكر","روضة","2023-01-25","وقاص","00000000000","بلوك5"),
("507","174","9","ريتاج صادق","1","انثى","روضة","2023-01-23","صادق","00000000000","بلوك5"),
("508","175","9","ريتاج صادق","1","انثى","روضة","2023-01-23","صادق","00000000000","بلوك5"),
("509","176","9","زهراء لؤي","1","انثى","روضة","2023-01-20","لؤي","00000000000","بلوك5"),
("510","177","9","همام عبدالله","5","ذكر","حضانة","2023-01-25","عبدالله","00000000000","بلوك5"),
("511","178","9","مسك عبدالله","5","انثى","تمهيدي","2023-01-25","عبدالله","00000000000","بلوك5"),
("512","179","9","عبد الله محمد هادي","5","ذكر","تمهيدي","2023-01-25","محمد هادي","00000000000","بلوك5"),
("513","180","9","اصف محمد","2","ذكر","حضانة","2023-01-25","محمد","00000000000","بلوك5"),
("514","181","9","رقيه سليم","2","انثى","حضانة","2023-01-25","سليم","00000000000","بلوك5"),
("515","182","9","اديم محمد","5","ذكر","حضانة","2023-01-25","محمد","00000000000","بلوك5"),
("516","183","9","رقيه سليم","1","انثى","روضة","2023-01-25","سليم","00000000000","بلوك5"),
("517","184","9","ارشد ليث","1","ذكر","روضة","2023-01-29","ليث","00000000000","بلوك5"),
("518","185","9","دارين علي","4","انثى","روضة","2023-01-25","علي","00000000000","بلوك5"),
("519","186","9","حيدر علي","4","ذكر","روضة","2023-01-25","علي","00000000000","بلوك5"),
("520","187","9","قمر جواد","4","انثى","روضة","2023-01-25","جواد","00000000000","بلوك5"),
("522","189","9","قمر مصطفى","5","انثى","حضانة","2023-01-25","مصطفى","00000000000","بلوك5"),
("523","190","9","مريم حيدر","5","انثى","تمهيدي","2023-01-25","حيدر","00000000000","بلوك5"),
("524","191","9","ايوب امير","5","ذكر","تمهيدي","2023-01-25","امير","00000000000","بلوك5"),
("525","192","9","علي مثنى","5","ذكر","تمهيدي","2023-01-25","مثنى","00000000000","بلوك5"),
("526","193","9","جود علي ","1","ذكر","روضة","2023-02-07","علي","00000000000","بلوك5"),
("527","194","9","زهراء عبد القادر","5","انثى","تمهيدي","2023-01-25","عبد القادر","00000000000","بلوك5"),
("528","195","9","ميار عبد القادر","4","انثى","روضة","2023-01-25","عبد القادر","00000000000","بلوك5"),
("529","196","9","يوسف فهد","2","ذكر","حضانة","2023-01-25","فهد","00000000000","بلوك5"),
("530","197","9","رزان فهد","5","انثى","تمهيدي","2023-01-25","فهد","00000000000","بلوك5"),
("532","199","9","الان نوزاد","1","انثى","روضة","2023-02-06","نوزاد","00000000000","بلوك5"),
("533","200","9","لافا نوزاد","1","انثى","روضة","2023-02-06","نوزاد","00000000000","بلوك5"),
("534","201","9","لافا نوزاد","1","انثى","روضة","2023-02-06","نوزاد","00000000000","بلوك5"),
("535","202","8","زين العابدين سرور","5","ذكر","تمهيدي","2023-01-25","سرور","00000000000","بلوك4"),
("536","203","8","نرجس كرار","5","انثى","تمهيدي","2023-01-25","كرار","00000000000","بلوك4"),
("537","204","8","فاطمه كرار","4","انثى","روضة","2023-01-25","كرار","00000000000","بلوك4");
INSERT INTO stud_tb VALUES
("538","205","8"," ادم عامر","5","ذكر","تمهيدي","2023-01-25","عامر","00000000000","بلوك4"),
("539","206","8","امير عامر","2","ذكر","حضانة","2023-01-25","عامر","00000000000","بلوك4"),
("540","207","8","علي حيدر","1","ذكر","تحضيري","2023-01-25","حيدر","00000000000","بلوك4"),
("541","208","8","زينب امير","3","انثى","تحضيري","2023-01-25","امير","00000000000","بلوك4"),
("542","209","8","عباس امير","3","ذكر","تحضيري","2023-01-25","امير","00000000000","بلوك4"),
("543","210","8","نور ساطع ","3","انثى","تحضيري","2023-01-25","ساطع","00000000000","بلوك4"),
("544","211","8","ود علي ","2","انثى","حضانة","2023-01-25","علي","00000000000","بلوك4"),
("545","212","8","ايلين صباح","4","انثى","روضة","2023-01-25","صباح","00000000000","بلوك4"),
("546","213","8","ليث مصطفى","4","ذكر","روضة","2023-01-25","مصطفى","00000000000","بلوك4"),
("547","214","8","نرجس امير","4","انثى","روضة","2023-01-25","امير","00000000000","بلوك4"),
("548","215","8","امير محمد","3","ذكر","تحضيري","2023-01-25","محمد","00000000000","بلوك4"),
("549","216","8","ايلين قيصر","1","انثى","روضة","2023-02-15","قيصر","00000000000","بلوك4"),
("550","217","8","عبدالله رسول","3","ذكر","تحضيري","2023-01-25","رسول","00000000000","بلوك4"),
("551","218","8","مرتضى عمر","5","ذكر","تمهيدي","2023-01-25","عمر","00000000000","بلوك4"),
("552","219","8","قسوره احمد","2","انثى","حضانة","2023-01-25","احمد","00000000000","بلوك4"),
("553","220","8","ريتال احمد","1","انثى","روضة","2023-02-20","احمد","00000000000","بلوك4"),
("554","221","8","جسور ابراهيم","1","ذكر","روضة","2023-01-22","ابراهيم","00000000000","بلوك4"),
("555","222","8","سدن علي","3","انثى","تحضيري","2023-01-25","علي","00000000000","بلوك4"),
("556","223","8","امير حيدر","4","ذكر","تمهيدي","2023-01-25","حيدر","00000000000","بلوك4"),
("557","224","8","جعفر حيدر","5","ذكر","تمهيدي","2023-01-25","حيدر","00000000000","بلوك4"),
("559","226","8","مريم انمار ","2","انثى","حضانة","2023-01-25","انمار","00000000000","بلوك4"),
("560","227","8","حسين انمار","5","ذكر","تمهيدي","2023-01-25","انمار","00000000000","بلوك4"),
("561","228","8","جعفر سهيل","5","ذكر","تمهيدي","2023-01-25","سهيل","00000000000","بلوك4"),
("562","229","8","ماريه محسن","5","انثى","تمهيدي","2023-01-25","محسن","00000000000","بلوك4"),
("563","230","8","محمد علي","2","ذكر","حضانة","2023-01-25","علي","00000000000","بلوك4"),
("564","231","8","تيم عمر","2","ذكر","حضانة","2023-01-25","عمر","00000000000","بلوك4"),
("565","232","8","محمد فراس صادق","2","ذكر","حضانة","2023-01-25","فراس صادق","00000000000","بلوك4"),
("566","233","8","ايهم خليل","2","ذكر","حضانة","2023-01-25","خليل","00000000000","بلوك4"),
("567","234","8","ايدن خليل","4","ذكر","روضة","2023-01-25","خليل","00000000000","بلوك4"),
("568","235","8","عبدالرزاق صلاح","1","ذكر","تمهيدي","2023-02-22","صلاح","00000000000","بلوك4"),
("569","236","8","علي سيف","5","ذكر","تمهيدي","2023-01-25","سيف","00000000000","بلوك4"),
("570","237","8","كرستيان باسم","4","انثى","روضة","2023-01-25","باسم","00000000000","بلوك4"),
("571","238","8","حمزه عماد","5","ذكر","تمهيدي","2023-01-25","عماد","00000000000","بلوك4"),
("572","239","8","ريتاد محمد","5","انثى","تمهيدي","2023-01-25","محمد","00000000000","بلوك4"),
("573","240","8","لمار رائد","5","انثى","تمهيدي","2023-01-25","رائد","00000000000","بلوك4"),
("574","241","8","علي رائد","2","ذكر","حضانة","2023-01-25","رائد","00000000000","بلوك4"),
("575","242","8","فاطمه عصام","5","انثى","تمهيدي","2023-01-25","عصام","00000000000","بلوك4"),
("576","243","8","رضا عصام","5","ذكر","تمهيدي","2023-01-25","عصام","00000000000","بلوك4"),
("577","244","8","فضل الله سرمد","1","ذكر","تحضيري","2023-01-25","سرمد","00000000000","بلوك4"),
("578","245","8","عبدالله سرمد","2","ذكر","حضانة","2023-01-25","سرمد","00000000000","بلوك4"),
("579","246","8","ادم رائد","5","ذكر","تمهيدي","2023-01-25","رائد","00000000000","بلوك4"),
("580","247","8","بنامين ياسر","4","ذكر","روضة","2023-01-25","ياسر","00000000000","بلوك4"),
("581","248","8","يعقوب ياسر","2","ذكر","حضانة","2023-01-25","ياسر","00000000000","بلوك4"),
("582","249","8","يوسف ياسر","5","ذكر","تمهيدي","2023-01-25","ياسر","00000000000","بلوك4"),
("583","250","8","يامن مصطفى","3","ذكر","تحضيري","2023-01-25","مصطفى","00000000000","بلوك4"),
("584","251","8","مهيمن محمد","2","ذكر","حضانة","2023-01-25","محمد","00000000000","بلوك4"),
("586","253","8","مصطفى احسان","5","ذكر","تمهيدي","2023-01-25","احسان","00000000000","بلوك4"),
("587","254","8","محمد احسان","4","ذكر","روضة","2023-01-25","احسان","00000000000","بلوك4"),
("588","255","8","مرتضى كاظم","2","ذكر","حضانة","2023-01-25","كاظم","00000000000","بلوك4"),
("589","256","8","ام البنين كاظم","4","انثى","روضة","2023-01-25","كاظم","00000000000","بلوك4"),
("590","257","8","لليان محمد","3","انثى","تحضيري","2023-01-25","محمد","00000000000","بلوك4"),
("591","258","8","ليان محمد","3","انثى","تحضيري","2023-01-25","محمد","00000000000","بلوك4"),
("592","259","8","يمان محمد","4","انثى","روضة","2023-01-25","محمد","00000000000","بلوك4"),
("593","260","8","شدن محمد","4","انثى","روضة","2023-01-25","محمد","00000000000","بلوك4"),
("594","261","8","حسن احمد قاسم","5","ذكر","تمهيدي","2023-01-25","احمد قاسم","00000000000","بلوك4"),
("595","262","8","يوسف عبد الرحمن ","2","ذكر","حضانة","2023-01-25","عبد الرحمن","00000000000","بلوك4"),
("596","263","8","مسك عبد الرحمن","2","انثى","حضانة","2023-01-25","عبد الرحمن","00000000000","بلوك4"),
("598","265","8","ليان عامر","4","انثى","روضة","2023-01-25","عامر","00000000000","بلوك4"),
("599","266","8","ياسين حسنين","5","ذكر","تمهيدي","2023-01-25","حسنين","00000000000","بلوك4"),
("600","267","8","هيلين حسنين","5","انثى","تمهيدي","2023-01-25","حسنين","00000000000","بلوك4"),
("601","268","8","هبه الله مصطفى","3","انثى","تحضيري","2023-01-25","مصطفى","00000000000","بلوك4"),
("602","269","8","يقين احمد","4","انثى","روضة","2023-01-25","احمد","00000000000","بلوك4"),
("603","270","8","غسق احمد","3","انثى","تحضيري","2023-01-25","احمد","00000000000","بلوك4"),
("605","272","8","علي حامد","3","ذكر","تحضيري","2023-01-25","حامد","00000000000","بلوك4"),
("606","273","8","لارين حامد","4","انثى","روضة","2023-01-25","حامد","00000000000","بلوك4"),
("607","274","8","حيدر احمد","3","ذكر","تحضيري","2023-01-25","احمد","00000000000","بلوك4"),
("608","275","8","يافا احمد","5","انثى","تمهيدي","2023-01-25","احمد","00000000000","بلوك4"),
("609","276","8","علي بسام","5","ذكر","تمهيدي","2023-01-25","بسام","00000000000","بلوك4"),
("610","277","8","حسين صادق","5","ذكر","تمهيدي","2023-01-25","صادق","00000000000","بلوك4"),
("611","278","8","زينب صادق","4","انثى","روضة","2023-01-25","صادق","00000000000","بلوك4"),
("612","279","8","منتظر محمود","1","ذكر","روضة","2023-02-20","محمود","00000000000","بلوك4"),
("613","280","8","رهف نور الدين","4","انثى","تحضيري","2023-01-25","نور الدين","00000000000","بلوك4"),
("614","281","8","ازل نور الدين","5","انثى","تمهيدي","2023-01-25","نور الدين","00000000000","بلوك4"),
("615","282","8","فاطمه زيدون","5","انثى","تمهيدي","2023-01-25","زيدون","00000000000","بلوك4"),
("616","283","8","علي زيدون","4","ذكر","روضة","2023-01-25","زيدون","00000000000","بلوك4"),
("617","284","8","اسامه ماهر","4","ذكر","روضة","2023-01-25","ماهر","00000000000","بلوك4"),
("618","285","12","مرجان وسام","1","ذكر","روضة","2023-01-09","وسام","00000000000","بلوك8"),
("619","286","12","نارين باسم","1","انثى","روضة","2023-01-11","باسم","00000000000","بلوك8"),
("620","287","12","ناي محمد","1","انثى","روضة","2023-01-08","محمد","00000000000","بلوك8"),
("621","288","12","ابراهيم محمد","1","ذكر","روضة","2023-01-08","محمد","00000000000","بلوك8"),
("622","289","12","ادم وسام","1","ذكر","روضة","2023-01-16","وسام","00000000000","بلوك8"),
("623","290","12","ليان احمد","1","انثى","روضة","2023-01-11","احمد","00000000000","بلوك8"),
("624","291","12","علي احمد","1","ذكر","روضة","2023-01-11","احمد","00000000000","بلوك8"),
("625","292","12","علي احمد ","1","ذكر","روضة","2023-01-11","احمد","00000000000","بلوك8"),
("626","293","12","حسن مهند","1","ذكر","روضة","2023-01-11","مهند","00000000000","بلوك8"),
("627","294","12","يوسف مهند","1","ذكر","روضة","2023-01-11","مهند","00000000000","بلوك8"),
("628","295","12","كيان مهند","1","ذكر","روضة","2023-01-11","مهند","00000000000","بلوك8"),
("629","296","12","غنى محمد","1","انثى","روضة","2023-01-12","محمد","00000000000","بلوك8"),
("630","297","12","ادم داود","1","ذكر","روضة","2023-01-12","داود","00000000000","بلوك8"),
("631","298","12","صالح مهدي","1","ذكر","روضة","2023-01-05","مهدي","00000000000","بلوك8"),
("632","299","12","دره حيدر","1","انثى","روضة","2023-01-12","حيدر","00000000000","بلوك8"),
("633","300","12","ياسين فلاح","1","ذكر","روضة","2023-01-06","فلاح","00000000000","بلوك8"),
("634","301","12","سلا زيد","1","ذكر","روضة","2023-01-15","زيد","00000000000","بلوك8"),
("635","302","12","امير علي","1","ذكر","روضة","2023-01-12","علي","00000000000","بلوك8"),
("636","303","12","نور حميد","1","ذكر","روضة","2023-01-05","حميد","00000000000","بلوك8"),
("637","304","12","يمين حميد","1","ذكر","روضة","2023-01-05","حميد","00000000000","بلوك8"),
("638","305","12","رزان ياسر","1","انثى","روضة","2023-01-16","ياسر","00000000000","بلوك8"),
("639","306","12","الحارث غزوان","1","ذكر","روضة","2023-01-16","غزوان","00000000000","بلوك8"),
("640","307","12","حسين علي ","1","ذكر","روضة","2023-01-16","علي","00000000000","بلوك8"),
("641","308","12","رضوان الله سجاد","4","ذكر","روضة","2023-01-17","سجاد","00000000000","بلوك8");
INSERT INTO stud_tb VALUES
("642","309","12","محمد هاشم","4","ذكر","روضة","2023-01-14","هاشم","00000000000","بلوك8"),
("643","310","12","ماري احمد","4","انثى","روضة","2023-01-17","احمد","00000000000","بلوك8"),
("644","311","12","ايلان احمد","4","انثى","روضة","2023-01-18","احمد","00000000000","بلوك8"),
("645","312","12","رزان علي ","4","انثى","روضة","2023-01-22","علي","00000000000","بلوك8"),
("646","313","12","ليث داود","4","ذكر","روضة","2023-01-22","داود","00000000000","بلوك8"),
("647","314","12","رامي مصطفى","4","ذكر","روضة","2023-01-22","مصطفى","00000000000","بلوك8"),
("648","315","12","يونس  ابراهيم","4","ذكر","روضة","2023-01-22","ابراهيم","00000000000","بلوك8"),
("649","316","12","ياس ابراهيم","4","ذكر","روضة","2023-01-22","ابراهيم","00000000000","بلوك8"),
("650","317","12","حسن هيثم","4","ذكر","روضة","2023-01-21","هيثم","00000000000","بلوك8"),
("651","318","12","جوان هيثم","4","ذكر","روضة","2023-01-21","هيثم","00000000000","بلوك8"),
("652","319","12","محمد مؤيد","4","ذكر","روضة","2023-01-22","مؤيد","00000000000","بلوك8"),
("653","320","12","حسن حيدر","4","ذكر","روضة","2022-12-14","حيدر","00000000000","بلوك8"),
("654","321","11","حامد عمار","5","ذكر","تمهيدي","2023-02-11","عمار","00000000000","بلوك7"),
("655","322","11","علي غزوان","5","ذكر","تمهيدي","2023-02-12","غزوان","00000000000","بلوك 7"),
("657","324","11","سما محمد","6","انثى","تمهيدي","2023-02-12","محمد","00000000000","بلوك7"),
("658","325","11","ميار محمد","5","انثى","تمهيدي","2023-02-12","محمد","00000000000","بلوك7"),
("659","326","11","محمد حسام","4","ذكر","روضة","2023-02-19","حسام","00000000000","بلوك7"),
("660","327","11","امير حسام","4","ذكر","روضة","2023-02-19","حسام","00000000000","بلوك7"),
("661","328","11","رما ثائر","4","انثى","روضة","2023-02-19","ثائر","00000000000","بلوك7"),
("662","329","11","احمد منير","2","ذكر","حضانة","2023-02-17","منير","00000000000","بلوك7"),
("663","330","11","حسين نبيل","5","ذكر","تمهيدي","2023-02-19","نبيل","00000000000","بلوك7"),
("664","331","11","فرح محمد","6","انثى","تمهيدي","2023-02-21","محمد","00000000000","بلوك7"),
("667","334","11","زين العابدين احمد","2","ذكر","حضانة","2023-01-22","احمد","00000000000","بلوك7"),
("668","335","11","حسين احمد","5","ذكر","تمهيدي","2023-02-21","احمد","00000000000","بلوك7"),
("669","336","11","يارا احمد","4","انثى","روضة","2023-02-21","احمد","00000000000","بلوك7"),
("670","337","11","تارا احمد","4","انثى","روضة","2023-02-21","احمد","00000000000","بلوك7"),
("671","338","11","تاره احمد","5","انثى","روضة","2023-02-21","احمد","00000000000","بلوك7"),
("672","339","11","جعفر مصطفى","4","ذكر","روضة","2023-01-23","مصطفى","00000000000","بلوك7"),
("673","340","11","غلا مصطفى","4","انثى","روضة","2023-01-23","مصطفى","00000000000","بلوك7"),
("674","341","11","ياسين مصطفى","4","ذكر","روضة","2023-02-23","مصطفى","00000000000","بلوك7"),
("675","342","11","موسى مصطفى ","6","ذكر","تمهيدي","2023-02-23","مصطفى","00000000000","بلوك7"),
("676","343","11","حسن محمد","4","ذكر","روضة","2023-02-20","محمد","00000000000","بلوك7"),
("677","344","11","دره منتظر","5","انثى","تمهيدي","2023-01-25","منتظر","00000000000","بلوك7"),
("678","345","11","امير محمد","5","ذكر","روضة","2023-02-17","محمد","00000000000","بلوك7"),
("679","346","11","موسى فراس","4","ذكر","روضة","2023-02-23","فراس","00000000000","بلوك7"),
("680","347","11","علي محمد","5","ذكر","تمهيدي","2023-01-26","محمد","00000000000","بلوك7"),
("681","348","11","يزن محمد","4","ذكر","روضة","2023-01-26","محمد","00000000000","بلوك7"),
("682","349","11","منه الله كرار","5","انثى","تمهيدي","2023-01-23","كرار","00000000000","بلوك7"),
("683","350","11","ساره مصطفى","5","انثى","تمهيدي","2023-01-29","مصطفى","00000000000","بلوك7"),
("684","351","11","يارا مصطفى","3","انثى","حضانة","2023-01-29","مصطفى","00000000000","بلوك7"),
("685","352","11","عباس هشام","4","ذكر","روضة","2023-02-28","هشام","00000000000","بلوك7"),
("686","353","11","محمد هشام","3","ذكر","حضانة","2023-02-28","هشام","00000000000","بلوك7"),
("687","354","11","ميار احمد","5","انثى","تمهيدي","2023-01-30","احمد","00000000000","بلوك7"),
("688","355","11","حسين علي ","5","ذكر","تمهيدي","2023-02-24","علي","00000000000","بلوك7"),
("689","356","11","يمان علي ","3","انثى","حضانة","2023-02-24","علي","00000000000","بلوك7"),
("690","357","11","يحى وائل","4","ذكر","روضة","2023-02-01","وئل","00000000000","بلوك7"),
("691","358","11","غيث وائل","2","ذكر","حضانة","2023-02-01","وئل","00000000000","بلوك7"),
("692","359","11","كنده باسم","2","انثى","حضانة","2023-02-01","باسم","00000000000","بلوك7"),
("693","360","11","كيان زين العابدين","4","ذكر","روضة","2023-01-31","زين العابدين","00000000000","بلوك7"),
("694","361","11","يزن امير ","5","ذكر","تمهيدي","2023-01-30","امير","00000000000","بلوك7"),
("695","362","11","قمر امير ","3","انثى","حضانة","2023-01-30","امير","00000000000","بلوك7"),
("696","363","11","كيان محمد","4","ذكر","روضة","2023-02-01","محمد","00000000000","بلوك7"),
("697","364","11","فضل محمد","1","ذكر","حضانة","2023-02-01","محمد","00000000000","بلوك7"),
("698","365","11","اشتر زيد","3","ذكر","حضانة","2023-02-05","زيد","00000000000","بلوك7"),
("699","366","11","علي رضا زيد","4","ذكر","روضة","2023-02-05","زيد","00000000000","بلوك7"),
("700","367","11","شهم حيدر","4","ذكر","روضة","2023-02-05","حيدر","00000000000","بلوك7"),
("701","368","11","شمم حيدر","4","انثى","روضة","2023-02-05","حيدر","00000000000","بلوك7"),
("702","369","11","ضي وسام ","3","انثى","حضانة","2023-02-04","وسام","00000000000","بلوك7"),
("703","370","11","رزان كرار","4","انثى","روضة","2023-02-08","كرار","00000000000","بلوك7"),
("704","371","11","مريم كرار","4","انثى","روضة","2023-02-08","كرار","00000000000","بلوك7"),
("705","372","11","زين العابدين محمود","5","ذكر","تمهيدي","2023-02-08","محمود","00000000000","بلوك7"),
("706","373","11","زمرد محمود","3","انثى","حضانة","2023-02-08","محمود","00000000000","بلوك7"),
("707","374","11","زمرد محمود","3","انثى","حضانة","2023-02-08","محمود","00000000000","بلوك7"),
("708","375","11","موسى احمد","5","ذكر","تمهيدي","2023-02-08","احمد","00000000000","بلوك7"),
("709","376","11","فاطمه امير ","5","انثى","تمهيدي","2023-02-09","امير","00000000000","بلوك7"),
("710","377","11","علي امير ","4","ذكر","روضة","2023-02-09","امير","00000000000","بلوك7"),
("711","378","11","حمزه حسين","5","ذكر","تمهيدي","2023-02-09","حسين","00000000000","بلوك7"),
("712","379","11","دانيه حسين ","3","انثى","حضانة","2023-02-09","حسين","00000000000","بلوك7"),
("713","380","11","كيان علي ","5","ذكر","تمهيدي","2023-02-09","علي","00000000000","بلوك7"),
("714","381","11","يمان علي ","3","ذكر","حضانة","2023-02-09","علي","00000000000","بلوك7"),
("715","382","10","هيلين هيثم هلال","2","انثى","حضانة","2023-02-13","هيثم هلال","00000000000","بلوك6"),
("716","383","10","عباس علي عباس","5","ذكر","تمهيدي","2023-02-13","علي عباس","00000000000","بلوك6"),
("717","384","10","ناي علي عباس","3","انثى","تحضيري","2023-01-25","علي عباس","00000000000","بلوك6"),
("718","385","10","مريم حسن اكرم","2","انثى","حضانة","2023-02-14","حسن اكرم","00000000000","بلوك6"),
("719","386","10","امير علي عبد الكريم ","2","ذكر","حضانة","2023-02-18","علي عبد الكريم","00000000000","بلوك6"),
("720","387","10","ريتال حسن محسن","2","انثى","حضانة","2023-02-19","حسن محسن","00000000000","بلوك6"),
("721","388","10","بدر علي ياسين","5","ذكر","تمهيدي","2023-02-19","علي ياسين","00000000000","بلوك6"),
("722","389","10","الحسن عبد الكريم غالب","5","ذكر","تمهيدي","2023-02-19","عبد الكريم غالب","00000000000","بلوك6"),
("723","390","10","مينا احمد سامي","5","انثى","تمهيدي","2023-02-19","احمد سامي","00000000000","بلوك6"),
("724","391","10","لجين نبيل محمد","5","انثى","تمهيدي","2023-02-19"," نبيل محمد","00000000000","بلوك6"),
("725","392","10","نبيل علي صبحي","5","ذكر","روضة","2023-02-19","علي صبحي","00000000000","بلوك6"),
("726","393","10","هيا نبيل صبحي","22","انثى","حضانة","2023-02-19","نبيل صبحي","00000000000","بلوك6"),
("727","394","10","سدن الحسن ماجد","5","انثى","تمهيدي","2023-02-19","الحسن ماجد","00000000000","بلوك6"),
("728","395","10","ندى حيدر حميد","4","انثى","روضة","2023-02-19","حيدر حميد","00000000000","بلوك6"),
("729","396","10","علي طارق خالد","4","ذكر","روضة","2023-02-19"," طارق خالد","00000000000","بلوك6"),
("730","397","10","محمد طارق خالد","3","ذكر","تحضيري","2023-01-25"," طارق خالد","00000000000","بلوك6"),
("731","398","10","حسين عبد المحسن عيسى","5","ذكر","تمهيدي","2023-02-21","عبد المحسن عيسى","00000000000","بلوك6"),
("732","399","10","عبد الله امير علاء","4","ذكر","روضة","2023-02-19","امير علاء","00000000000","بلوك6"),
("733","400","10","ياسر امير علاء","6","ذكر","تمهيدي","2023-02-19","امير علاء","00000000000","بلوك6"),
("734","401","10","ديار ياسين طه","4","انثى","روضة","2023-02-19","ياسين طه","00000000000","بلوك6"),
("735","402","10","زينب صميم بسا","5","انثى","تمهيدي","2023-02-19","صميم بسام","00000000000","بلوك6"),
("736","403","10","زينب صميم بسام","5","انثى","تمهيدي","2023-02-19","صميم بسام","00000000000","بلوك6"),
("737","404","10","تاينا حيدر حسن","3","انثى","حضانة","2023-02-19","حيدر حسن","00000000000","بلوك6"),
("738","405","10","تاليا حسن علي","2","انثى","تحضيري","2023-01-25","حسن علي","00000000000","بلوك6"),
("739","406","10","لارا محمد داود","4","انثى","روضة","2023-02-19","محمد داود","00000000000","بلوك6"),
("740","407","10","ريان فواد نجم","2","انثى","حضانة","2023-02-21","فواد نجم","00000000000","بلوك6"),
("741","408","10","علي عباس حسن","4","ذكر","روضة","2023-02-18","عباس حسن","00000000000","بلوك6"),
("742","409","10","امير عباس حسن","3","ذكر","تحضيري","2023-01-25","عباس حسن","00000000000","بلوك6"),
("743","410","10","الياس محمد خير الله","3","ذكر","تحضيري","2023-01-25"," محمد خير الله","00000000000","بلوك6"),
("744","411","10","عسل اسماعيل جمعه","5","انثى","تمهيدي","2023-02-20"," اسماعيل جمعه","00000000000","بلوك6");
INSERT INTO stud_tb VALUES
("745","412","10","غزل عبد الحميد جعفر","4","انثى","روضة","2023-02-20"," عبد الحميد جعفر","00000000000","بلوك6"),
("746","413","10","ادم ضياء سيد","3","ذكر","تحضيري","2023-01-25","ضياء سيد","00000000000","بلوك6"),
("747","414","10","ادم مصطفى سلام","4","ذكر","روضة","2023-02-25","مصطفى سلام","00000000000","بلوك6"),
("748","415","10","ادم مصطفى سلام","4","ذكر","روضة","2023-02-25","مصطفى سلام","00000000000","بلوك6"),
("749","416","10","غيث الكرار حسن","2","ذكر","حضانة","2023-02-26","الكرار حسن","00000000000","بلوك6"),
("750","417","10","زهراء جعفر هادي","5","انثى","تمهيدي","2023-02-23","جعفر هادي","00000000000","بلوك6"),
("751","418","10","موسى جعفر هادي","3","ذكر","تحضيري","2023-01-25","جعفر هادي","00000000000","بلوك6"),
("752","419","10","موسى هادي","3","ذكر","حضانة","2023-02-23","هادي","00000000000","بلوك6"),
("753","420","10","موسى جعفر هادي","3","ذكر","حضانة","2023-02-23","جعفر هادي","00000000000","بلوك6"),
("754","421","10","ايليا سيف تركي","3","انثى","تحضيري","2023-01-25","سيف تركي","00000000000","بلوك6"),
("755","422","10","امنه سيف تركي","5","انثى","تمهيدي","2023-02-23","سيف تركي","00000000000","بلوك6"),
("756","423","10","ريتاج صلاح محسن","4","انثى","روضة","2023-02-19","صلاح محسن","00000000000","بلوك6"),
("757","424","10","محمد صلاح محسن","3","ذكر","تحضيري","2023-01-25","كرارصلاح محسن","00000000000","بلوك6"),
("758","425","10","علي احمد جلوب ","2","ذكر","حضانة","2023-02-24","احمد جلوب","00000000000","بلوك6"),
("759","426","10","مرتضى مصطفى ساجد","5","ذكر","تمهيدي","2023-01-23","مصطفى ساجد","00000000000","بلوك6"),
("760","427","10","فهد علي ماجد","4","ذكر","روضة","2023-01-25","علي ماجد","00000000000","بلوك6"),
("761","428","10","زين العابدين علي ماجد","3","ذكر","تحضيري","2023-01-25","علي ماجد","00000000000","بلوك6"),
("762","429","10","لارا اسعد حامد ","2","انثى","حضانة","2023-01-24","اسعد حامد","00000000000","بلوك6"),
("763","430","10","يوسف اسعد حامد","3","ذكر","تحضيري","2023-01-25","اسعد حامد","00000000000","بلوك6"),
("764","431","10","زينب الحوراء حسن عبد الهادي ","5","انثى","تمهيدي","2023-02-07"," حسن عبد الهادي","00000000000","بلوك6"),
("765","432","10","نسم حيدر اسماعيل","4","انثى","روضة","2023-01-26","حيدر سماعيل","00000000000","بلوك6"),
("766","433","10","رهف عدنان عباس ","5","انثى","تمهيدي","2023-01-30","عدنان عباس","00000000000","بلوك6"),
("767","434","10","در فاضل عباس","5","انثى","تمهيدي","2023-01-27","فاضل عباس","00000000000","بلوك6"),
("768","435","10","ديمه علي حسين","4","انثى","روضة","2023-02-01","علي حسين","00000000000","بلوك6"),
("769","436","10","ريان علي احمد","3","انثى","تحضيري","2023-01-25","علي احمد","00000000000","بلوك6"),
("770","437","10","هيلين دريد سلمان","4","انثى","روضة","2023-02-02","دريد سلمان","00000000000","بلوك6"),
("771","438","10","سيف علي منذر","5","ذكر","تمهيدي","2023-02-05","علي منذر","00000000000","بلوك6"),
("772","439","10","جود محمد عبد الحسين","5","ذكر","تمهيدي","2023-01-29","محمد عبد الحسين","00000000000","بلوك6"),
("773","440","10","جوليا محمد عبد الحسين","2","انثى","حضانة","2023-01-29","محمد عبد الحسين","00000000000","بلوك6"),
("774","441","10","ياسين عمار ماجد","3","ذكر","تحضيري","2023-01-25","عمار ماجد","00000000000","بلوك6"),
("775","442","10","ابراهيم اسعد يوسف","4","ذكر","روضة","2023-02-04","اسعد يوسف","00000000000","بلوك6"),
("776","443","10","فضل عادل ياسين ","5","ذكر","تمهيدي","2023-02-02","عادل ياسين","00000000000","بلوك6"),
("777","444","10","سيدرا سيف هيثم","5","انثى","تمهيدي","2023-02-06","سيف هيثم","00000000000","بلوك6"),
("778","445","10","علي سيف هيثم ","2","ذكر","حضانة","2023-02-06","سيف هيثم","00000000000","بلوك6"),
("779","446","10","هيا ليث محمد","5","انثى","تمهيدي","2023-02-03","ليث محمد","00000000000","بلوك6"),
("780","447","10","ميسم معمر محمد","2","انثى","حضانة","2023-02-02","معمر محمد","00000000000","بلوك6"),
("781","448","10","همام محمود فجيل","2","ذكر","حضانة","2023-02-08","محمود فجيل","00000000000","بلوك6"),
("782","449","10","رهف ناضر جلوب","4","انثى","روضة","2023-02-07","ناضر جلوب","00000000000","بلوك6"),
("783","450","10","فهد ناضر جلوب","2","ذكر","حضانة","2023-02-07","ناضر جلوب","00000000000","بلوك6"),
("784","451","10","علي احمد كيلو حاشوش","4","ذكر","روضة","2023-02-08","احمد كيلو حاشوش","00000000000","بلوك6"),
("785","452","10","عبد الله احمد كيلو حاشوش","3","ذكر","تحضيري","2023-01-25"," احمد كيلو حاشوش","00000000000","بلوك6"),
("786","453","10","يوسف فادي فائق","3","ذكر","تحضيري","2023-01-25","فادي فائق","00000000000","بلوك6"),
("787","454","10","ساره معتز كاظم","4","انثى","روضة","2023-02-09","معتز كاظم","00000000000","بلوك6"),
("788","455","10","رضا محمد عبد الامير","2","ذكر","حضانة","2023-02-08","محمد عبد الامير","00000000000","بلوك6"),
("789","456","10","ابراهيم مصطفى جليل","3","ذكر","تحضيري","2023-01-25"," مصطفى جليل","00000000000","بلوك6"),
("790","457","10","ادم علي محمود","4","ذكر","روضة","2023-02-10","علي محمود","00000000000","بلوك6"),
("791","458","10","در علي محمود","2","انثى","حضانة","2023-02-10","علي محمود","00000000000","بلوك6"),
("792","459","10","ريان نصير ناظم ","5","ذكر","تمهيدي","2023-02-11"," نصير ناظم","00000000000","بلوك6"),
("793","460","10","علي فاضل عبد الواحد","3","ذكر","تحضيري","2023-01-25","فاضل عبد الواحد","00000000000","بلوك6"),
("794","461","10","كرم مصطفى سلام","6","ذكر","تمهيدي","2023-02-12","مصطفى سلام","00000000000","بلوك6"),
("795","462","10","مصطفى قاسم طالب","3","ذكر","حضانة","2023-02-10","قاسم طالب","00000000000","بلوك6"),
("796","463","10","روز باسم حسين","5","ذكر","تمهيدي","2023-02-09","باسم حسين","00000000000","بلوك6"),
("797","464","10","لارين حسام سماعيل","3","انثى","تحضيري","2023-01-25","حسام سماعيل","00000000000","بلوك6"),
("798","465","10","ريتال مضر خالد","2","انثى","حضانة","2023-02-24"," مضر خالد","00000000000","بلوك6"),
("800","467","12","شمس محمد","2","انثى","حضانة","2023-01-22","محمد","00000000000","بلوك8"),
("802","468","11","شهد احمد","4","انثى","روضة","2023-02-13","احمد","00000000000","A7"),
("803","469","11","ادم احمد","1","ذكر","حضانة","2023-02-13","احمد","00000000000","A7"),
("804","470","11","اهم علاء","1","ذكر","حضانة","2023-02-19","علاء","00000000000","A7"),
("805","471","11","علي رضاحسين","4","ذكر","روضة","2023-02-19","حسين","00000000000","A7"),
("806","472","11","ادم خيام","5","ذكر","تمهيدي","2023-02-19","خيام","00000000000","A7"),
("807","473","11","يزن اياد","4","ذكر","روضة","2023-02-26","اياد","00000000000","A7"),
("808","474","11","ميار احمد","4","انثى","تمهيدي","2023-03-01","احمد","00000000000","A7"),
("809","475","11","مسرى تحسين","4","انثى","روضة","2023-02-26","تحسين","00000000000","A7"),
("810","476","8","فاطمه محمد","5","انثى","تمهيدي","2023-02-12","محمد","00000000000","a4"),
("811","477","8","مريم مصطفى","560","انثى","تمهيدي","2023-02-08","مصطفى","00000000000","B8"),
("812","478","8","ماريا عمر","5","انثى","تمهيدي","2023-02-19","عمر","00000000000","a4"),
("813","479","8","مريم حسام","2","انثى","حضانة","2023-02-19","حسام","00000000000","a4"),
("814","480","8","اسل حسام","4","انثى","روضة","2023-02-19","حسام","00000000000","a4"),
("815","481","8","ماريا علي","2","انثى","حضانة","2023-02-19","علي","00000000000","A3"),
("816","482","6","هاشم نور","5","ذكر","تمهيدي","2023-02-26","0000000000","00000000000","a7"),
("817","483","6","رانسا قصي","5","انثى","تمهيدي","2023-02-26","0000000000","00000000000","0000"),
("818","484","8","فهد احمد","3","ذكر","تحضيري","2023-02-19","احمد","00000000000","A3"),
("819","485","8","اجوان احمد","5","انثى","تمهيدي","2023-02-19","احمد","00000000000","A8"),
("820","486","8","حميد مضر","4","ذكر","تحضيري","2023-02-19","مضر حميد","00000000000","a4"),
("821","487","8","يزن هادي","4","ذكر","روضة","2023-02-19","هادي","00000000000","A5"),
("822","488","8","عبد الله هادي","3","ذكر","تحضيري","2023-02-19","هادي","00000000000","A5"),
("823","489","11","بنين قصي","4","انثى","روضة","2023-02-27","قصي","00000000000","A7"),
("824","490","11","رهف حسين","4","انثى","روضة","2023-02-27","حسين","00000000000","A7"),
("825","491","8","جنى مازن","5","انثى","تمهيدي","2023-02-19","مازن","00000000000","a4");




CREATE TABLE `users_tb` (
  `id_user` int(100) NOT NULL AUTO_INCREMENT,
  `user_name` varchar(250) NOT NULL,
  `user_pass` varchar(250) NOT NULL,
  `role` varchar(100) NOT NULL,
  PRIMARY KEY (`id_user`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4;


INSERT INTO users_tb VALUES
("1","admin","20172017","Admin"),
("6","A2","1234","User"),
("7","A3","1234","User"),
("8","A4","1234","User"),
("9","A5","1234","User"),
("10","A6","1234","User"),
("11","A7","1234","User"),
("12","A8","1234","User");


