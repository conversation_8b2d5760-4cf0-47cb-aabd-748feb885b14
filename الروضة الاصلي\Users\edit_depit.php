<?php
session_start();
if (isset($_SESSION['user'])) {
  if ($_SESSION['user']->role === "User") {
  } else {
    header("location:../login.php", true);
    die("");
    echo "dont work";
  }
} else {
  header("location:../login.php", true);
  die("");
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>تعديل بيانات المصاريف</title>
  <link rel="stylesheet" href="css/bootstrap.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
  <script src="js/jquery.min.js"></script>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <?php include "addon/topbar.php" ?>
  <?php include "addon/dbcon.php" ?>
</head>

<body>
<div class="wrapper2" id="tost_info">
        <div id="toast2">
            <div class="container-11">
            <i id="icon" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-22">
                <p class="p1">Done !</p>
                <p class="p2">تم التعديل على البينانات</p>
            </div>
        </div>
    </div>

  <?php
    $id = $_GET['id'];
    $sql = "SELECT * FROM depit_tb WHERE id=$id";
    $resul = mysqli_query($con, $sql);
    $row = mysqli_fetch_assoc($resul);
    $id = $row['id'];
    $depit_note = $row['depit_note'];
    $depit_date = $row['depit_date'];
    //$depit_date2=$row['depit_date2'];
    $depit_cash = $row['depit_cash'];
    $userID = $row['userID']; ?>
  <form method="POST">
<div class='contin_depit' id="test">

      <div class='input-box'>
        <label for="f_name"> وصف المصروفات <label>
            <textarea name="depit_note" id="" cols="30" rows="10" style="
    max-width: fit-content;
    text-align: center;
    padding: 5px;
    resize: none;
    max-height: 180px;"><?php echo $row['depit_note']; ?></textarea>
            <label for="depit_date">تاريخ المصروفات<label>
                <input type="date" name="depit_date" required value="<?php echo $row['depit_date']; ?>">
                <label for="depit_cash">قيمة المصروفات </label>
                <input type="number" name="depit_cash" required value="<?php echo $row['depit_cash']; ?>">
                <div>
                  <button class=btn name="addS" id="23">حفظ </button>
                </div>


</div>
</body>
<script>
    let x;
    let toast = document.getElementById("toast2");
         p1 = document.querySelector(".p1");
         p2 = document.querySelector(".p2");
    function StudToast(ts,ic,tx1,tx2,icC){
    let  icon = document.getElementById("icon");
    clearTimeout(x);
    icon.className=icC;
    toast.style.borderRight=ts;
    icon.style.color=ic;
    p1.innerText=tx1;
    p2.innerText=tx2;
    toast.style.transition='1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition='1s';
    x = setTimeout(()=>{
    toast.style.transform = "translateX(-500px)"
    }, 4200);
    if(icC=="fa fa-circle-check"){
      dispable();
      setInterval(()=>{
        window.location.href="info_depit.php"
      },4700)
      
    }else{
    }
}
</script>
<script>
      function dispable () { 

jQuery("#23").prop("disabled",true)
jQuery(".contin_depit").css("transition","3s")
jQuery(".contin_depit").css("opacity","0.0")


};
</script>
<?php
  $id = $_GET['id'];
  $sql = "SELECT * FROM depit_tb WHERE id=$id";
  $resul = mysqli_query($con, $sql);
  $row = mysqli_fetch_assoc($resul);
  $id = $row['id'];
  $depit_note = $row['depit_note'];
  $depit_date = $row['depit_date'];
  //$depit_date2=$row['depit_date2'];
  $depit_cash = $row['depit_cash'];
  $userID = $row['userID'];
  if (isset($_POST['addS'])) {
    $depit_note = $_POST['depit_note'];
    $depit_date = $_POST['depit_date'];
    //$depit_date2=$_POST['depit_date2'];
    $depit_cash = $_POST['depit_cash'];
    $addData = "UPDATE depit_tb SET depit_note='$depit_note',depit_date='$depit_date',depit_cash='$depit_cash' WHERE id='$id'";
    $resul = mysqli_query($con, $addData);
    if ($resul) {
      $msg1=" ! تمت ";
      $msg2="تم التعديل على بيانات المصروف بنجاح";
      $iconC="fa fa-circle-check";
      echo "<script>StudToast(' 8px solid rgb(3, 188, 77)','rgb(3, 188, 77)','$msg1','$msg2','$iconC')</script>";
    }
  }
  ?>


</html> 