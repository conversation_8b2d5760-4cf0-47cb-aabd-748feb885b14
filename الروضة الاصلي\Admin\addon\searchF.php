<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:http://localhost/roda/login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:http://localhost/roda/login.php",true);
        die("");
}

include "dbcon.php";
    
 if(isset($_GET['input']))
    {
      $Filter=$_GET['input'];
      $query="SELECT * FROM stud_tb,stud_pay,users_tb WHERE stud_pay.id_stud=stud_tb.id AND stud_tb.userID=users_tb.id_user AND CONCAT(id_note,userID,name,age,sex,catg,datein,date_exp,p_name,p_phone,loc,id_pay,user_name) LIKE '%$Filter%'";
      $query_run=mysqli_query($con,$query);
      if(mysqli_num_rows($query_run)>0){
        foreach($query_run as $items){
          $id=$items['id'];
          $date_in=strtotime(date('y-m-d'));
          $date_out=strtotime($items['date_exp']);
          $stat=$date_out-$date_in;
          $cek=floor($stat/(60*60*24));
          if($cek<=0){
            $mes='<h3 class=exp>منتهي</h3>';
        }elseif($cek<=10 & $cek>0){
            $mes='<h3 class=soon >قريبا</h3>';
         }else{
            $mes='<h3 class=still >فعال</h3>';
        }
          ?>
          <tr id="tr_<?php echo $id?>">
          <td><button type="button" class="btn btn-secondary mb-1" id="edit_bnt" name="update"> <a href=editstud.php?id=<?=$id?> class="text-light">تعديل </a></button> 
          <button type="button" class="btn btn-secondary mb-1" id="reomve_btn" name="remove" onclick="deletdata(<?php echo $id ?>)">حذف </button>
          <button type="button" class="btn btn-secondary mb-2" id="renew_btn" name="remove"><a href=renew.php?renewId=<?=$id?> class="text-light">تجديد </a></button></td>
          <td><?= $mes;?></td>
          <td><?= $items['user_name'];?></td>
          <td><?= $items['date_exp'];?></td>
          <td><?= number_format($items['cash_stud']);?></td>
          <td><?= $items['datein'];?></td>
          <td><?= $items['loc'];?></td>
          <td><?= $items['p_phone'];?></td>
          <td><?= $items['p_name'];?></td>
          <td><?= $items['catg'];?></td>
          <td><?= $items['sex'];?></td>
          <td><?= $items['age'];?></td>
          <td><?= $items['name'];?></td>
          <td><?= $items['id_pay'];?></td>
          </tr>
        <?php
 

        }
    }else{
        echo "<td colspan=14 style='font-size: 25px;'>لاتوجد بيانات بهذا الوصف</td>";
    }
    }



    ?>
   