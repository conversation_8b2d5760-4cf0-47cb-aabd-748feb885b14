<?php
session_start();
if (isset($_SESSION['user'])) {
    if ($_SESSION['user']->role === "User") {
    } else {
        header("location:../login.php", true);
        die("");
        echo "dont work";
    }
} else {
    header("location:../login.php", true);
    die("");
}

include("dbcon.php");

// طباعة البيانات الواردة للتتبع
error_log("POST Data: " . print_r($_POST, true));

// التحقق من وجود البيانات المطلوبة لحذف السجل
if (isset($_POST['employee_id']) && isset($_POST['record_date'])) {
    $employee_id = intval($_POST['employee_id']);
    $record_date = mysqli_real_escape_string($con, $_POST['record_date']);
    
    // طباعة للتأكد من القيم
    error_log("Employee ID: " . $employee_id . ", Record Date: " . $record_date);
    
    // التحقق من وجود السجل أولاً
    $check_query = "SELECT * FROM stat2 WHERE id_employee = $employee_id AND data_stat = '$record_date'";
    $check_result = mysqli_query($con, $check_query);
    
    if (!$check_result) {
        echo "error: " . mysqli_error($con);
        exit;
    }
    
    if (mysqli_num_rows($check_result) == 0) {
        echo "not_found";
        exit;
    }
    
    // حذف السجل
    $delete_query = "DELETE FROM stat2 WHERE id_employee = $employee_id AND data_stat = '$record_date'";
    $delete_result = mysqli_query($con, $delete_query);
    
    if ($delete_result) {
        if (mysqli_affected_rows($con) > 0) {
            echo "done";
        } else {
            echo "not_found";
        }
    } else {
        echo "error: " . mysqli_error($con);
    }
} 
// معالجة إضافة سجل جديد (للاستخدام المستقبلي إذا لزم الأمر)
elseif (isset($_POST['id']) && isset($_POST['stat'])) {
    $id = intval($_POST['id']);
    $stat = mysqli_real_escape_string($con, $_POST['stat']);
    $datenow = date('Y-m-d');
    
    // معالجة تواريخ الإجازة إذا كانت موجودة
    $leave_start = null;
    $leave_end = null;
    if (isset($_POST['leave_start']) && isset($_POST['leave_end'])) {
        $leave_start = mysqli_real_escape_string($con, $_POST['leave_start']);
        $leave_end = mysqli_real_escape_string($con, $_POST['leave_end']);
    }

    // تحقق من وجود تسجيل مسبق لهذا الموظف اليوم
    $check = mysqli_query($con, "SELECT * FROM stat2 WHERE id_employee = $id AND data_stat = '$datenow'");
    
    if (mysqli_num_rows($check) > 0) {
        echo 1; // تم تسجيل الموظف اليوم
        exit;
    }

    // تحقق من حالة الإجازة الحالية
    $leave_check = mysqli_query($con, "SELECT * FROM stat2 WHERE id_employee = $id 
                                      AND stat_employee = 'إجازة' 
                                      AND (leave_start_date <= '$datenow' AND leave_end_date >= '$datenow')");
    
    if (mysqli_num_rows($leave_check) > 0) {
        if ($stat == 'غائب' || $stat == 'حاضر') {
            echo "has_leave"; // عنده إجازة، ما ينفعش يسجل حاضر أو غياب
            exit;
        }
    }

    // تسجيل الحالة الجديدة
    if ($stat == 'إجازة' && $leave_start && $leave_end) {
        $insert = mysqli_query($con, "INSERT INTO stat2 (id_employee, stat_employee, data_stat, leave_start_date, leave_end_date) 
                                     VALUES ($id, '$stat', '$datenow', '$leave_start', '$leave_end')");
    } else {
        $insert = mysqli_query($con, "INSERT INTO stat2 (id_employee, stat_employee, data_stat) 
                                     VALUES ($id, '$stat', '$datenow')");
    }
    
    if ($insert) {
        echo "done";
    } else {
        echo "error: " . mysqli_error($con);
    }
} else {
    echo "missing_data";
}
?>