<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل بيانات المستخدمين</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <script src="js/jquery.min.js"></script>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
   </head>
    <body>
    <?php 
    $id=$_GET['id'];
    $sql="SELECT * FROM users_tb WHERE id_user=$id";
    $resul=mysqli_query($con,$sql);
    $row=mysqli_fetch_assoc($resul);
          $id=$row['id_user'];
          $user_name=$row['user_name'];
          $user_pass=$row['user_pass'];
          $role=$row['role'];

    ?>
  
   <div class="wrapper2" id="tost_info">
        <div id="toast2">
            <div class="container-11">
            <i id="icon" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-22">
                <p class="p1">Done !</p>
                <p class="p2">تم اضافة البينانات</p>
            </div>
        </div>
    </div>
  
<form method="POST">
 <div class='contin_user'>

  <div class='input-box'>
        <label for="f_name"> اسم المستخدم  <label>
        <input type="text" name="user_name" required value="<?php echo $row['user_name'];?>">
        <label for="user_pass"> كلمة المرور  <label>
        <input type="text" name="user_pass" required value="<?php echo $row['user_pass'];?>">
        <label for="job" >صلاحية المستخدم</label>
       
        <div> <select name="role" id='selc'>
            <option value="Admin"> ادمن</option>
            <option value="User"> مدير</option>

        </select>
        
  </div>
        <button class=btn name="addS" id="23">حفظ </button>
 </div>
</form>
  
   </body>
 
   <script>
    let x;
    let toast = document.getElementById("toast2");
         p1 = document.querySelector(".p1");
         p2 = document.querySelector(".p2");
    function StudToast(ts,ic,tx1,tx2,icC){
    let  icon = document.getElementById("icon");
    
    clearTimeout(x);
    icon.className=icC;
    toast.style.borderRight=ts;
    icon.style.color=ic;
    p1.innerText=tx1;
    p2.innerText=tx2;
    toast.style.transition='1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition='1s';
    x = setTimeout(()=>{
    toast.style.transform = "translateX(-500px)"
    }, 4200);
    if(icC=="fa fa-circle-check"){
      dispable ();
      setInterval(()=>{
        window.location.href="info_user.php"
      },4700)
      
    }else{
    }
}
  </script>
  <script>
     function dispable () { 

        jQuery("#23").prop("disabled",true)
        jQuery(".contin_user").css("transition","3s")
        jQuery(".contin_user").css("opacity","0.0")
        
        
    };
</script>
    <?php
     if(isset($_POST['addS'])){
      $user_name=$_POST['user_name'];
      $user_pass=$_POST['user_pass'];
      $role=$_POST['role'];
      $addData="UPDATE users_tb SET  user_name='$user_name',user_pass='$user_pass',role='$role' WHERE id_user=$id";
      $resul=mysqli_query($con,$addData);
      if($resul){
        $msg1=" ! تمت ";
        $msg2="تم التعديل على بيانات المستخدم  بنجاح";
        $iconC="fa fa-circle-check";
        echo "<script> StudToast(' 8px solid rgb(3, 188, 77)','rgb(3, 188, 77)','$msg1','$msg2','$iconC')</script>";
        
      }
    }
    ?>
</html>