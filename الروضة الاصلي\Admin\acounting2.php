<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحاسابات حسب التاريخ</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.dataTables.min.js"></script>
    <link rel="stylesheet" href="css/jquery.dataTables.min.css">

    <style>
        /* تحسين شكل الجداول مع DataTables */
        .dataTables_wrapper {
            margin: 20px 0;
        }

        .dataTables_filter {
            float: right;
            text-align: right;
        }

        .dataTables_filter input {
            margin-right: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
        }

        .dataTables_length {
            float: left;
        }

        .dataTables_info {
            float: left;
            margin-top: 10px;
        }

        .dataTables_paginate {
            float: right;
            margin-top: 10px;
        }

        table.dataTable thead th {
            background-color: #343a40;
            color: white;
            border-bottom: 2px solid #dee2e6;
        }

        table.dataTable thead .sorting,
        table.dataTable thead .sorting_asc,
        table.dataTable thead .sorting_desc {
            background-repeat: no-repeat;
            background-position: center left;
            padding-left: 30px;
            cursor: pointer;
        }

        table.dataTable thead .sorting {
            background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAAZ0lEQVQ4EWNgGAWjYBSMglEwCkbBKBgFo2AUjIJRMAqGNdiAiQ25f+9+4Yj/8/cLR/yfv184EiZOnAgTOnDgAEzIyckJJgQWYGJiggkxMjLChGxtbWFCYWFhMCEXFxeYEFiAhYUFJgQWAABYWyD9mPUE5gAAAABJRU5ErkJggg==");
        }

        table.dataTable thead .sorting_asc {
            background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAAZ0lEQVQ4EWNgGAWjYBSMglEwCkbBKBgFo2AUjIJRMAqGNdiAiQ25f+9+4Yj/8/cLR/yfv184EiZOnAgTOnDgAEzIyckJJgQWYGJiggkxMjLChGxtbWFCYWFhMCEXFxeYEFiAhYUFJgQWAABYWyD9mPUE5gAAAABJRU5ErkJggg==");
        }

        table.dataTable thead .sorting_desc {
            background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAAaklEQVQ4EWNgGAWjYBSMglEwCkbBKBgFo2AUjIJRMAqGNdiAiQ25f+9+4Yj/8/cLR/yfv184EiZOnAgTOnDgAEzIyckJJgQWYGJiggkxMjLChGxtbWFCYWFhMCEXFxeYEFiAhYUFJgQWAAA+UyD9s/UE5gAAAABJRU5ErkJggg==");
        }

        .accounting-section {
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .accounting-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
        }
    </style>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>

   </head>
   <body>
   <form action="" method="POST">
  <div class="search_ac">
     <select name="acout_type" id="selc2" placeholder='اختر مستخدم'>
      <option value="0" >اختر مستخدم </option>
     <?php
            $sql="SELECT * FROM users_tb ";
            $result=mysqli_query($con,$sql);
            if($result){
                while($row=mysqli_fetch_assoc($result)){
                    $user_name=$row['user_name'];
                    $id=$row['id_user'];
                echo '<option value='.$id.'>'.$user_name.'</option>';
                }
            }
            ?>
     
     </select>
    <input name="datee" type="date" required>
    <label for=""> الى تاريخ</label>
    <input name="dates" type="date" required>
    <label for="">اختر من تاريخ</label>
    <button class="btn btn-warning" name='myInput'  > بحث</button>
    <button type="button" class="btn btn-primary" onclick="printAccountingReport()">طباعة التقرير</button>

  </div>
 

      </div>
      <div class="wrapper2" id="tost_info">
        <div id="toast2">
            <div class="container-11">
            <i id="icon" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-22">
                <p id="titlecash">مجموع الايرادات  </p>
                <p id="text">تم اضافة البينانات</p>
            </div>
        </div>
    </div>
    <div class="wrapper3" id="tost_info">
        <div id="toast3">
            <div class="container-33">
            <i id="icon3" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-33">
                <p id="titlecash3">مجموع المصروفات</p>
                <p id="text3">تم اضافة البينانات</p>
            </div>
        </div>
    </div>
    <div class="wrapper5" id="tost_info">
        <div id="toast5">
            <div class="container-55">
            <i id="icon5" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-55">
                <p id="titlecash5"></p>
                <p id="text5">  مجموع الرواتب </p>
            </div>
        </div>
    </div>
    <div class="wrapper4" id="tost_info">
        <div id="toast4">
            <div class="container-44">
            <i id="icon4" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-44">
                <p id="titlecash4">صافي الربح او الخسارة</p>
                <p id="text4">تم اضافة البينانات</p>
            </div>
        </div>
    </div>
    <script >
let x;
let toast = document.getElementById("toast2"),
    text=document.getElementById("text"),
    titlecash=document.getElementById("titlecash"),
    icon=document.getElementById("icon")
function Toastincoming(ss){
  cash=new Intl.NumberFormat('de-DE', { style: 'currency' ,currency:"IQD" }).format(ss);
    clearTimeout(x);
    titlecash.innerText="مجموع الايرادات "
    icon.className="fa-solid fa-money-bills";
    text.style.fontSize="18px"
    text.innerText=cash;
    toast.style.transition='1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition='1s';
    x = setTimeout(()=>{
        toast.style.transform = "translateX(-500px)"
    }, 500000);
}
</script>
<script >
let y;
let toast3 = document.getElementById("toast3"),
    text3=document.getElementById("text3"),
    titlecash3=document.getElementById("titlecash3"),
    icon3=document.getElementById("icon3");

function ToastDepit(bb){
    cash2=new Intl.NumberFormat('de-DE', { style: 'currency' ,currency:"IQD" }).format(bb);
    clearTimeout(y);
    toast3.style.borderRight="8px solid rgb(255 9 9)";
    icon3.style.color="red"
    icon3.className="fa-solid fa-money-bill-1-wave";
    text3.style.fontSize="18px"
    text3.innerText=cash2;
    toast3.style.transition='1s';
    toast3.style.transform = "translateX(0)";
    toast3.style.transition='1s';
    y=setTimeout(()=>{
        toast3.style.transform = "translateX(-500px)"
    }, 600000)   
}

</script>
<script >
let c;
let toast5 = document.getElementById("toast5"),
    text5=document.getElementById("text5"),
    titlecash5=document.getElementById("titlecash5"),
    icon5=document.getElementById("icon5");
function tostemploye(xx){
    cash5=new Intl.NumberFormat('de-DE', { style: 'currency' ,currency:"IQD" }).format(xx);
    clearTimeout(c);
    titlecash5.innerText="مجموع الرواتب "
    toast5.style.borderRight="8px solid rgb(178, 102, 255)";
    icon5.style.color="rgb(178, 102, 255)"
    icon5.className="fa-solid fa-people-group";
    text5.style.fontSize="18px"
    text5.innerText=cash5;
    toast5.style.transition='1s';
    toast5.style.transform = "translateX(0)";
    toast5.style.transition='1s';
    c=setTimeout(()=>{
        toast5.style.transform = "translateX(-500px)"
    }, 650000)   
}

</script>
<script >
let z;
let toast4 = document.getElementById("toast4"),
    text4=document.getElementById("text4"),
    titlecash4=document.getElementById("titlecash4"),
    icon4=document.getElementById("icon4");
function prof(cc){
    cash3=new Intl.NumberFormat('de-DE', { style: 'currency' ,currency:"IQD" }).format(cc);
    clearTimeout(z);
    toast4.style.borderRight="8px solid rgb(0 194 255)";
    icon4.style.color="#00c2ff"
    icon4.className="fa-solid fa-sack-dollar";
    text4.style.fontSize="18px"
    text4.innerText=cash3;
    toast4.style.transition='1s';
    toast4.style.transform = "translateX(0)";
    toast4.style.transition='1s';
    z = setTimeout(()=>{
        toast4.style.transform = "translateX(-500px)"
    }, 700000);

}

</script>


    <?php
    if(isset($_POST['myInput']))
    { 
      if($_POST['acout_type']){
      $id=$_POST['acout_type'];
      $dates=$_POST['dates'];
      $datee=$_POST['datee'];
      $query=$query="SELECT * FROM users_tb,stud_tb,stud_pay WHERE users_tb.id_user=$id AND stud_pay.id_stud=stud_tb.id AND stud_tb.userID=users_tb.id_user AND DATE(stud_tb.datein) BETWEEN '$dates' AND '$datee'";
      $query_run=mysqli_query($con,$query);

        ?>
        <div class="accounting-section">
            <h3><i class="fa-solid fa-graduation-cap"></i> جدول الطلاب والاشتراكات</h3>
          <table class="table" id="studentsTable">
        <thead>
          <tr>
            <th scope="col"> مستخدم الحضانة </th>
            <th scope="col">تاريخ  التسجيل</th>
            <th scope="col">قيمة الاشتراك</th>
            <th scope="col">اسم  الطالب</th>
            <th scope="col">رقم   الوصل </th>

          </tr>
        </thead>
          <?php
          $count=0;
         foreach($query_run as $items){
          $count+=$items['cash_stud'];
        

          ?>
          <tr>
          <td><?= $items['user_name'];?></td>
          <td><?= $items['datein'];?></td>
          <td><?=number_format($items['cash_stud']);?></td>
          <td><?= $items['name'];?></td>
          <td><?= $items['id_note'];?></td>
          </tr>
         </tbody>
 
 
        <?php
        

        
      }
      echo " <script> Toastincoming(".$count.") </script>";
      }

    }
    ?>
        </div> <!-- إغلاق قسم جدول الطلاب -->
    <?php
 
        if(isset($_POST['myInput'])){
          if($_POST['acout_type']){
            $id=$_POST['acout_type'];
          $dates=$_POST['dates'];
          $datee=$_POST['datee'];
        $query2="SELECT * FROM users_tb,depit_tb WHERE users_tb.id_user=$id AND depit_tb.userID=users_tb.id_user AND DATE(depit_tb.depit_date) BETWEEN '$dates' AND '$datee'";
        $query_run2=mysqli_query($con,$query2);
        $count2=0;
        ?>
        <div class="accounting-section">
            <h3><i class="fa-solid fa-money-bill-transfer"></i> جدول المصروفات</h3>
        <table class="table" id="expensesTable">
        <thead>
          <tr>
            <th scope="col"> مستخدم الحضانة </th>
            <th scope="col"> التاريخ الاسبوعي</th>
            <th scope="col">قيمة المصروفات</th>
            <th scope="col"> وصف المصروفات </th>

          </tr>
        </thead>
     
        <?php
        foreach($query_run2 as $items2){
          
          $count2+=$items2['depit_cash'];
          ?>
          <tr>
           <td><?= $items2['user_name'];?></td>
           <td><?= $items2['depit_date'];?></td>
           <td><?= number_format($items2['depit_cash']);?></td>
           <td><?= $items2['depit_note'];?></td>
           </tr>
           </tbody>
 
 
        <?php
 

        }
        $final=$count-$count2;
        echo " <script>ToastDepit(".$count2.")</script>";

        }
        ?>
        </div> <!-- إغلاق قسم جدول المصروفات -->
        <?php
        if(isset($_POST['myInput'])){
          if($_POST['acout_type']){
            $id=$_POST['acout_type'];
        $query3="SELECT * FROM employ_tb,users_tb WHERE users_tb.id_user=employ_tb.userID AND users_tb.id_user=$id";
        $query_run=mysqli_query($con,$query3);
        $count3=0;
        ?>
        <div class="accounting-section">
            <h3><i class="fa-solid fa-user-group"></i> جدول الموظفين والرواتب</h3>
        <table class="table" id="employeesTable">
        <thead>
          <tr>
            <th scope="col"> مستخدم الحضانة </th>
            <th scope="col"> الراتب الشهري </th>
            <th scope="col">المسمى الوظيفي</th>
            <th scope="col"> تاريخ المباشرة</th>
            <th scope="col"> اسم الموظف</th>

          </tr>
        </thead>
     
        <?php
        foreach($query_run as $items){
            $salary= $items['salary'];
            $count3=$count3+$salary;
            ?>
            <tr>
            <td><?= $items['user_name']; ?></td>
            <td> IQD <?= number_format($count3); ?></td>
            <td><?= $items['job']; ?></td>
            <td><?= $items['date_start']; ?></td>
            <td><?= $items['f_name']; ?></td>
            </tr>
           <?php
           
        }
   
      } 
    
      }
      $final=$count-$count2;
      $final=$final-$count3;
      echo"<script>tostemploye(".$count3.")</script>";
      echo " <script>prof(".$final.")</script>";
    }


    ?>
        </div> <!-- إغلاق قسم جدول الموظفين -->
    <?php

<script>
$(document).ready(function() {
    // تفعيل DataTables للجداول الثلاثة مع إعدادات اللغة العربية

    // إعدادات اللغة العربية
    var arabicLanguage = {
        "sProcessing": "جارٍ التحميل...",
        "sLengthMenu": "أظهر _MENU_ مدخلات",
        "sZeroRecords": "لم يعثر على أية سجلات",
        "sInfo": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
        "sInfoEmpty": "يعرض 0 إلى 0 من أصل 0 سجل",
        "sInfoFiltered": "(منتقاة من مجموع _MAX_ مُدخل)",
        "sInfoPostFix": "",
        "sSearch": "ابحث:",
        "sUrl": "",
        "oPaginate": {
            "sFirst": "الأول",
            "sPrevious": "السابق",
            "sNext": "التالي",
            "sLast": "الأخير"
        },
        "oAria": {
            "sSortAscending": ": تفعيل لترتيب العمود تصاعدياً",
            "sSortDescending": ": تفعيل لترتيب العمود تنازلياً"
        }
    };

    // تفعيل DataTables لجدول الطلاب
    if ($('#studentsTable').length) {
        $('#studentsTable').DataTable({
            "language": arabicLanguage,
            "pageLength": 25,
            "order": [[ 1, "desc" ]], // ترتيب حسب التاريخ تنازلياً
            "columnDefs": [
                { "orderable": true, "targets": "_all" }
            ]
        });
    }

    // تفعيل DataTables لجدول المصروفات
    if ($('#expensesTable').length) {
        $('#expensesTable').DataTable({
            "language": arabicLanguage,
            "pageLength": 25,
            "order": [[ 1, "desc" ]], // ترتيب حسب التاريخ تنازلياً
            "columnDefs": [
                { "orderable": true, "targets": "_all" }
            ]
        });
    }

    // تفعيل DataTables لجدول الموظفين
    if ($('#employeesTable').length) {
        $('#employeesTable').DataTable({
            "language": arabicLanguage,
            "pageLength": 25,
            "order": [[ 4, "asc" ]], // ترتيب حسب اسم الموظف تصاعدياً
            "columnDefs": [
                { "orderable": true, "targets": "_all" }
            ]
        });
    }
});

// وظيفة طباعة التقرير
function printAccountingReport(){
    // إنشاء نافذة جديدة للطباعة
    var printWindow = window.open('', '_blank');

    // جمع محتوى جميع الجداول
    var studentsTable = document.getElementById('studentsTable');
    var expensesTable = document.getElementById('expensesTable');
    var employeesTable = document.getElementById('employeesTable');

    var tablesContent = '';

    if (studentsTable) {
        tablesContent += '<h3 style="color: #333; margin-top: 30px;">جدول الطلاب والاشتراكات</h3>';
        tablesContent += studentsTable.outerHTML;
    }

    if (expensesTable) {
        tablesContent += '<h3 style="color: #333; margin-top: 30px;">جدول المصروفات</h3>';
        tablesContent += expensesTable.outerHTML;
    }

    if (employeesTable) {
        tablesContent += '<h3 style="color: #333; margin-top: 30px;">جدول الموظفين والرواتب</h3>';
        tablesContent += employeesTable.outerHTML;
    }

    // إنشاء محتوى HTML للطباعة
    var printContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تقرير المحاسبة</title>
        <style>
            @font-face {
                font-family:"LamaSans-Medium";
                src: url(css/JannaLT.ttf);
            }

            body {
                font-family: "LamaSans-Medium", Arial, sans-serif;
                direction: rtl;
                margin: 20px;
                background: white;
            }

            .print-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #333;
                padding-bottom: 20px;
            }

            .print-header h1 {
                color: #333;
                margin-bottom: 10px;
                font-size: 24px;
            }

            .print-header p {
                color: #666;
                margin: 5px 0;
                font-size: 14px;
            }

            h3 {
                color: #333;
                font-size: 18px;
                margin: 20px 0 10px 0;
                padding: 10px;
                background-color: #f8f9fa;
                border-right: 4px solid #007bff;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                font-size: 11px;
            }

            table th, table td {
                border: 1px solid #333;
                padding: 6px;
                text-align: center;
            }

            table thead {
                background-color: #f8f9fa;
                font-weight: bold;
            }

            table thead th {
                background-color: #333;
                color: white;
                font-weight: bold;
            }

            .print-footer {
                margin-top: 30px;
                text-align: center;
                font-size: 12px;
                color: #666;
                border-top: 1px solid #ccc;
                padding-top: 10px;
            }

            @media print {
                body { margin: 0; }
                .print-header { page-break-inside: avoid; }
                table { page-break-inside: auto; }
                tr { page-break-inside: avoid; page-break-after: auto; }
                h3 { page-break-after: avoid; }
            }
        </style>
    </head>
    <body>
        <div class="print-header">
            <h1>تقرير المحاسبة الشامل</h1>
            <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</p>
            <p>وقت الطباعة: ${new Date().toLocaleTimeString('ar-EG')}</p>
        </div>

        ${tablesContent}

        <div class="print-footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الروضة</p>
        </div>
    </body>
    </html>
    `;

    // كتابة المحتوى في النافذة الجديدة
    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى ثم طباعة
    printWindow.onload = function() {
        printWindow.print();
        printWindow.close();
    };
}
</script>

   </body>
</html>