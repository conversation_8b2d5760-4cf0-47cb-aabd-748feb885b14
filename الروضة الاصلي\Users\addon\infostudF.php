<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

include "dbcon.php";
$idGet=$_GET['id'];
      $sql="SELECT * FROM stud_tb,stud_pay,users_tb WHERE stud_pay.id_stud=stud_tb.id AND stud_tb.userID=users_tb.id_user AND users_tb.id_user=$idGet ORDER BY `stud_tb`.`datein` DESC";
      $result=mysqli_query($con,$sql);
      if(mysqli_num_rows($result)>0){
       while($row=mysqli_fetch_assoc($result)) {
        
          $id=$row['id'];
          $id_note=$row['id_note'];
          $name=$row['name'];
          $age=$row['age'];
          $sex=$row['sex'];
          $catg=$row['catg'];
          $datein=$row['datein'];
          $p_name=$row['p_name'];
          $p_phone=$row['p_phone'];
          $loc=$row['loc'];
          $date_exp=$row['date_exp'];
          $cash_stud=number_format($row['cash_stud']);
          $user_name=$row['user_name'];
          $id_pay=$row['id_pay'];
          $date_in=strtotime(date('y-m-d'));
          $date_out=strtotime($date_exp);
          $stat=$date_out-$date_in;
          $cek=floor($stat/(60*60*24));
          if($cek<=0){
            $mes='<h3 class=exp>منتهي</h3>';
            $btndelet='<button type="button" class="btn btn-secondary mb-1" id="reomve_btn" name="remove" onclick="deletdata('.$id.')">حذف </button>';
            $btnv='<button type="button" class="btn btn-secondary mb-1" id="renew_btn" name="remove"><a href="renew.php?renewId='.$id.'" class="text-light">تجديد </a></button>';
        }elseif($cek<=10 & $cek>0){
          $btndelet="";
            $mes='<h3 class=soon >قريبا</h3>';
         }else{
          $btndelet="";
          $btnv="";
          $mes='<h3 class=still >فعال</h3>';
        }
         $currentDate = new DateTime();
                $targetDate = new DateTime($datein);
$regStatus='old';
                if ($currentDate->format('Y-m') == $targetDate->format('Y-m')) {
                    $regStatus = 'جديد';
                    $classStatus="still";
                } else {
                    $regStatus = 'قديم';
                    $classStatus="soon";

                }

 
        ?>
          <tr id="tr_<?php echo $id ?>">
          <td><button type="button" class="btn btn-secondary mb-1"id="edit_bnt" name="update" > <a style="text-decoration: none;color:whitesmoke;" href="editstud.php?id=<?php echo $id?>">تعديل</a>  </button> 
          <?php echo $btnv   ?>
          <?php echo $btndelet   ?>
          </td>
          <td><p class="<?php echo $classStatus; ?>"><?php echo $regStatus ?></p></td>
          <td> <?php echo $cek?> </td>
          <td> <?php echo $mes?> </td>
          <td> IQD <?php echo $cash_stud?>  </td>
          <td><?php echo $date_exp?>  </td>
          <td><?php echo $datein?></td>
          <td><?php echo $p_phone?></td>
          <td><?php echo $catg?></td>
          <td><?php echo $sex?></td>
          <td><?php echo $age?></td>
          <td><?php echo $loc?></td>
          <td><?php echo $name?></td>
          <td><?php echo $id_pay?></td>
          
          </tr>
          
         <?php

         }
         $datenow=date('Y-m-d');
         $Date2=date('Y-m-d', strtotime($datenow. ' + 1 days'));
         $Date4=date('Y-m-d', strtotime($datenow. ' + 365 days'));
           $sql2="SELECT * FROM stud_tb,stud_pay,users_tb WHERE DATE(stud_pay.date_exp) BETWEEN date('$Date2')AND Date('$Date4') AND stud_tb.id=stud_pay.id_stud AND users_tb.id_user=stud_tb.userID AND users_tb.id_user=$idGet ";
           $result2=mysqli_query($con,$sql2);
           $sum=mysqli_num_rows($result2);
          
           echo "<script>showToast2(".$sum.")</script>";
           
       }else{
        
        echo "<td colspan=15 style='font-size: 25px;'>لاتوجد بيانات  طلاب لهذا المستخدم</td>";
        echo "<script>reomtost()</script>";
      }
      

    ?>