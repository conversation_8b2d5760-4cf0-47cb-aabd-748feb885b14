<?php
session_start();
if (isset($_SESSION['user'])) {
    if ($_SESSION['user']->role !== "Admin") {
        header("location:../login.php", true);
        die("");
    }
} else {
    header("location:../login.php", true);
    die("");
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title> تقارير الفعالين </title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/jquery.dataTables.min.css">
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
    <script src="js/all.min.js"></script>
    <script src="js/jquery.dataTables.min.js"></script>
    <?php include "addon/topbar.php"; ?>
    <?php include "addon/dbcon.php"; ?>
</head>
<body>
    <form action="" method="POST">
        <div class="search_ac">
            <input name="datee" type="date" required>
            <label>الى تاريخ</label>
            <input name="dates" type="date" required>
            <label>اختر من تاريخ</label>
            <button class="btn btn-warning" name="myInput"> اظهار</button> 
            <?php
            if (isset($_POST['myInput'])) {
                $dates = $_POST['dates'];
                $datee = $_POST['datee'];
                echo '<button class="btn btn-success text-light"><a href="addon/exportStudAc.php?dates='.$dates.'&datee='.$datee.'" class="text-light">تحميل اكسل</a></button>';
            }
            ?>
        </div>

        <?php
        if (isset($_POST['myInput'])) {
            $dates = $_POST['dates'];
            $datee = $_POST['datee'];
            //$query = "SELECT * FROM stud_tb, stud_pay, users_tb WHERE stud_pay.id_stud = stud_tb.id AND DATE(stud_tb.datein) BETWEEN '$dates' AND '$datee'";
            $query="SELECT * FROM stud_tb, stud_pay, users_tb WHERE  DATE(stud_tb.datein) BETWEEN '$dates' AND '$datee' AND stud_pay.id_stud = stud_tb.id AND users_tb.id_user=stud_tb.userID";
            $query_run = mysqli_query($con, $query);

            if (mysqli_num_rows($query_run) > 0) {
                ?>
                <table id="Table" class="table">
                    <thead>
                        <tr>
                            <th scope="col"> عدد الطلاب : <?php echo mysqli_num_rows($query_run); ?></th>
                            <th scope="col"> مستخدم الحضانة </th>
                            <th scope="col">تاريخ التسجيل</th>
                            <th scope="col">قيمة الاشتراك</th>
                            <th scope="col">اسم الطالب</th>
                            <th scope="col">رقم الوصل</th>
                        </tr>
                    </thead>
                    <tbody>
                    <?php
                    while ($items = mysqli_fetch_assoc($query_run)) {
                        ?>
                        <tr>
                            <td>فعال</td>
                            <td><?= $items['user_name']; ?></td>
                            <td><?= $items['datein']; ?></td>
                            <td>IQD <?= number_format($items['cash_stud']); ?></td>
                            <td><?= $items['name']; ?></td>
                            <td><?= $items['id_pay']; ?></td>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                </table>
                <script>
                    $(document).ready(function () {
                        // Initialize DataTable after the table is rendered
                        $("#Table").DataTable();
                    });
                </script>
                <?php
            } else {
                echo "<h3 class='aades-note'>لاتوجد ايرادات لهذا التاريخ</h3>";
            }
        }
        ?>
    </form>
</body>
</html>
