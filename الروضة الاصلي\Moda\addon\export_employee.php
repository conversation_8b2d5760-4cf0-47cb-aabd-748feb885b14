<?php
// export_employee_range.php - نسخة محسنة ومُصححة
$user = "kidzrcle_rwda";
$pass = "kidzrcle_rwda";
$host = "localhost";
$database = "kidzrcle_rwda";

$con = new mysqli($host, $user, $pass, $database);

// التحقق من الاتصال
if ($con->connect_error) {
    die("فشل الاتصال بقاعدة البيانات: " . $con->connect_error);
}

// تحديد ترميز UTF-8
$con->set_charset("utf8");

// التحقق من وجود المعاملات
if (!isset($_GET['useratt']) || !isset($_GET['dateFrom']) || !isset($_GET['dateTo'])) {
    die("معاملات مفقودة");
}

$user_id = $_GET['useratt'];
$dateFrom = $_GET['dateFrom'];
$dateTo = $_GET['dateTo'];

// استعلام محسن مع معالجة الأخطاء
$sql = "SELECT 
            users_tb.user_name, 
            employ_tb.f_name, 
            employ_tb.job, 
            employ_tb.location,
            employ_tb.salary, 
            stat2.data_stat,
            stat2.stat_employee,
            stat2.leave_start_date, 
            stat2.leave_end_date
        FROM users_tb
        INNER JOIN employ_tb ON users_tb.id_user = employ_tb.userID
        INNER JOIN stat2 ON employ_tb.id_employ = stat2.id_employee
        WHERE users_tb.id_user = ? 
        AND stat2.data_stat BETWEEN ? AND ?
        ORDER BY stat2.data_stat DESC";

// استخدام prepared statements لمنع SQL injection
$stmt = $con->prepare($sql);
if (!$stmt) {
    die("خطأ في تحضير الاستعلام: " . $con->error);
}

$stmt->bind_param("iss", $user_id, $dateFrom, $dateTo);
$stmt->execute();
$result = $stmt->get_result();

if (!$result) {
    die("خطأ في تنفيذ الاستعلام: " . $con->error);
}

$fileName = "حضور_الموظفين_من_" . $dateFrom . "_الى_" . $dateTo . ".csv";

// تحديد headers للتحميل
header("Content-Type: text/csv; charset=utf-8");
header("Content-Disposition: attachment; filename=\"$fileName\"");
header("Pragma: no-cache");
header("Expires: 0");

// إنشاء output stream
$output = fopen('php://output', 'w');

// إضافة BOM للدعم UTF-8 في Excel
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// كتابة رؤوس الأعمدة
$headers = [
    'اسم الموظف', 
    'الوظيفة', 
    'الموقع', 
    'الراتب', 
    'تاريخ الحضور', 
    'اسم المستخدم', 
    'الحالة العامة',
    'حالة الحضور الأصلية',
    'بداية الإجازة', 
    'نهاية الإجازة',
    'ملاحظات'
];
fputcsv($output, $headers);

// التحقق من وجود نتائج
if ($result->num_rows == 0) {
    fputcsv($output, [
        'لا توجد بيانات للفترة والمستخدم المحددين', 
        '', '', '', '', '', '', '', '', '', ''
    ]);
} else {
    // دالة محسنة لتحديد الحالة العامة
    function getOverallStatusForExport($stat_employee, $data_stat, $leave_start_date, $leave_end_date) {
        $notes = '';
        
        // التحقق من حالة الإجازة أولاً
        $is_on_leave = false;
        if (!empty($leave_start_date) && !empty($leave_end_date) && !empty($data_stat)) {
            try {
                $leave_start = new DateTime($leave_start_date);
                $leave_end = new DateTime($leave_end_date);
                $current_date = new DateTime($data_stat);
                
                if ($current_date >= $leave_start && $current_date <= $leave_end) {
                    $is_on_leave = true;
                    $notes = 'في فترة الإجازة المحددة';
                } elseif ($current_date > $leave_end) {
                    $notes = 'بعد انتهاء الإجازة';
                } elseif ($current_date < $leave_start) {
                    $notes = 'قبل بداية الإجازة';
                }
            } catch (Exception $e) {
                $notes = 'خطأ في تحليل تاريخ الإجازة';
            }
        }
        
        // تحديد الحالة النهائية
        if ($stat_employee === 'إجازة' || $is_on_leave) {
            return ['إجازة', $notes];
        } elseif ($stat_employee === 'حاضر') {
            return ['حاضر', $notes ?: 'حضور عادي'];
        } else {
            return ['غائب', $notes ?: 'غياب'];
        }
    }
    
    // جلب البيانات وكتابتها
    while ($row = $result->fetch_assoc()) {
        // تحديد الحالة العامة والملاحظات
        list($overall_status, $notes) = getOverallStatusForExport(
            $row['stat_employee'], 
            $row['data_stat'], 
            $row['leave_start_date'], 
            $row['leave_end_date']
        );
        
        $rowData = [
            $row['f_name'] ?? 'غير محدد',
            $row['job'] ?? 'غير محدد',
            $row['location'] ?? 'غير محدد',
            $row['salary'] ?? 'غير محدد',
            $row['data_stat'] ?? 'غير محدد',
            $row['user_name'] ?? 'غير محدد',
            $overall_status,
            $row['stat_employee'] ?? 'غير محدد',
            $row['leave_start_date'] ?? 'غير محدد',
            $row['leave_end_date'] ?? 'غير محدد',
            $notes
        ];
        
        fputcsv($output, $rowData);
    }
}

fclose($output);
$stmt->close();
$con->close();
exit();
?>