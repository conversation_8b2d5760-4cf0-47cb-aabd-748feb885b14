<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:http://localhost/roda/login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:http://localhost/roda/login.php",true);
        die("");
}

include "dbcon.php";
$sm=$_GET['input'];
      if(isset($_GET['input'])){
        $search=$_GET['input'];
      
      $sql="SELECT * FROM depit_tb,users_tb WHERE depit_tb.userID=users_tb.id_user AND CONCAT(depit_note,user_name,depit_cash,depit_date) LIKE '%$search%' ";
      $result=mysqli_query($con,$sql);
      if(mysqli_num_rows($result)>0){
       while($row=mysqli_fetch_assoc($result)) {
          $id=$row['id'];
          $depit_note=$row['depit_note'];
          $depit_cash=number_format($row['depit_cash']);
          $depit_date=$row['depit_date'];
          //$depit_date2=$row['depit_date2'];
          $user_name=$row['user_name'];
           
          ?>
          <tr id="tr_<?php echo $id ?>"> 
          <td><button type="button" class="btn btn-secondary mb-1"id="edit_bnt" style="text-decoration: none;color:aliceblue;"  >  <a href=../Admin/edit_depit.php?id="<?php echo $id; ?>" style="text-decoration: none;color:aliceblue;">تعديل</a></button> 
          <button type="button" class="btn btn-secondary mb-1" onclick="deletdata(<?php echo $id ?>)" >حذف </button></td>
          <td><?php echo $user_name ?></td>
          <td><?php echo $depit_date ?></td>
          <td><?php echo $depit_cash ?></td>
          <td><?php echo $depit_note ?></td>
          
          </tr>
          
          <?php
         
         }
      }else{
        echo "<td colspan=5 style='font-size: 25px;'>لاتوجد   معلومات بهذا الوصف </td>";
    }
    }
  

    ?>
   