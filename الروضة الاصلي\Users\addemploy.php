<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أضافة موظف</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php";
      include "addon/dbcon.php";?>
   </head>
    <body>
    <div class="wrapper2" id="tost_info">
        <div id="toast2">
            <div class="container-11">
            <i id="icon" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-22">
                <p class="p1">Done !</p>
                <p class="p2">تم اضافة البينانات</p>
            </div>
        </div>
    </div>
 <div class='contin_employ_admin'>
 <form action="" method="POST">
 <div class='input-box'>
        <label for="f_name"> الاسم الثلاثي<label>
        <input type="text" name="f_name" required>
        <label for="b_date"> تاريخ التولد  <label>
        <input type="date" name="b_date" required>
        <label for="job" >العنوان الوظيفي</label>
        <input type="text" name="job" required>
        <label for="date_start" >تاريخ المباشرة</label>
        <input type="date" name="date_start" required>
        <label for="location" >السكن  </label>
        <input type="text" name="location" required>
        <label for="salary" >الراتب الشهري</label>
        <input type="number" name="salary" required>        
        </div>
        <button class=btn name="addS" id="23">حفظ </button>
        </form>
          
 </div>
  
   </body>
   <script>
       let x;
    let toast = document.getElementById("toast2");
         p1 = document.querySelector(".p1");
         p2 = document.querySelector(".p2");
    function StudToast(ts,ic,tx1,tx2,icC){
    let  icon = document.getElementById("icon");
    clearTimeout(x);
    icon.className=icC;
    toast.style.borderRight=ts;
    icon.style.color=ic;
    p1.innerText=tx1;
    p2.innerText=tx2;
    toast.style.transition='1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition='1s';
    x = setTimeout(()=>{
    toast.style.transform = "translateX(-500px)"
    }, 4200);
}
  </script>
  <?php
include "addon/dbcon.php";
if(isset($_POST['addS'])){
    $f_name=$_POST['f_name'];
    $b_date=$_POST['b_date'];
    $location=$_POST['location'];
    $date_start=$_POST['date_start'];
    $job=$_POST['job'];
    $salary=$_POST['salary'];
    $userID=$_SESSION['user']->id_user;

    if(strlen($f_name)<2){
        $msg1=" ! انتبه ";
        $msg2="يرجى التاكد من اسم الموظف ";
        $iconC="fa-solid fa-circle-info";
        echo "<script> StudToast('8px solid rgb(247, 167, 22)','#f7a716','$msg1','$msg2','$iconC')</script>";
    }else{

    $query="INSERT INTO employ_tb(userID,f_name,b_date,job,date_start,location,salary)VALUES('$userID','$f_name','$b_date','$job','$date_start','$location','$salary') ";
    $res1=mysqli_query($con,$query);
  
    if($res1){
        $msg1=" ! تمت ";
        $msg2="تم اضافة بيانات الموظف بنجاح";
        $iconC="fa fa-circle-check";
        echo "<script> StudToast(' 8px solid rgb(3, 188, 77)','rgb(3, 188, 77)','$msg1','$msg2','$iconC')</script>";
        

    }
}
}

?>
</html>