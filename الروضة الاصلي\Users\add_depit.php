<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أضافة مصروف</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php";?>
   </head>
    <body>
    <div class="wrapper2" id="tost_info">
        <div id="toast2">
            <div class="container-11">
            <i id="icon" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-22">
                <p class="p1">Done !</p>
                <p class="p2">تم اضافة البينانات</p>
            </div>
        </div>
    </div>

    <form method="POST">
<div class='contin_depit'>
<div class='input-box'>
        <label for="name">وصف المصروف<label>
        <textarea id="textarea" type="text" name="depit_note" required placeholder="يرجى كتابة وصف المصروفات بشكل صحيح"></textarea>
        <label for="age"> قيمة المصروف <label>
        <input type="number" name="depit_cash" required placeholder="0.000 IQD">
        <label for="sex" > تاريخ عملية الصرف </label>
        <input type="date" name="depit_date" required>
        <div>
        <button class=btn name="addS" id="23">حفظ </button>
        </div>
</form>
</body>
<script>

       let x;
    let toast = document.getElementById("toast2");
         p1 = document.querySelector(".p1");
         p2 = document.querySelector(".p2");
    function StudToast(ts,ic,tx1,tx2,icC){
    let  icon = document.getElementById("icon");
    clearTimeout(x);
    icon.className=icC;
    toast.style.borderRight=ts;
    icon.style.color=ic;
    p1.innerText=tx1;
    p2.innerText=tx2;
    toast.style.transition='1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition='1s';
    x = setTimeout(()=>{
    toast.style.transform = "translateX(-500px)"
    }, 4200);
}
  </script>


<?php

include "addon/dbcon.php";
if(isset($_POST['addS'])){
    $depit_note=$_POST['depit_note'];
    $depit_cash=$_POST['depit_cash'];
    $depit_date=$_POST['depit_date'];
    if(strlen($depit_note)<10){
        $msg1=" ! انتبه ";
        $msg2="يرجى كتابة وصف صحيح للمصروف ";
        $iconC="fa-solid fa-circle-info";
        echo "<script> StudToast('8px solid rgb(247, 167, 22)','#f7a716','$msg1','$msg2','$iconC')</script>";
    }else{
    $query="INSERT INTO depit_tb(userID,depit_note,depit_cash,depit_date)VALUES('{$_SESSION['user']->id_user}','$depit_note','$depit_cash','$depit_date') ";
    $res1=mysqli_query($con,$query);
    if($res1){
        $msg1=" ! تمت ";
        $msg2="تم اضافة بيانات المصروف بنجاح";
        $iconC="fa fa-circle-check";
        echo "<script> StudToast(' 8px solid rgb(3, 188, 77)','rgb(3, 188, 77)','$msg1','$msg2','$iconC')</script>";
    }
}
}
?>
</html>