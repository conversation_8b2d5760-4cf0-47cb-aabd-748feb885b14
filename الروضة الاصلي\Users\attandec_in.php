<?php
session_start();
if (isset($_SESSION['user'])) {
  if ($_SESSION['user']->role === "User") {
  } else {
    header("location:../login.php", true);
    die("");
    echo "dont work";
  }
} else {
  header("location:../login.php", true);
  die("");
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>عرض حضور الطلاب</title>
  <link rel="stylesheet" href="css/bootstrap.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/all.min.css">
  <script src="js/all.min.js"></script>
  <link rel="icon" href="css/icon.ico">
  <script src="js/jquery.min.js"></script>
  <script src="js/jquery.dataTables.min.js"></script>
  <link rel="stylesheet" href="css/jquery.dataTables.min.css">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <?php include "addon/topbar.php" ?>
  <?php include "addon/dbcon.php" ?>
</head>

<body id="td">
<div class="search">
    <button class="btn btn-secondary text-light ml-10" id="show" type="submit"> تسجيل حضور </button> 
  </div>

  <form action="" method="POST">
    <div class="search">

      <select name="users" id="selc" placeholder='اختر مستخدم'>
        <option value="0" selected disabled>اختر صنف تدريس </option>
        <option value="روضة" <?php echo (isset($_POST['users']) && $_POST['users'] == 'روضة') ? 'selected' : ''; ?>>روضة</option>
        <option value="حضانة" <?php echo (isset($_POST['users']) && $_POST['users'] == 'حضانة') ? 'selected' : ''; ?>>حضانة</option>
        <option value="تمهيدي" <?php echo (isset($_POST['users']) && $_POST['users'] == 'تمهيدي') ? 'selected' : ''; ?>>تمهيدي</option>
        <option value="تحضيري" <?php echo (isset($_POST['users']) && $_POST['users'] == 'تحضيري') ? 'selected' : ''; ?>>تحضيري</option>
      </select>

      <button name="show" type="submit" class="btn btn-secondary text-light">اظهار</button>
  

    </div>
  </form>
  <div class="wrapper2" id="tost_info">
    <div id="toast2">
      <div class="container-11">
        <i id="icon" class="fa-solid fa-circle-info"></i>
      </div>
      <div class="container-22">
        <p class="p1">Done !</p>
        <p class="p2">تم اضافة البينانات</p>
      </div>
    </div>
  </div>

  <div class="wrapper2" id="tost_info">
    <div id="toast2">
      <div class="container-11">
        <i id="icon" class="fa-solid fa-circle-info"></i>
      </div>
      <div class="container-22">
        <p class="p1">Done !</p>
        <p class="p2">تم اضافة البينانات</p>
      </div>
    </div>
  </div>

  <table class="table" id="Table">
    <thead>
      <tr>
        <th scope="col"> عمليات الحضور والغياب </th>
        <th scope="col"> حالة الاشتراك </th>
        <th scope="col"> مستخدم الحضانة </th>
        <th scope="col">صنف التسجيل</th>
        <th scope="col">التاريخ</th>
        <th scope="col">اسم الطالب </th>

      </tr>
    </thead>

    <tbody id="myTable">

<?php
$datenow = date('Y-m-d');

if (isset($_POST['users'])) {
  $cagtselc = $_POST['users'];
  
  // استعلام محدث لعرض كل الطلاب المسجلين اليوم بغض النظر عن حالة الاشتراك
  $sql = "SELECT stat.data_stat, stat.id_stud, stat.stat_stud, stud_pay.date_exp, stud_tb.name, stud_tb.catg, stud_tb.p_name, users_tb.user_name, stud_tb.id 
          FROM stat 
          LEFT JOIN stud_tb ON stat.id_stud = stud_tb.id 
          LEFT JOIN users_tb ON stud_tb.userID = users_tb.id_user 
          LEFT JOIN stud_pay ON stud_pay.id_stud = stud_tb.id  
          WHERE stud_tb.catg = '$cagtselc' 
          AND users_tb.id_user = {$_SESSION['user']->id_user} 
          AND DATE(stat.data_stat) = '$datenow'
          ORDER BY stud_tb.name";
          
  $result = mysqli_query($con, $sql);
  
  if ($result && mysqli_num_rows($result) > 0) {
    while ($row = mysqli_fetch_assoc($result)) {
      $id = $row['id'];
      $attendance_status = $row['stat_stud']; // حاضر أو غائب
      
      // التحقق من حالة الاشتراك
      $subscription_status = "غير محدد";
      $status_color = "#6c757d"; // رمادي افتراضي
      
      if ($row['date_exp']) {
        $expiry_date = new DateTime($row['date_exp']);
        $current_date = new DateTime();
        
        if ($expiry_date > $current_date) {
          $subscription_status = "نشط";
          $status_color = "#28a745"; // أخضر
        } else {
          $subscription_status = "منتهي";
          $status_color = "#dc3545"; // أحمر
        }
      }
?>

      <tr id="tr_<?php echo $id ?>" class="sm" value="<?php echo $attendance_status ?>">
        <td>
          <?php if($attendance_status == 'حاضر') { ?>
            <p style="color: green; font-weight: bold;">
              <i class="fa-solid fa-circle-check" style="margin-left: 5px;"></i>
              تم تسجيل الحضور لهذا اليوم
            </p>
          <?php } else { ?>
            <p style="color: red; font-weight: bold;">
              <i class="fa-solid fa-circle-xmark" style="margin-left: 5px;"></i>
              تم تسجيل الغياب لهذا اليوم
            </p>
          <?php } ?>
          <i id="reomve" class="fa-solid fa-trash-can" onclick="reomve(<?php echo $id?>)" style="color: #dc3545; cursor: pointer; margin-left: 10px;" title="حذف السجل"></i>
        </td>
        <td>
          <span class="badge" style="background-color: <?php echo $status_color; ?>; color: white; padding: 8px 12px; border-radius: 20px; font-size: 12px;">
            <?php echo $subscription_status; ?>
          </span>
          <?php if ($subscription_status != "غير محدد"): ?>
            <br><small style="color: #6c757d; margin-top: 5px; display: block;">
              <?php if ($subscription_status == "منتهي"): ?>
                <i class="fa-solid fa-calendar-xmark" style="margin-left: 3px;"></i>
              <?php else: ?>
                <i class="fa-solid fa-calendar-check" style="margin-left: 3px;"></i>
              <?php endif; ?>
              انتهاء: <?php echo date('Y-m-d', strtotime($row['date_exp'])); ?>
            </small>
          <?php endif; ?>
        </td>
        <td> <?php echo $row['user_name'] ?> </td>
        <td>
          <span class="badge badge-secondary" style="background-color: #6f42c1; padding: 5px 10px; border-radius: 15px;">
            <?php echo $row['catg'] ?>
          </span>
        </td>
        <td><?php echo $datenow ?></td>
        <td>
          <strong><?php echo $row['name'] ?></strong>
        </td>
      </tr>

<?php
    }
  } else {
    // لو مفيش نتائج، اعرض رسالة بنفس عدد الأعمدة
    echo "<tr>";
    echo "<td style='text-align: center; color: #6c757d; padding: 20px;' colspan='6'>";
    echo "<i class='fa-solid fa-info-circle' style='margin-left: 5px;'></i>";
    echo "لا توجد سجلات حضور أو غياب لهذا الصنف اليوم";
    echo "</td>";
    echo "</tr>";
  }
}
?></tbody>
</table>

<script>
  $("#show").click(function () { 
    location.href="attandec.php"
  });

  function reomve(id){
    if(confirm('هل أنت متأكد من حذف هذا السجل؟')) {
      $.ajax({
        method: "post",
        url: "./addon/code2.php",
        data: {
          id:id
        },
        success: function (data) {
          if(data==1){
            // إضافة toast notification للحذف الناجح
            showToast('تم الحذف بنجاح', 'success');
            jQuery("#tr_"+id).css("background","#dc354554");
            jQuery("#tr_"+id).hide(2000);
          } else {
            showToast('حدث خطأ أثناء الحذف', 'error');
          }
        }
      });
    }
  }

  // دالة لعرض الإشعارات
  function showToast(message, type) {
    let color = type === 'success' ? '#28a745' : '#dc3545';
    let icon = type === 'success' ? 'fa-circle-check' : 'fa-circle-xmark';
    
    StudToast(`8px solid ${color}`, color, type === 'success' ? 'نجح!' : 'خطأ!', message, `fa-solid ${icon}`);
  }
</script>

<script>
  $(document).ready(function() {
    // التأكد من وجود بيانات في الجدول قبل تفعيل DataTables
    if ($("#myTable tr").length > 0 && $("#myTable tr td").first().attr('colspan') != '6') {
      $("#Table").DataTable({
        "language": {
          "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
        },
        "columnDefs": [
          { "orderable": false, "targets": 0 } // منع ترتيب عمود العمليات
        ]
      });
    }
  });
</script>

<script>
  let x;
  let toast = document.getElementById("toast2");
  p1 = document.querySelector(".p1");
  p2 = document.querySelector(".p2");

  function StudToast(ts, ic, tx1, tx2, icC) {
    let icon = document.getElementById("icon");
    clearTimeout(x);
    icon.className = icC;
    toast.style.borderRight = ts;
    icon.style.color = ic;
    p1.innerText = tx1;
    p2.innerText = tx2;
    toast.style.transition = '1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition = '1s';
    x = setTimeout(() => {
      toast.style.transform = "translateX(-500px)"
    }, 4200);
  }
</script>

</body>
</html>