
-- Database Backup --
-- Ver. : 1.0.1
-- Host : 127.0.0.1
-- Generating Time : Apr 09, 2023 at 13:16:31:PM



CREATE TABLE `depit_tb` (
  `id` int(100) NOT NULL AUTO_INCREMENT,
  `userID` int(100) NOT NULL,
  `depit_note` varchar(250) NOT NULL,
  `depit_date` date NOT NULL,
  `depit_date2` date NOT NULL,
  `depit_cash` float NOT NULL,
  PRIMARY KEY (`id`),
  KEY `userID` (`userID`),
  CONSTRAINT `depit_tb_ibfk_1` FOREIGN KEY (`userID`) REFERENCES `users_tb` (`id_user`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=81 DEFAULT CHARSET=utf8mb4;


INSERT INTO depit_tb VALUES
("22","9","مسواك +لبن +ماء","2023-01-22","2023-01-22","17500"),
("30","11","ماء+مسواك","2023-02-26","0000-00-00","11000"),
("31","11","مسواك","2023-02-13","0000-00-00","5000"),
("32","11","بند ورق ","2023-02-13","0000-00-00","7000"),
("33","11","مسواك +ماء ","2023-02-13","0000-00-00","26000"),
("34","11","عودان ماسحه","2023-02-13","0000-00-00","2000"),
("35","11","اسئله  هند \r\nاسئله زينب ","2023-02-17","0000-00-00","31000"),
("36","11","طباعه شهايد","2023-02-13","0000-00-00","20000"),
("40","6","ماء وصندوق ماء","2023-02-28","0000-00-00","6500"),
("41","6","مسواك ","2023-02-28","0000-00-00","15000"),
("42","6","سنك ومنشار","2023-02-26","0000-00-00","35000"),
("43","9","خضار وفواكهه مع علبة لبن ","2023-02-26","0000-00-00","15000"),
("44","11","الماء","2023-02-26","0000-00-00","5000"),
("45","11","مسواك","2023-02-26","0000-00-00","6000"),
("47","11","قرطاسيه","2023-03-01","0000-00-00","8000"),
("48","11","الطباعه","2023-03-02","0000-00-00","2000"),
("49","11","مسواك","2023-03-02","0000-00-00","19000"),
("51","11","فواكه","2023-03-02","0000-00-00","4500"),
("52","6","معكرونة ووصل مسح وسكاكين","2023-03-05","0000-00-00","23000"),
("53","10"," واستنساخ ووخضروات وفواكهجرس وبطاريات  ","2023-02-12","0000-00-00","30000"),
("54","10","سماعة بلوتوث","2023-02-12","0000-00-00","30000"),
("55","10","قماش مسح وكلينكس و مي","2023-02-21","0000-00-00","9000"),
("56","10","اكياس نفايات ولبن خضروات وفواكه","2023-02-26","0000-00-00","22000"),
("57","10","صيانة حمامات ","2023-02-26","0000-00-00","80000"),
("58","10","معطر ارضية واكياس نفايات و اضائة و عدس و خضروات و لبن و تمر وسفرة وبلنكو واقلام سبورة وقفازات","2023-03-04","0000-00-00","39750"),
("59","10","راتب مس فرح","2023-03-04","0000-00-00","55000"),
("60","11","ماء+مسواك+قرطاسية+طبع","2023-02-26","0000-00-00","21000"),
("61","11","مسواك","2023-03-01","0000-00-00","23000"),
("62","11","صيانة حمامات ","2023-03-05","0000-00-00","85000"),
("63","11","مسواك +ماء+اقلام","2023-03-08","0000-00-00","16000"),
("64","9","لبن مع خضار وفواكهه مع ماء ","2023-03-05","0000-00-00","20000"),
("69","10","   عدس 2 ع3كلنس عددااقلام صبورة عدد 3   سناك خيار جزر تفاح  اقلام  جاف 2   علبة لبن كبيرة","0003-02-20","0000-00-00","23000"),
("70","8","مسواك اسبوعي مخضر","2023-03-17","0000-00-00","15000"),
("71","8","مسواك اسبوعي","2023-03-12","0000-00-00","15000"),
("72","7"," مسواك  اسبوعين خضروات وفواكهه","2023-03-19","0000-00-00","20000"),
("73","7","بند اوراق مع اسنساخ ارشيف خاص ب حضانه مع فايل بوكس","2023-03-20","0000-00-00","20000"),
("74","6","سحب 150 الف لابو حيدر","2023-03-27","0000-00-00","150000"),
("75","6","اعواد ماسحات","2023-03-20","0000-00-00","4000"),
("76","11","مسواك +لبن","2023-03-26","0000-00-00","13500"),
("77","10","عدس  معطر ارضية  معكرونة","2023-03-27","0000-00-00","12500"),
("78","10","فواكه  كلينكس  معطر ارضية","2023-04-02","0000-00-00","16500"),
("79","10","مي ارو","2023-04-03","0000-00-00","3000"),
("80","12","مسواك لمده اسبوعين18و18 ماء 10 منظفات 10 كلينكس 10 نقاط كهرباء 10 سيار كهربائي 10 انتر نت37 قصاصات ومعر جو5 معجون 5 ","2023-04-09","0000-00-00","128");




CREATE TABLE `employ_tb` (
  `id_employ` int(11) NOT NULL AUTO_INCREMENT,
  `f_name` varchar(250) NOT NULL,
  `b_date` date NOT NULL,
  `job` varchar(250) NOT NULL,
  `date_start` date NOT NULL,
  `location` varchar(250) NOT NULL,
  `salary` float NOT NULL,
  `userID` int(100) NOT NULL,
  PRIMARY KEY (`id_employ`),
  KEY `userID` (`userID`),
  CONSTRAINT `employ_tb_ibfk_1` FOREIGN KEY (`userID`) REFERENCES `users_tb` (`id_user`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4;


INSERT INTO employ_tb VALUES
("17","نجلاء عدنان","1978-02-01","معلمه روضه","2022-11-09","بلوك b3","300000","11"),
("23","زينب ياس خضير ","1994-02-01","مديره","2019-06-01","بلوك 6 - 602","400000","9"),
("24","هديل راشد عبد العظيم ","1991-03-28","مربيه ","2022-07-12","بسماية 208","300000","9"),
("25","لمى جاسم محمد ","1980-08-06","مربيه ","2023-02-13","بسمايه 106","300000","9"),
("26","فرح نوري عودة","1991-06-24","مساعدة","2022-09-12","A6  601   908","200000","10"),
("27","ريم احمد سلمان","2022-04-06"," موظفة خدمه","2022-03-01","A3  301   897","300000","10"),
("29","زينة غالب طالب","1982-07-07","مربية","2022-12-12","A8   811    301","300000","10"),
("30","رقية علي فاضل ","1998-03-12","معلمة","2023-01-09","A6   602   203","300000","10"),
("32","زينب عماد","7565-03-31","مس تمهيدي","0001-01-01","بسماية","300000","6"),
("33","هند جعفر خضير","1990-02-22","مديرة","2023-09-04","بسماية","400000","6"),
("34","بشرى جلال","0001-01-01","مس روضة","0001-01-01","بسماية","300000","6"),
("35","نور عادل","1994-02-23","مربية","0001-01-01","بسماية","300000","6"),
("36","زينب مشتاق","0001-01-01","موظفة خدمة","0001-01-01","بسماية","300000","6"),
("37","ريهام انور","0001-01-01","مس روضة","0001-01-01","بسماية","300000","6"),
("38","مها عبد الزهرة","0001-01-01","معاونة","0001-01-01","بسماية","300000","6"),
("39","عذراء جاسم حميد","1992-05-27","مديرة الحضانه","2019-10-11","A3","400000","8"),
("42","غاده رياض حسين","1999-01-06","مس kg2","2023-02-06","B3","300000","8"),
("44","مريم زياد طارق","1998-08-27","موضفة الخدمه","2023-02-23","A9","300000","8"),
("45","هبه ماجد عبد","1986-10-05","مس تحضيري","2023-02-27","A2","300000","8"),
("47","منار قاسم","2002-04-23","عامله","2023-03-23","A7","300000","12"),
("48","وسن حازم","1986-07-23","مس روضه","2023-03-23","A8","300000","12"),
("49","ساره سالم","1992-06-23","مس روضه","2023-03-23","A8","300000","12"),
("50","ثريا محمد","1994-06-23","مربيبه","2023-03-23","A5","300000","12"),
("51","هدى كاظم","6564-02-03","مربية","2022-02-07","A2","300000","6"),
("52","زمن علي حسين","1992-02-08","مديره","2022-09-29","B3","400000","12");




CREATE TABLE `stat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_stud` int(11) NOT NULL,
  `stat_stud` varchar(250) NOT NULL,
  `data_stat` date NOT NULL,
  PRIMARY KEY (`id`),
  KEY `id_stud` (`id_stud`)
) ENGINE=InnoDB AUTO_INCREMENT=2841 DEFAULT CHARSET=utf8mb4;


INSERT INTO stat VALUES
("337","670","حاضر","2023-02-23"),
("338","685","حاضر","2023-02-23"),
("339","681","حاضر","2023-02-23"),
("340","674","","2023-02-27"),
("341","659","","2023-02-27"),
("342","661","","2023-02-27"),
("343","660","","2023-02-27"),
("344","670","","2023-02-27"),
("345","685","","2023-02-27"),
("346","678","","2023-02-27"),
("347","676","","2023-02-27"),
("348","669","","2023-02-27"),
("349","671","","2023-02-27"),
("350","690","","2023-02-27"),
("351","679","","2023-02-27"),
("352","696","","2023-02-27"),
("353","699","","2023-02-27"),
("354","700","","2023-02-27"),
("355","701","","2023-02-27"),
("356","809","حاضر","2023-02-27"),
("357","807","حاضر","2023-02-27"),
("358","805","حاضر","2023-02-27"),
("359","802","حاضر","2023-02-27"),
("360","662","حاضر","2023-02-27"),
("361","686","حاضر","2023-02-27"),
("362","691","حاضر","2023-02-27"),
("363","698","حاضر","2023-02-27"),
("364","695","حاضر","2023-02-27"),
("365","684","حاضر","2023-02-27"),
("366","702","حاضر","2023-02-27"),
("367","706","حاضر","2023-02-27"),
("368","697","حاضر","2023-02-27"),
("369","707","حاضر","2023-02-27"),
("370","804","حاضر","2023-02-27"),
("371","803","حاضر","2023-02-27"),
("372","714","حاضر","2023-02-27"),
("373","712","حاضر","2023-02-27"),
("374","654","حاضر","2023-02-27"),
("375","655","حاضر","2023-02-27"),
("376","657","حاضر","2023-02-27"),
("377","658","حاضر","2023-02-27"),
("378","663","حاضر","2023-02-27"),
("379","668","حاضر","2023-02-27"),
("380","675","حاضر","2023-02-27"),
("381","687","حاضر","2023-02-27"),
("382","683","حاضر","2023-02-27"),
("383","694","حاضر","2023-02-27"),
("384","705","حاضر","2023-02-27"),
("385","708","حاضر","2023-02-27"),
("386","711","حاضر","2023-02-27"),
("387","713","حاضر","2023-02-27"),
("388","808","حاضر","2023-02-27"),
("389","679","","2023-02-28"),
("390","807","","2023-02-28"),
("391","660","حاضر","2023-02-28"),
("392","659","حاضر","2023-02-28"),
("393","809","","2023-02-28"),
("394","699","","2023-02-28"),
("395","802","","2023-02-28"),
("396","685","","2023-02-28"),
("397","690","","2023-02-28"),
("398","696","","2023-02-28"),
("399","700","","2023-02-28"),
("400","701","","2023-02-28"),
("401","669","حاضر","2023-02-28"),
("402","661","حاضر","2023-02-28"),
("403","670","حاضر","2023-02-28"),
("404","671","حاضر","2023-02-28"),
("405","674","حاضر","2023-02-28"),
("406","824","","2023-02-28"),
("407","710","","2023-02-28"),
("408","823","","2023-02-28"),
("409","676","حاضر","2023-02-28"),
("410","662","حاضر","2023-02-28"),
("411","686","حاضر","2023-02-28"),
("412","691","حاضر","2023-02-28"),
("413","695","حاضر","2023-02-28"),
("414","689","حاضر","2023-02-28"),
("415","697","حاضر","2023-02-28"),
("416","698","حاضر","2023-02-28"),
("417","706","حاضر","2023-02-28"),
("418","712","حاضر","2023-02-28"),
("419","707","حاضر","2023-02-28"),
("420","803","حاضر","2023-02-28"),
("421","804","حاضر","2023-02-28"),
("422","714","حاضر","2023-02-28"),
("423","687","حاضر","2023-02-28"),
("424","675","حاضر","2023-02-28"),
("425","668","حاضر","2023-02-28"),
("426","688","حاضر","2023-02-28"),
("427","694","حاضر","2023-02-28"),
("428","705","حاضر","2023-02-28"),
("429","711","حاضر","2023-02-28"),
("430","713","حاضر","2023-02-28"),
("431","808","حاضر","2023-02-28"),
("432","709","حاضر","2023-02-28"),
("433","335","حاضر","2023-02-28"),
("434","339","حاضر","2023-02-28"),
("435","340","حاضر","2023-02-28"),
("436","341","حاضر","2023-02-28");
INSERT INTO stat VALUES
("437","342","حاضر","2023-02-28"),
("438","343","حاضر","2023-02-28"),
("439","346","حاضر","2023-02-28"),
("440","347","حاضر","2023-02-28"),
("441","356","حاضر","2023-02-28"),
("442","350","حاضر","2023-02-28"),
("443","360","حاضر","2023-02-28"),
("444","338","حاضر","2023-02-28"),
("445","352","حاضر","2023-02-28"),
("446","363","حاضر","2023-02-28"),
("447","369","حاضر","2023-02-28"),
("448","334","حاضر","2023-02-28"),
("449","351","حاضر","2023-02-28"),
("450","337","حاضر","2023-02-28"),
("451","359","حاضر","2023-02-28"),
("452","357","حاضر","2023-02-28"),
("453","833","حاضر","2023-03-01"),
("454","513","حاضر","2023-03-01"),
("455","497","حاضر","2023-03-01"),
("456","831","حاضر","2023-03-01"),
("457","659","حاضر","2023-03-01"),
("458","660","حاضر","2023-03-01"),
("459","670","حاضر","2023-03-01"),
("460","661","حاضر","2023-03-01"),
("461","669","حاضر","2023-03-01"),
("462","671","حاضر","2023-03-01"),
("463","679","حاضر","2023-03-01"),
("464","674","حاضر","2023-03-01"),
("465","678","حاضر","2023-03-01"),
("466","676","حاضر","2023-03-01"),
("467","699","حاضر","2023-03-01"),
("468","685","حاضر","2023-03-01"),
("469","696","حاضر","2023-03-01"),
("470","690","حاضر","2023-03-01"),
("471","681","حاضر","2023-03-01"),
("472","700","حاضر","2023-03-01"),
("473","701","حاضر","2023-03-01"),
("474","802","حاضر","2023-03-01"),
("475","710","حاضر","2023-03-01"),
("476","703","حاضر","2023-03-01"),
("477","704","حاضر","2023-03-01"),
("478","807","حاضر","2023-03-01"),
("479","809","حاضر","2023-03-01"),
("480","824","حاضر","2023-03-01"),
("481","823","حاضر","2023-03-01"),
("482","662","حاضر","2023-03-01"),
("483","686","حاضر","2023-03-01"),
("484","689","حاضر","2023-03-01"),
("485","691","حاضر","2023-03-01"),
("486","695","حاضر","2023-03-01"),
("487","697","حاضر","2023-03-01"),
("488","698","حاضر","2023-03-01"),
("489","712","حاضر","2023-03-01"),
("490","707","حاضر","2023-03-01"),
("491","706","حاضر","2023-03-01"),
("492","702","حاضر","2023-03-01"),
("493","714","حاضر","2023-03-01"),
("494","803","حاضر","2023-03-01"),
("495","804","حاضر","2023-03-01"),
("496","826","حاضر","2023-03-01"),
("497","827","حاضر","2023-03-01"),
("498","655","حاضر","2023-03-01"),
("499","657","حاضر","2023-03-01"),
("500","654","حاضر","2023-03-01"),
("501","658","حاضر","2023-03-01"),
("502","663","حاضر","2023-03-01"),
("503","675","حاضر","2023-03-01"),
("504","668","حاضر","2023-03-01"),
("505","688","حاضر","2023-03-01"),
("506","694","حاضر","2023-03-01"),
("507","705","حاضر","2023-03-01"),
("508","711","حاضر","2023-03-01"),
("509","708","حاضر","2023-03-01"),
("510","709","حاضر","2023-03-01"),
("511","682","حاضر","2023-03-01"),
("512","808","حاضر","2023-03-01"),
("513","713","حاضر","2023-03-01"),
("514","828","حاضر","2023-03-01"),
("515","549","حاضر","2023-03-01"),
("516","612","حاضر","2023-03-01"),
("517","553","حاضر","2023-03-01"),
("518","433","حاضر","2023-03-01"),
("519","439","حاضر","2023-03-01"),
("520","342","","2023-03-06"),
("521","343","","2023-03-06"),
("522","341","","2023-03-06"),
("523","335","","2023-03-06"),
("524","339","","2023-03-06"),
("525","340","","2023-03-06"),
("526","346","","2023-03-06"),
("527","347","","2023-03-06"),
("528","350","","2023-03-06"),
("529","364","","2023-03-06"),
("530","356","","2023-03-06"),
("531","365","","2023-03-06"),
("532","367","","2023-03-06"),
("533","368","","2023-03-06"),
("534","373","","2023-03-06"),
("535","383","","2023-03-06"),
("536","387","","2023-03-06");
INSERT INTO stat VALUES
("537","389","","2023-03-06"),
("538","390","","2023-03-06"),
("539","391","","2023-03-06"),
("540","395","","2023-03-06"),
("541","396","","2023-03-06"),
("542","397","","2023-03-06"),
("543","402","","2023-03-06"),
("544","405","","2023-03-06"),
("545","403","","2023-03-06"),
("546","413","","2023-03-06"),
("547","412","","2023-03-06"),
("548","417","","2023-03-06"),
("549","418","","2023-03-06"),
("550","421","","2023-03-06"),
("551","425","","2023-03-06"),
("552","427","","2023-03-06"),
("553","431","","2023-03-06"),
("554","432","","2023-03-06"),
("555","876","","2023-03-06"),
("556","914","حاضر","2023-03-06"),
("557","877","","2023-03-06"),
("558","338","","2023-03-06"),
("559","369","","2023-03-06"),
("560","370","","2023-03-06"),
("561","363","","2023-03-06"),
("562","352","","2023-03-06"),
("563","360","","2023-03-06"),
("564","371","","2023-03-06"),
("565","376","","2023-03-06"),
("566","377","","2023-03-06"),
("567","378","","2023-03-06"),
("568","385","","2023-03-06"),
("569","394","","2023-03-06"),
("570","392","","2023-03-06"),
("571","409","","2023-03-06"),
("572","410","","2023-03-06"),
("573","414","","2023-03-06"),
("574","415","","2023-03-06"),
("575","419","","2023-03-06"),
("576","422","","2023-03-06"),
("577","424","","2023-03-06"),
("578","426","حاضر","2023-03-06"),
("579","429","حاضر","2023-03-06"),
("580","875","حاضر","2023-03-06"),
("581","917","حاضر","2023-03-06"),
("582","918","حاضر","2023-03-06"),
("583","337","","2023-03-06"),
("584","334","","2023-03-06"),
("585","351","","2023-03-06"),
("586","359","","2023-03-06"),
("587","361","","2023-03-06"),
("588","357","","2023-03-06"),
("589","362","","2023-03-06"),
("590","366","","2023-03-06"),
("591","375","","2023-03-06"),
("592","380","","2023-03-06"),
("593","381","","2023-03-06"),
("594","382","","2023-03-06"),
("595","384","","2023-03-06"),
("596","401","","2023-03-06"),
("597","393","","2023-03-06"),
("598","404","","2023-03-06"),
("599","406","","2023-03-06"),
("600","408","","2023-03-06"),
("601","411","","2023-03-06"),
("602","420","","2023-03-06"),
("603","428","حاضر","2023-03-06"),
("604","816","حاضر","2023-03-06"),
("605","817","حاضر","2023-03-06"),
("606","882","حاضر","2023-03-06"),
("607","915","حاضر","2023-03-06"),
("608","913","حاضر","2023-03-06"),
("609","916","","2023-03-06"),
("610","876","","2023-03-07"),
("611","914","حاضر","2023-03-07"),
("612","340","","2023-03-07"),
("613","343","","2023-03-07"),
("614","346","","2023-03-07"),
("615","335","","2023-03-07"),
("616","356","","2023-03-07"),
("617","347","","2023-03-07"),
("618","350","","2023-03-07"),
("619","364","","2023-03-07"),
("620","365","","2023-03-07"),
("621","368","","2023-03-07"),
("622","367","","2023-03-07"),
("623","383","","2023-03-07"),
("624","387","","2023-03-07"),
("625","389","","2023-03-07"),
("626","391","","2023-03-07"),
("627","390","","2023-03-07"),
("628","396","","2023-03-07"),
("629","339","","2023-03-07"),
("630","403","","2023-03-07"),
("631","405","","2023-03-07"),
("632","395","","2023-03-07"),
("633","402","","2023-03-07"),
("634","397","","2023-03-07"),
("635","421","","2023-03-07"),
("636","417","","2023-03-07");
INSERT INTO stat VALUES
("637","427","","2023-03-07"),
("638","431","","2023-03-07"),
("639","432","","2023-03-07"),
("640","398","","2023-03-07"),
("641","400","","2023-03-07"),
("642","964","حاضر","2023-03-07"),
("643","386","","2023-03-07"),
("644","966","حاضر","2023-03-07"),
("645","874","","2023-03-07"),
("646","412","","2023-03-07"),
("647","338","","2023-03-07"),
("648","918","حاضر","2023-03-07"),
("649","360","","2023-03-07"),
("650","363","","2023-03-07"),
("651","369","","2023-03-07"),
("652","370","","2023-03-07"),
("653","371","","2023-03-07"),
("654","377","","2023-03-07"),
("655","376","","2023-03-07"),
("656","378","","2023-03-07"),
("657","385","","2023-03-07"),
("658","394","","2023-03-07"),
("659","352","","2023-03-07"),
("660","392","","2023-03-07"),
("661","409","","2023-03-07"),
("662","410","","2023-03-07"),
("663","414","","2023-03-07"),
("664","415","","2023-03-07"),
("665","419","","2023-03-07"),
("666","422","","2023-03-07"),
("667","424","","2023-03-07"),
("668","916","","2023-03-07"),
("669","426","","2023-03-07"),
("670","429","","2023-03-07"),
("671","875","","2023-03-07"),
("672","917","","2023-03-07"),
("673","357","","2023-03-07"),
("674","337","","2023-03-07"),
("675","351","","2023-03-07"),
("676","384","","2023-03-07"),
("677","401","","2023-03-07"),
("678","393","","2023-03-07"),
("679","382","","2023-03-07"),
("680","381","","2023-03-07"),
("681","380","","2023-03-07"),
("682","375","","2023-03-07"),
("683","362","","2023-03-07"),
("684","366","","2023-03-07"),
("685","361","","2023-03-07"),
("686","406","","2023-03-07"),
("687","408","","2023-03-07"),
("688","404","","2023-03-07"),
("689","411","","2023-03-07"),
("690","413","","2023-03-07"),
("691","420","","2023-03-07"),
("692","817","حاضر","2023-03-07"),
("693","913","حاضر","2023-03-07"),
("694","882","حاضر","2023-03-07"),
("695","915","حاضر","2023-03-07"),
("696","407","","2023-03-07"),
("697","423","","2023-03-07"),
("698","428","","2023-03-07"),
("699","816","","2023-03-07"),
("700","965","حاضر","2023-03-07"),
("701","343","","2023-03-08"),
("702","339","","2023-03-08"),
("703","340","","2023-03-08"),
("704","364","","2023-03-08"),
("705","365","","2023-03-08"),
("706","367","","2023-03-08"),
("707","383","","2023-03-08"),
("708","356","","2023-03-08"),
("709","386","","2023-03-08"),
("710","387","","2023-03-08"),
("711","389","","2023-03-08"),
("712","373","","2023-03-08"),
("713","390","","2023-03-08"),
("714","395","","2023-03-08"),
("715","391","","2023-03-08"),
("716","397","","2023-03-08"),
("717","398","","2023-03-08"),
("718","400","","2023-03-08"),
("719","402","","2023-03-08"),
("720","412","","2023-03-08"),
("721","425","","2023-03-08"),
("722","874","","2023-03-08"),
("723","427","","2023-03-08"),
("724","431","","2023-03-08"),
("725","350","","2023-03-08"),
("726","334","","2023-03-08"),
("727","965","حاضر","2023-03-08"),
("728","357","","2023-03-08"),
("729","375","","2023-03-08"),
("730","382","","2023-03-08"),
("731","384","","2023-03-08"),
("732","393","","2023-03-08"),
("733","401","","2023-03-08"),
("734","404","","2023-03-08"),
("735","406","","2023-03-08"),
("736","407","","2023-03-08");
INSERT INTO stat VALUES
("737","408","","2023-03-08"),
("738","420","","2023-03-08"),
("739","423","","2023-03-08"),
("740","428","","2023-03-08"),
("741","816","","2023-03-08"),
("742","817","","2023-03-08"),
("743","873","","2023-03-08"),
("744","915","","2023-03-08"),
("745","338","","2023-03-08"),
("746","360","","2023-03-08"),
("747","363","","2023-03-08"),
("748","369","","2023-03-08"),
("749","370","","2023-03-08"),
("750","371","","2023-03-08"),
("751","376","","2023-03-08"),
("752","377","","2023-03-08"),
("753","378","","2023-03-08"),
("754","392","","2023-03-08"),
("755","409","","2023-03-08"),
("756","414","","2023-03-08"),
("757","415","","2023-03-08"),
("758","419","","2023-03-08"),
("759","424","","2023-03-08"),
("760","426","","2023-03-08"),
("761","429","","2023-03-08"),
("762","875","حاضر","2023-03-08"),
("763","917","حاضر","2023-03-08"),
("764","918","حاضر","2023-03-08"),
("765","361","","2023-03-09"),
("766","380","","2023-03-09"),
("767","381","","2023-03-09"),
("768","362","","2023-03-09"),
("769","359","","2023-03-09"),
("770","366","","2023-03-09"),
("771","384","","2023-03-09"),
("772","393","","2023-03-09"),
("773","401","","2023-03-09"),
("774","420","","2023-03-09"),
("775","428","","2023-03-09"),
("776","816","","2023-03-09"),
("777","873","","2023-03-09"),
("778","965","حاضر","2023-03-09"),
("779","339","","2023-03-09"),
("780","340","","2023-03-09"),
("781","350","","2023-03-09"),
("782","367","","2023-03-09"),
("783","387","","2023-03-09"),
("784","390","","2023-03-09"),
("785","395","","2023-03-09"),
("786","396","","2023-03-09"),
("787","397","","2023-03-09"),
("788","398","","2023-03-09"),
("789","402","","2023-03-09"),
("790","403","","2023-03-09"),
("791","412","","2023-03-09"),
("792","421","","2023-03-09"),
("793","877","حاضر","2023-03-09"),
("794","914","حاضر","2023-03-09"),
("795","966","حاضر","2023-03-09"),
("796","371","","2023-03-09"),
("797","360","","2023-03-09"),
("798","376","","2023-03-09"),
("799","370","","2023-03-09"),
("800","394","","2023-03-09"),
("801","377","","2023-03-09"),
("802","410","","2023-03-09"),
("803","409","","2023-03-09"),
("804","422","","2023-03-09"),
("805","661","حاضر","2023-03-12"),
("806","659","حاضر","2023-03-12"),
("807","669","حاضر","2023-03-12"),
("808","674","حاضر","2023-03-12"),
("809","671","حاضر","2023-03-12"),
("810","676","حاضر","2023-03-12"),
("811","681","حاضر","2023-03-12"),
("812","685","حاضر","2023-03-12"),
("813","690","حاضر","2023-03-12"),
("814","696","حاضر","2023-03-12"),
("815","699","حاضر","2023-03-12"),
("816","700","حاضر","2023-03-12"),
("817","701","حاضر","2023-03-12"),
("818","703","حاضر","2023-03-12"),
("819","805","حاضر","2023-03-12"),
("820","710","حاضر","2023-03-12"),
("821","807","حاضر","2023-03-12"),
("822","809","حاضر","2023-03-12"),
("823","823","حاضر","2023-03-12"),
("824","824","حاضر","2023-03-12"),
("825","933","حاضر","2023-03-12"),
("826","935","حاضر","2023-03-12"),
("827","654","حاضر","2023-03-12"),
("828","657","حاضر","2023-03-12"),
("829","658","حاضر","2023-03-12"),
("830","663","حاضر","2023-03-12"),
("831","675","حاضر","2023-03-12"),
("832","682","حاضر","2023-03-12"),
("833","668","حاضر","2023-03-12"),
("834","683","حاضر","2023-03-12"),
("835","664","حاضر","2023-03-12"),
("836","688","حاضر","2023-03-12");
INSERT INTO stat VALUES
("837","694","حاضر","2023-03-12"),
("838","705","حاضر","2023-03-12"),
("839","708","حاضر","2023-03-12"),
("840","828","حاضر","2023-03-12"),
("841","808","حاضر","2023-03-12"),
("842","709","حاضر","2023-03-12"),
("843","940","حاضر","2023-03-12"),
("844","806","حاضر","2023-03-12"),
("845","941","حاضر","2023-03-12"),
("846","942","حاضر","2023-03-12"),
("847","686","حاضر","2023-03-12"),
("848","667","حاضر","2023-03-12"),
("849","684","حاضر","2023-03-12"),
("850","691","حاضر","2023-03-12"),
("851","698","حاضر","2023-03-12"),
("852","827","حاضر","2023-03-12"),
("853","335","","2023-03-12"),
("854","339","","2023-03-12"),
("855","341","","2023-03-12"),
("856","343","","2023-03-12"),
("857","350","","2023-03-12"),
("858","356","","2023-03-12"),
("859","364","","2023-03-12"),
("860","365","","2023-03-12"),
("861","367","","2023-03-12"),
("862","368","","2023-03-12"),
("863","386","","2023-03-12"),
("864","388","","2023-03-12"),
("865","389","","2023-03-12"),
("866","390","","2023-03-12"),
("867","391","","2023-03-12"),
("868","395","","2023-03-12"),
("869","398","","2023-03-12"),
("870","397","","2023-03-12"),
("871","396","","2023-03-12"),
("872","403","","2023-03-12"),
("873","400","","2023-03-12"),
("874","405","","2023-03-12"),
("875","427","حاضر","2023-03-12"),
("876","874","حاضر","2023-03-12"),
("877","412","","2023-03-12"),
("878","425","حاضر","2023-03-12"),
("879","876","حاضر","2023-03-12"),
("880","421","حاضر","2023-03-12"),
("881","877","حاضر","2023-03-12"),
("882","914","حاضر","2023-03-12"),
("883","964","حاضر","2023-03-12"),
("884","352","","2023-03-12"),
("885","363","","2023-03-12"),
("886","376","","2023-03-12"),
("887","371","","2023-03-12"),
("888","338","","2023-03-12"),
("889","370","","2023-03-12"),
("890","378","","2023-03-12"),
("891","414","","2023-03-12"),
("892","419","","2023-03-12"),
("893","422","","2023-03-12"),
("894","429","","2023-03-12"),
("895","875","حاضر","2023-03-12"),
("896","918","حاضر","2023-03-12"),
("897","375","","2023-03-12"),
("898","334","","2023-03-12"),
("899","366","","2023-03-12"),
("900","357","","2023-03-12"),
("901","337","","2023-03-12"),
("902","351","","2023-03-12"),
("903","380","","2023-03-12"),
("904","382","","2023-03-12"),
("905","381","","2023-03-12"),
("906","384","","2023-03-12"),
("907","404","","2023-03-12"),
("908","428","","2023-03-12"),
("909","817","","2023-03-12"),
("910","816","","2023-03-12"),
("911","633","حاضر","2023-03-13"),
("912","620","حاضر","2023-03-13"),
("913","621","حاضر","2023-03-13"),
("914","646","حاضر","2023-03-13"),
("915","618","حاضر","2023-03-13"),
("916","619","حاضر","2023-03-13"),
("917","852","حاضر","2023-03-13"),
("918","973","حاضر","2023-03-13"),
("919","975","حاضر","2023-03-13"),
("920","988","حاضر","2023-03-13"),
("921","974","حاضر","2023-03-13"),
("922","1009","حاضر","2023-03-13"),
("923","980","حاضر","2023-03-13"),
("924","434","حاضر","2023-03-13"),
("925","443","حاضر","2023-03-13"),
("926","472","حاضر","2023-03-13"),
("927","469","حاضر","2023-03-13"),
("928","475","حاضر","2023-03-13"),
("929","478","حاضر","2023-03-13"),
("930","480","حاضر","2023-03-13"),
("931","659","حاضر","2023-03-13"),
("932","660","حاضر","2023-03-13"),
("933","661","حاضر","2023-03-13"),
("934","669","حاضر","2023-03-13"),
("935","670","حاضر","2023-03-13"),
("936","671","حاضر","2023-03-13");
INSERT INTO stat VALUES
("937","674","حاضر","2023-03-13"),
("938","685","حاضر","2023-03-13"),
("939","704","حاضر","2023-03-13"),
("940","710","حاضر","2023-03-13"),
("941","703","حاضر","2023-03-13"),
("942","809","حاضر","2023-03-13"),
("943","933","حاضر","2023-03-13"),
("944","823","حاضر","2023-03-13"),
("945","654","حاضر","2023-03-13"),
("946","657","حاضر","2023-03-13"),
("947","658","حاضر","2023-03-13"),
("948","663","حاضر","2023-03-13"),
("949","664","حاضر","2023-03-13"),
("950","668","حاضر","2023-03-13"),
("951","675","حاضر","2023-03-13"),
("952","677","حاضر","2023-03-13"),
("953","682","حاضر","2023-03-13"),
("954","683","حاضر","2023-03-13"),
("955","688","حاضر","2023-03-13"),
("956","708","حاضر","2023-03-13"),
("957","713","حاضر","2023-03-13"),
("958","667","حاضر","2023-03-13"),
("959","684","حاضر","2023-03-13"),
("960","686","حاضر","2023-03-13"),
("961","662","حاضر","2023-03-13"),
("962","691","حاضر","2023-03-13"),
("963","697","حاضر","2023-03-13"),
("964","689","حاضر","2023-03-13"),
("965","706","حاضر","2023-03-13"),
("966","702","حاضر","2023-03-13"),
("967","938","حاضر","2023-03-13"),
("968","826","حاضر","2023-03-13"),
("969","681","حاضر","2023-03-13"),
("970","678","حاضر","2023-03-13"),
("971","679","حاضر","2023-03-13"),
("972","690","حاضر","2023-03-13"),
("973","676","حاضر","2023-03-13"),
("974","659","حاضر","2023-03-15"),
("975","660","حاضر","2023-03-15"),
("976","661","حاضر","2023-03-15"),
("977","669","حاضر","2023-03-15"),
("978","670","حاضر","2023-03-15"),
("979","674","حاضر","2023-03-15"),
("980","671","حاضر","2023-03-15"),
("981","676","حاضر","2023-03-15"),
("982","679","حاضر","2023-03-15"),
("983","678","حاضر","2023-03-15"),
("984","681","حاضر","2023-03-15"),
("985","685","حاضر","2023-03-15"),
("986","703","حاضر","2023-03-15"),
("987","696","حاضر","2023-03-15"),
("988","699","حاضر","2023-03-15"),
("989","704","حاضر","2023-03-15"),
("990","690","حاضر","2023-03-15"),
("991","710","حاضر","2023-03-15"),
("992","802","حاضر","2023-03-15"),
("993","809","حاضر","2023-03-15"),
("994","934","حاضر","2023-03-15"),
("995","662","حاضر","2023-03-15"),
("996","684","حاضر","2023-03-15"),
("997","689","حاضر","2023-03-15"),
("998","686","حاضر","2023-03-15"),
("999","691","حاضر","2023-03-15"),
("1000","697","حاضر","2023-03-15"),
("1001","714","حاضر","2023-03-15"),
("1002","803","حاضر","2023-03-15"),
("1003","804","حاضر","2023-03-15"),
("1004","695","حاضر","2023-03-15"),
("1005","698","حاضر","2023-03-15"),
("1006","700","حاضر","2023-03-15"),
("1007","701","حاضر","2023-03-15"),
("1008","827","حاضر","2023-03-15"),
("1009","654","حاضر","2023-03-15"),
("1010","657","حاضر","2023-03-15"),
("1011","658","حاضر","2023-03-15"),
("1012","663","حاضر","2023-03-15"),
("1013","668","حاضر","2023-03-15"),
("1014","675","حاضر","2023-03-15"),
("1015","683","حاضر","2023-03-15"),
("1016","688","حاضر","2023-03-15"),
("1017","694","حاضر","2023-03-15"),
("1018","705","حاضر","2023-03-15"),
("1019","708","حاضر","2023-03-15"),
("1020","709","حاضر","2023-03-15"),
("1021","713","حاضر","2023-03-15"),
("1022","808","حاضر","2023-03-15"),
("1023","828","حاضر","2023-03-15"),
("1024","942","حاضر","2023-03-15"),
("1025","941","حاضر","2023-03-15"),
("1026","940","حاضر","2023-03-15"),
("1027","335","حاضر","2023-03-15"),
("1028","339","حاضر","2023-03-15"),
("1029","340","حاضر","2023-03-15"),
("1030","341","حاضر","2023-03-15"),
("1031","343","حاضر","2023-03-15"),
("1032","346","حاضر","2023-03-15"),
("1033","347","حاضر","2023-03-15"),
("1034","342","حاضر","2023-03-15"),
("1035","334","حاضر","2023-03-15"),
("1036","659","حاضر","2023-03-16");
INSERT INTO stat VALUES
("1037","661","حاضر","2023-03-16"),
("1038","669","حاضر","2023-03-16"),
("1039","670","حاضر","2023-03-16"),
("1040","671","حاضر","2023-03-16"),
("1041","676","حاضر","2023-03-16"),
("1042","674","حاضر","2023-03-16"),
("1043","681","حاضر","2023-03-16"),
("1044","685","حاضر","2023-03-16"),
("1045","690","حاضر","2023-03-16"),
("1046","696","حاضر","2023-03-16"),
("1047","699","حاضر","2023-03-16"),
("1048","703","حاضر","2023-03-16"),
("1049","704","حاضر","2023-03-16"),
("1050","710","حاضر","2023-03-16"),
("1051","802","حاضر","2023-03-16"),
("1052","805","حاضر","2023-03-16"),
("1053","807","حاضر","2023-03-16"),
("1054","934","حاضر","2023-03-16"),
("1055","824","حاضر","2023-03-16"),
("1056","823","حاضر","2023-03-16"),
("1057","809","حاضر","2023-03-16"),
("1058","654","حاضر","2023-03-16"),
("1059","657","حاضر","2023-03-16"),
("1060","658","حاضر","2023-03-16"),
("1061","663","حاضر","2023-03-16"),
("1062","664","حاضر","2023-03-16"),
("1063","668","حاضر","2023-03-16"),
("1064","675","حاضر","2023-03-16"),
("1065","683","حاضر","2023-03-16"),
("1066","688","حاضر","2023-03-16"),
("1067","694","حاضر","2023-03-16"),
("1068","711","حاضر","2023-03-16"),
("1069","709","حاضر","2023-03-16"),
("1070","708","حاضر","2023-03-16"),
("1071","713","حاضر","2023-03-16"),
("1072","806","حاضر","2023-03-16"),
("1073","808","حاضر","2023-03-16"),
("1074","828","حاضر","2023-03-16"),
("1075","941","حاضر","2023-03-16"),
("1076","940","حاضر","2023-03-16"),
("1077","695","حاضر","2023-03-16"),
("1078","698","حاضر","2023-03-16"),
("1079","700","حاضر","2023-03-16"),
("1080","701","حاضر","2023-03-16"),
("1081","827","حاضر","2023-03-16"),
("1082","826","حاضر","2023-03-16"),
("1083","1044","حاضر","2023-03-16"),
("1084","662","حاضر","2023-03-16"),
("1085","684","حاضر","2023-03-16"),
("1086","686","حاضر","2023-03-16"),
("1087","689","حاضر","2023-03-16"),
("1088","691","حاضر","2023-03-16"),
("1089","697","حاضر","2023-03-16"),
("1090","803","حاضر","2023-03-16"),
("1091","714","حاضر","2023-03-16"),
("1092","667","حاضر","2023-03-16"),
("1093","702","حاضر","2023-03-16"),
("1094","804","حاضر","2023-03-16"),
("1095","503","حاضر","2023-03-16"),
("1096","512","حاضر","2023-03-16"),
("1097","523","حاضر","2023-03-16"),
("1098","525","حاضر","2023-03-16"),
("1099","829","حاضر","2023-03-16"),
("1100","501","حاضر","2023-03-16"),
("1101","534","حاضر","2023-03-16"),
("1102","528","حاضر","2023-03-16"),
("1103","835","حاضر","2023-03-16"),
("1104","889","حاضر","2023-03-16"),
("1105","899","حاضر","2023-03-16"),
("1106","895","حاضر","2023-03-16"),
("1107","888","حاضر","2023-03-16"),
("1108","993","حاضر","2023-03-16"),
("1109","885","حاضر","2023-03-16"),
("1110","898","حاضر","2023-03-16"),
("1111","990","حاضر","2023-03-16"),
("1112","513","حاضر","2023-03-16"),
("1113","515","حاضر","2023-03-16"),
("1114","522","حاضر","2023-03-16"),
("1115","834","حاضر","2023-03-16"),
("1116","836","حاضر","2023-03-16"),
("1117","886","حاضر","2023-03-16"),
("1118","887","حاضر","2023-03-16"),
("1119","900","حاضر","2023-03-16"),
("1120","991","حاضر","2023-03-16"),
("1121","995","حاضر","2023-03-16"),
("1122","335","حاضر","2023-03-19"),
("1123","340","حاضر","2023-03-19"),
("1124","341","حاضر","2023-03-19"),
("1125","342","حاضر","2023-03-19"),
("1126","347","حاضر","2023-03-19"),
("1127","343","حاضر","2023-03-19"),
("1128","346","حاضر","2023-03-19"),
("1129","356","حاضر","2023-03-19"),
("1130","364","حاضر","2023-03-19"),
("1131","365","حاضر","2023-03-19"),
("1132","373","حاضر","2023-03-19"),
("1133","383","حاضر","2023-03-19"),
("1134","387","حاضر","2023-03-19"),
("1135","391","حاضر","2023-03-19"),
("1136","396","حاضر","2023-03-19");
INSERT INTO stat VALUES
("1137","397","حاضر","2023-03-19"),
("1138","400","حاضر","2023-03-19"),
("1139","403","حاضر","2023-03-19"),
("1140","405","حاضر","2023-03-19"),
("1141","412","حاضر","2023-03-19"),
("1142","425","حاضر","2023-03-19"),
("1143","427","حاضر","2023-03-19"),
("1144","874","حاضر","2023-03-19"),
("1145","432","حاضر","2023-03-19"),
("1146","876","حاضر","2023-03-19"),
("1147","914","حاضر","2023-03-19"),
("1148","1052","حاضر","2023-03-19"),
("1149","1053","حاضر","2023-03-19"),
("1150","1054","حاضر","2023-03-19"),
("1151","1055","حاضر","2023-03-19"),
("1152","339","حاضر","2023-03-19"),
("1153","352","حاضر","2023-03-19"),
("1154","370","حاضر","2023-03-19"),
("1155","371","حاضر","2023-03-19"),
("1156","376","حاضر","2023-03-19"),
("1157","377","حاضر","2023-03-19"),
("1158","378","حاضر","2023-03-19"),
("1159","392","حاضر","2023-03-19"),
("1160","414","حاضر","2023-03-19"),
("1161","415","حاضر","2023-03-19"),
("1162","419","حاضر","2023-03-19"),
("1163","875","حاضر","2023-03-19"),
("1164","917","حاضر","2023-03-19"),
("1165","918","حاضر","2023-03-19"),
("1166","1051","حاضر","2023-03-19"),
("1167","357","حاضر","2023-03-19"),
("1168","361","حاضر","2023-03-19"),
("1169","362","حاضر","2023-03-19"),
("1170","366","حاضر","2023-03-19"),
("1171","375","حاضر","2023-03-19"),
("1172","380","حاضر","2023-03-19"),
("1173","381","حاضر","2023-03-19"),
("1174","382","حاضر","2023-03-19"),
("1175","384","حاضر","2023-03-19"),
("1176","393","حاضر","2023-03-19"),
("1177","401","حاضر","2023-03-19"),
("1178","404","حاضر","2023-03-19"),
("1179","406","حاضر","2023-03-19"),
("1180","407","حاضر","2023-03-19"),
("1181","411","حاضر","2023-03-19"),
("1182","413","حاضر","2023-03-19"),
("1183","420","حاضر","2023-03-19"),
("1184","428","حاضر","2023-03-19"),
("1185","816","حاضر","2023-03-19"),
("1186","817","حاضر","2023-03-19"),
("1187","873","حاضر","2023-03-19"),
("1188","882","حاضر","2023-03-19"),
("1189","913","حاضر","2023-03-19"),
("1190","915","حاضر","2023-03-19"),
("1191","1046","حاضر","2023-03-19"),
("1192","1049","حاضر","2023-03-19"),
("1193","1074","حاضر","2023-03-19"),
("1194","1075","حاضر","2023-03-19"),
("1195","660","حاضر","2023-03-19"),
("1196","661","حاضر","2023-03-19"),
("1197","669","حاضر","2023-03-19"),
("1198","671","حاضر","2023-03-19"),
("1199","670","حاضر","2023-03-19"),
("1200","674","حاضر","2023-03-19"),
("1201","676","حاضر","2023-03-19"),
("1202","679","حاضر","2023-03-19"),
("1203","681","حاضر","2023-03-19"),
("1204","807","حاضر","2023-03-19"),
("1205","805","حاضر","2023-03-19"),
("1206","802","حاضر","2023-03-19"),
("1207","710","حاضر","2023-03-19"),
("1208","704","حاضر","2023-03-19"),
("1209","699","حاضر","2023-03-19"),
("1210","685","حاضر","2023-03-19"),
("1211","690","حاضر","2023-03-19"),
("1212","703","حاضر","2023-03-19"),
("1213","696","حاضر","2023-03-19"),
("1214","809","حاضر","2023-03-19"),
("1215","824","حاضر","2023-03-19"),
("1216","934","حاضر","2023-03-19"),
("1217","662","حاضر","2023-03-19"),
("1218","667","حاضر","2023-03-19"),
("1219","684","حاضر","2023-03-19"),
("1220","686","حاضر","2023-03-19"),
("1221","689","حاضر","2023-03-19"),
("1222","697","حاضر","2023-03-19"),
("1223","702","حاضر","2023-03-19"),
("1224","803","حاضر","2023-03-19"),
("1225","804","حاضر","2023-03-19"),
("1226","1045","حاضر","2023-03-19"),
("1227","695","حاضر","2023-03-19"),
("1228","698","حاضر","2023-03-19"),
("1229","700","حاضر","2023-03-19"),
("1230","701","حاضر","2023-03-19"),
("1231","706","حاضر","2023-03-19"),
("1232","712","حاضر","2023-03-19"),
("1233","827","حاضر","2023-03-19"),
("1234","935","حاضر","2023-03-19"),
("1235","938","حاضر","2023-03-19"),
("1236","1044","حاضر","2023-03-19");
INSERT INTO stat VALUES
("1237","654","حاضر","2023-03-19"),
("1238","657","حاضر","2023-03-19"),
("1239","658","حاضر","2023-03-19"),
("1240","663","حاضر","2023-03-19"),
("1241","664","حاضر","2023-03-19"),
("1242","675","حاضر","2023-03-19"),
("1243","668","حاضر","2023-03-19"),
("1244","683","حاضر","2023-03-19"),
("1246","694","حاضر","2023-03-19"),
("1247","705","حاضر","2023-03-19"),
("1248","713","حاضر","2023-03-19"),
("1249","711","حاضر","2023-03-19"),
("1250","709","حاضر","2023-03-19"),
("1251","708","حاضر","2023-03-19"),
("1252","808","حاضر","2023-03-19"),
("1253","806","حاضر","2023-03-19"),
("1254","940","حاضر","2023-03-19"),
("1255","942","حاضر","2023-03-19"),
("1256","941","حاضر","2023-03-19"),
("1257","725","حاضر","2023-03-19"),
("1258","728","حاضر","2023-03-19"),
("1259","729","حاضر","2023-03-19"),
("1260","732","حاضر","2023-03-19"),
("1261","739","حاضر","2023-03-19"),
("1262","551","حاضر","2023-03-19"),
("1263","560","حاضر","2023-03-19"),
("1264","561","حاضر","2023-03-19"),
("1265","536","حاضر","2023-03-19"),
("1266","538","حاضر","2023-03-19"),
("1267","562","حاضر","2023-03-19"),
("1268","568","حاضر","2023-03-19"),
("1269","569","حاضر","2023-03-19"),
("1270","571","حاضر","2023-03-19"),
("1271","572","حاضر","2023-03-19"),
("1272","575","حاضر","2023-03-19"),
("1273","582","حاضر","2023-03-19"),
("1274","586","حاضر","2023-03-19"),
("1275","600","حاضر","2023-03-19"),
("1276","576","حاضر","2023-03-19"),
("1277","594","حاضر","2023-03-19"),
("1278","599","حاضر","2023-03-19"),
("1279","610","حاضر","2023-03-19"),
("1280","608","حاضر","2023-03-19"),
("1281","609","حاضر","2023-03-19"),
("1282","611","حاضر","2023-03-19"),
("1283","810","حاضر","2023-03-19"),
("1284","614","حاضر","2023-03-19"),
("1285","969","حاضر","2023-03-19"),
("1286","1019","حاضر","2023-03-19"),
("1287","968","حاضر","2023-03-19"),
("1288","825","حاضر","2023-03-19"),
("1289","535","حاضر","2023-03-19"),
("1290","540","حاضر","2023-03-19"),
("1291","578","حاضر","2023-03-19"),
("1292","543","حاضر","2023-03-19"),
("1293","601","حاضر","2023-03-19"),
("1294","603","حاضر","2023-03-19"),
("1295","605","حاضر","2023-03-19"),
("1296","583","حاضر","2023-03-19"),
("1297","820","حاضر","2023-03-19"),
("1298","822","حاضر","2023-03-19"),
("1299","818","حاضر","2023-03-19"),
("1300","910","حاضر","2023-03-19"),
("1301","539","حاضر","2023-03-19"),
("1302","544","حاضر","2023-03-19"),
("1303","552","حاضر","2023-03-19"),
("1304","548","حاضر","2023-03-19"),
("1305","555","حاضر","2023-03-19"),
("1306","553","حاضر","2023-03-19"),
("1307","564","حاضر","2023-03-19"),
("1308","566","حاضر","2023-03-19"),
("1309","577","حاضر","2023-03-19"),
("1310","581","حاضر","2023-03-19"),
("1311","588","حاضر","2023-03-19"),
("1312","596","حاضر","2023-03-19"),
("1313","595","حاضر","2023-03-19"),
("1314","815","حاضر","2023-03-19"),
("1315","906","حاضر","2023-03-19"),
("1316","813","حاضر","2023-03-19"),
("1317","607","حاضر","2023-03-19"),
("1318","1020","حاضر","2023-03-19"),
("1319","1021","حاضر","2023-03-19"),
("1320","1073","حاضر","2023-03-19"),
("1321","537","حاضر","2023-03-19"),
("1322","545","حاضر","2023-03-19"),
("1323","546","حاضر","2023-03-19"),
("1324","554","حاضر","2023-03-19"),
("1325","573","حاضر","2023-03-19"),
("1326","587","حاضر","2023-03-19"),
("1327","567","حاضر","2023-03-19"),
("1328","570","حاضر","2023-03-19"),
("1329","589","حاضر","2023-03-19"),
("1330","592","حاضر","2023-03-19"),
("1331","593","حاضر","2023-03-19"),
("1332","598","حاضر","2023-03-19"),
("1333","602","حاضر","2023-03-19"),
("1334","606","حاضر","2023-03-19"),
("1335","907","حاضر","2023-03-19"),
("1336","821","حاضر","2023-03-19"),
("1337","814","حاضر","2023-03-19");
INSERT INTO stat VALUES
("1338","612","حاضر","2023-03-19"),
("1339","617","حاضر","2023-03-19"),
("1340","659","حاضر","2023-03-20"),
("1341","661","حاضر","2023-03-20"),
("1342","669","حاضر","2023-03-20"),
("1343","671","حاضر","2023-03-20"),
("1344","674","حاضر","2023-03-20"),
("1345","676","حاضر","2023-03-20"),
("1346","679","حاضر","2023-03-20"),
("1347","681","حاضر","2023-03-20"),
("1348","685","حاضر","2023-03-20"),
("1349","690","حاضر","2023-03-20"),
("1350","696","حاضر","2023-03-20"),
("1351","699","حاضر","2023-03-20"),
("1352","703","حاضر","2023-03-20"),
("1353","704","حاضر","2023-03-20"),
("1355","802","حاضر","2023-03-20"),
("1356","710","حاضر","2023-03-20"),
("1357","1080","حاضر","2023-03-20"),
("1358","934","حاضر","2023-03-20"),
("1359","823","حاضر","2023-03-20"),
("1360","824","حاضر","2023-03-20"),
("1361","809","حاضر","2023-03-20"),
("1362","506","حاضر","2023-03-20"),
("1363","509","حاضر","2023-03-20"),
("1364","518","حاضر","2023-03-20"),
("1365","519","حاضر","2023-03-20"),
("1366","520","حاضر","2023-03-20"),
("1367","526","حاضر","2023-03-20"),
("1368","528","حاضر","2023-03-20"),
("1369","884","حاضر","2023-03-20"),
("1370","835","حاضر","2023-03-20"),
("1371","534","حاضر","2023-03-20"),
("1372","335","حاضر","2023-03-22"),
("1373","340","حاضر","2023-03-22"),
("1374","350","حاضر","2023-03-22"),
("1375","356","حاضر","2023-03-22"),
("1376","364","حاضر","2023-03-22"),
("1377","365","حاضر","2023-03-22"),
("1378","373","حاضر","2023-03-22"),
("1379","386","حاضر","2023-03-22"),
("1380","389","حاضر","2023-03-22"),
("1381","390","حاضر","2023-03-22"),
("1382","391","حاضر","2023-03-22"),
("1383","396","حاضر","2023-03-22"),
("1384","397","حاضر","2023-03-22"),
("1385","398","حاضر","2023-03-22"),
("1386","400","حاضر","2023-03-22"),
("1387","403","حاضر","2023-03-22"),
("1388","412","حاضر","2023-03-22"),
("1389","417","حاضر","2023-03-22"),
("1390","418","حاضر","2023-03-22"),
("1391","425","حاضر","2023-03-22"),
("1392","432","حاضر","2023-03-22"),
("1393","877","حاضر","2023-03-22"),
("1394","1047","حاضر","2023-03-22"),
("1395","1052","حاضر","2023-03-22"),
("1396","1084","حاضر","2023-03-22"),
("1397","1085","حاضر","2023-03-22"),
("1398","337","حاضر","2023-03-22"),
("1399","357","حاضر","2023-03-22"),
("1400","359","حاضر","2023-03-22"),
("1401","361","حاضر","2023-03-22"),
("1402","362","حاضر","2023-03-22"),
("1403","384","حاضر","2023-03-22"),
("1404","393","حاضر","2023-03-22"),
("1405","404","حاضر","2023-03-22"),
("1406","411","حاضر","2023-03-22"),
("1407","817","حاضر","2023-03-22"),
("1408","915","حاضر","2023-03-22"),
("1409","913","حاضر","2023-03-22"),
("1410","1086","حاضر","2023-03-22"),
("1411","338","حاضر","2023-03-22"),
("1412","339","حاضر","2023-03-22"),
("1413","360","حاضر","2023-03-22"),
("1414","370","حاضر","2023-03-22"),
("1415","371","حاضر","2023-03-22"),
("1416","376","حاضر","2023-03-22"),
("1417","377","حاضر","2023-03-22"),
("1418","378","حاضر","2023-03-22"),
("1419","392","حاضر","2023-03-22"),
("1420","917","حاضر","2023-03-22"),
("1421","918","حاضر","2023-03-22"),
("1422","1048","حاضر","2023-03-22"),
("1423","1050","حاضر","2023-03-22"),
("1424","539","حاضر","2023-03-22"),
("1425","544","حاضر","2023-03-22"),
("1426","552","حاضر","2023-03-22"),
("1427","559","حاضر","2023-03-22"),
("1428","564","حاضر","2023-03-22"),
("1429","577","حاضر","2023-03-22"),
("1430","659","حاضر","2023-03-22"),
("1431","661","حاضر","2023-03-22"),
("1432","669","حاضر","2023-03-22"),
("1433","679","حاضر","2023-03-22"),
("1434","681","حاضر","2023-03-22"),
("1435","685","حاضر","2023-03-22"),
("1436","690","حاضر","2023-03-22"),
("1437","699","حاضر","2023-03-22"),
("1438","703","حاضر","2023-03-22");
INSERT INTO stat VALUES
("1439","704","حاضر","2023-03-22"),
("1440","802","حاضر","2023-03-22"),
("1441","710","حاضر","2023-03-22"),
("1442","696","حاضر","2023-03-22"),
("1443","824","حاضر","2023-03-22"),
("1444","823","حاضر","2023-03-22"),
("1445","809","حاضر","2023-03-22"),
("1446","934","حاضر","2023-03-22"),
("1447","939","حاضر","2023-03-22"),
("1448","1080","حاضر","2023-03-22"),
("1449","667","حاضر","2023-03-22"),
("1450","689","حاضر","2023-03-22"),
("1451","686","حاضر","2023-03-22"),
("1452","691","حاضر","2023-03-22"),
("1453","714","حاضر","2023-03-22"),
("1454","803","حاضر","2023-03-22"),
("1455","804","حاضر","2023-03-22"),
("1456","1045","حاضر","2023-03-22"),
("1457","654","حاضر","2023-03-22"),
("1458","664","حاضر","2023-03-22"),
("1459","668","حاضر","2023-03-22"),
("1460","675","حاضر","2023-03-22"),
("1461","682","حاضر","2023-03-22"),
("1462","688","حاضر","2023-03-22"),
("1463","705","حاضر","2023-03-22"),
("1464","694","حاضر","2023-03-22"),
("1465","711","حاضر","2023-03-22"),
("1466","709","حاضر","2023-03-22"),
("1467","713","حاضر","2023-03-22"),
("1468","808","حاضر","2023-03-22"),
("1469","828","حاضر","2023-03-22"),
("1470","942","حاضر","2023-03-22"),
("1471","433","حاضر","2023-03-22"),
("1472","436","حاضر","2023-03-22"),
("1473","442","حاضر","2023-03-22"),
("1474","459","حاضر","2023-03-22"),
("1475","454","حاضر","2023-03-22"),
("1476","451","حاضر","2023-03-22"),
("1477","447","حاضر","2023-03-22"),
("1478","444","حاضر","2023-03-22"),
("1479","439","حاضر","2023-03-22"),
("1480","464","حاضر","2023-03-22"),
("1481","465","حاضر","2023-03-22"),
("1482","466","حاضر","2023-03-22"),
("1483","470","حاضر","2023-03-22"),
("1484","474","حاضر","2023-03-22"),
("1485","467","حاضر","2023-03-22"),
("1486","471","حاضر","2023-03-22"),
("1487","483","حاضر","2023-03-22"),
("1488","482","حاضر","2023-03-22"),
("1489","479","حاضر","2023-03-22"),
("1490","1057","حاضر","2023-03-22"),
("1491","1042","حاضر","2023-03-22"),
("1492","1037","حاضر","2023-03-22"),
("1493","489","حاضر","2023-03-22"),
("1494","495","حاضر","2023-03-22"),
("1495","496","حاضر","2023-03-22"),
("1496","1029","حاضر","2023-03-22"),
("1497","1031","حاضر","2023-03-22"),
("1498","1059","حاضر","2023-03-22"),
("1499","1072","حاضر","2023-03-22"),
("1500","1058","حاضر","2023-03-22"),
("1501","435","حاضر","2023-03-22"),
("1502","441","حاضر","2023-03-22"),
("1503","453","حاضر","2023-03-22"),
("1504","446","حاضر","2023-03-22"),
("1505","468","حاضر","2023-03-22"),
("1506","463","حاضر","2023-03-22"),
("1507","458","حاضر","2023-03-22"),
("1508","460","حاضر","2023-03-22"),
("1509","455","حاضر","2023-03-22"),
("1510","461","حاضر","2023-03-22"),
("1511","1063","حاضر","2023-03-22"),
("1512","1003","حاضر","2023-03-22"),
("1513","920","حاضر","2023-03-22"),
("1514","493","حاضر","2023-03-22"),
("1515","486","حاضر","2023-03-22"),
("1516","487","حاضر","2023-03-22"),
("1517","1002","حاضر","2023-03-22"),
("1518","919","حاضر","2023-03-22"),
("1519","472","حاضر","2023-03-22"),
("1520","469","حاضر","2023-03-22"),
("1521","457","حاضر","2023-03-22"),
("1522","456","حاضر","2023-03-22"),
("1523","443","حاضر","2023-03-22"),
("1524","434","حاضر","2023-03-22"),
("1525","481","حاضر","2023-03-22"),
("1526","480","حاضر","2023-03-22"),
("1527","478","حاضر","2023-03-22"),
("1528","1036","حاضر","2023-03-22"),
("1529","1034","حاضر","2023-03-22"),
("1530","1030","حاضر","2023-03-22"),
("1531","1001","حاضر","2023-03-22"),
("1532","492","حاضر","2023-03-22"),
("1533","490","حاضر","2023-03-22"),
("1534","1061","حاضر","2023-03-22"),
("1535","1043","حاضر","2023-03-22"),
("1536","1040","حاضر","2023-03-22"),
("1537","1076","حاضر","2023-03-22"),
("1538","1066","حاضر","2023-03-22");
INSERT INTO stat VALUES
("1539","1056","حاضر","2023-03-22"),
("1540","1062","حاضر","2023-03-22"),
("1541","484","حاضر","2023-03-22"),
("1542","485","حاضر","2023-03-22"),
("1543","1028","حاضر","2023-03-22"),
("1544","947","حاضر","2023-03-22"),
("1545","1032","حاضر","2023-03-22"),
("1546","1064","حاضر","2023-03-22"),
("1547","886","حاضر","2023-03-23"),
("1548","836","حاضر","2023-03-23"),
("1549","834","حاضر","2023-03-23"),
("1550","515","حاضر","2023-03-23"),
("1551","513","حاضر","2023-03-23"),
("1552","514","حاضر","2023-03-23"),
("1553","904","حاضر","2023-03-23"),
("1554","991","حاضر","2023-03-23"),
("1555","1091","حاضر","2023-03-23"),
("1556","900","حاضر","2023-03-23"),
("1557","503","حاضر","2023-03-23"),
("1558","523","حاضر","2023-03-23"),
("1559","880","حاضر","2023-03-23"),
("1560","881","حاضر","2023-03-23"),
("1561","512","حاضر","2023-03-23"),
("1562","534","حاضر","2023-03-23"),
("1563","526","حاضر","2023-03-23"),
("1564","895","حاضر","2023-03-23"),
("1565","903","حاضر","2023-03-23"),
("1566","1092","حاضر","2023-03-23"),
("1567","340","حاضر","2023-03-23"),
("1568","350","حاضر","2023-03-23"),
("1569","364","حاضر","2023-03-23"),
("1570","373","حاضر","2023-03-23"),
("1571","387","حاضر","2023-03-23"),
("1572","391","حاضر","2023-03-23"),
("1573","396","حاضر","2023-03-23"),
("1574","397","حاضر","2023-03-23"),
("1575","400","حاضر","2023-03-23"),
("1576","417","حاضر","2023-03-23"),
("1577","386","حاضر","2023-03-23"),
("1578","874","حاضر","2023-03-23"),
("1579","432","حاضر","2023-03-23"),
("1580","964","حاضر","2023-03-23"),
("1581","1047","حاضر","2023-03-23"),
("1582","1053","حاضر","2023-03-23"),
("1583","1054","حاضر","2023-03-23"),
("1584","1084","حاضر","2023-03-23"),
("1585","403","حاضر","2023-03-23"),
("1586","339","حاضر","2023-03-23"),
("1587","370","حاضر","2023-03-23"),
("1588","371","حاضر","2023-03-23"),
("1589","1048","حاضر","2023-03-23"),
("1590","351","حاضر","2023-03-23"),
("1591","357","حاضر","2023-03-23"),
("1592","366","حاضر","2023-03-23"),
("1593","382","حاضر","2023-03-23"),
("1594","384","حاضر","2023-03-23"),
("1595","401","حاضر","2023-03-23"),
("1596","393","حاضر","2023-03-23"),
("1597","423","حاضر","2023-03-23"),
("1598","817","حاضر","2023-03-23"),
("1599","428","حاضر","2023-03-23"),
("1600","420","حاضر","2023-03-23"),
("1601","873","حاضر","2023-03-23"),
("1602","413","حاضر","2023-03-23"),
("1603","1075","حاضر","2023-03-23"),
("1604","1083","حاضر","2023-03-23"),
("1605","1086","حاضر","2023-03-23"),
("1606","661","حاضر","2023-03-23"),
("1607","674","حاضر","2023-03-23"),
("1608","659","حاضر","2023-03-23"),
("1609","681","حاضر","2023-03-23"),
("1610","685","حاضر","2023-03-23"),
("1611","690","حاضر","2023-03-23"),
("1612","699","حاضر","2023-03-23"),
("1613","802","حاضر","2023-03-23"),
("1614","805","حاضر","2023-03-23"),
("1615","809","حاضر","2023-03-23"),
("1616","939","حاضر","2023-03-23"),
("1617","1094","حاضر","2023-03-23"),
("1618","662","حاضر","2023-03-23"),
("1619","686","حاضر","2023-03-23"),
("1620","691","حاضر","2023-03-23"),
("1621","702","حاضر","2023-03-23"),
("1622","714","حاضر","2023-03-23"),
("1623","803","حاضر","2023-03-23"),
("1624","804","حاضر","2023-03-23"),
("1625","654","حاضر","2023-03-23"),
("1626","663","حاضر","2023-03-23"),
("1627","664","حاضر","2023-03-23"),
("1628","675","حاضر","2023-03-23"),
("1629","688","حاضر","2023-03-23"),
("1630","711","حاضر","2023-03-23"),
("1631","708","حاضر","2023-03-23"),
("1632","694","حاضر","2023-03-23"),
("1633","828","حاضر","2023-03-23"),
("1634","808","حاضر","2023-03-23"),
("1635","941","حاضر","2023-03-23"),
("1636","942","حاضر","2023-03-23"),
("1637","938","حاضر","2023-03-23"),
("1638","935","حاضر","2023-03-23");
INSERT INTO stat VALUES
("1639","706","حاضر","2023-03-23"),
("1640","700","حاضر","2023-03-23"),
("1641","701","حاضر","2023-03-23"),
("1642","698","حاضر","2023-03-23"),
("1643","712","حاضر","2023-03-23"),
("1644","1044","حاضر","2023-03-23"),
("1645","632","حاضر","2023-03-23"),
("1646","630","حاضر","2023-03-23"),
("1647","628","حاضر","2023-03-23"),
("1648","629","حاضر","2023-03-23"),
("1649","627","حاضر","2023-03-23"),
("1650","633","حاضر","2023-03-23"),
("1651","635","حاضر","2023-03-23"),
("1652","636","حاضر","2023-03-23"),
("1653","637","حاضر","2023-03-23"),
("1654","640","حاضر","2023-03-23"),
("1655","646","حاضر","2023-03-23"),
("1656","647","حاضر","2023-03-23"),
("1657","648","حاضر","2023-03-23"),
("1658","649","حاضر","2023-03-23"),
("1659","650","حاضر","2023-03-23"),
("1660","652","حاضر","2023-03-23"),
("1661","651","حاضر","2023-03-23"),
("1662","851","حاضر","2023-03-23"),
("1663","853","حاضر","2023-03-23"),
("1664","921","حاضر","2023-03-23"),
("1665","905","حاضر","2023-03-23"),
("1666","922","حاضر","2023-03-23"),
("1667","923","حاضر","2023-03-23"),
("1668","999","حاضر","2023-03-23"),
("1669","1010","حاضر","2023-03-23"),
("1670","1012","حاضر","2023-03-23"),
("1671","1015","حاضر","2023-03-23"),
("1672","1016","حاضر","2023-03-23"),
("1673","1018","حاضر","2023-03-23"),
("1674","620","حاضر","2023-03-23"),
("1675","621","حاضر","2023-03-23"),
("1676","537","حاضر","2023-03-26"),
("1677","545","حاضر","2023-03-26"),
("1678","547","حاضر","2023-03-26"),
("1679","567","حاضر","2023-03-26"),
("1680","580","حاضر","2023-03-26"),
("1681","589","حاضر","2023-03-26"),
("1682","587","حاضر","2023-03-26"),
("1683","573","حاضر","2023-03-26"),
("1684","592","حاضر","2023-03-26"),
("1685","593","حاضر","2023-03-26"),
("1686","612","حاضر","2023-03-26"),
("1687","617","حاضر","2023-03-26"),
("1688","1089","حاضر","2023-03-26"),
("1689","909","حاضر","2023-03-26"),
("1690","602","حاضر","2023-03-26"),
("1691","598","حاضر","2023-03-26"),
("1692","539","حاضر","2023-03-26"),
("1693","552","حاضر","2023-03-26"),
("1694","553","حاضر","2023-03-26"),
("1695","563","حاضر","2023-03-26"),
("1696","564","حاضر","2023-03-26"),
("1697","581","حاضر","2023-03-26"),
("1698","584","حاضر","2023-03-26"),
("1699","588","حاضر","2023-03-26"),
("1700","596","حاضر","2023-03-26"),
("1701","1073","حاضر","2023-03-26"),
("1702","595","حاضر","2023-03-26"),
("1703","335","حاضر","2023-03-26"),
("1704","350","حاضر","2023-03-26"),
("1705","356","حاضر","2023-03-26"),
("1706","373","حاضر","2023-03-26"),
("1707","386","حاضر","2023-03-26"),
("1708","387","حاضر","2023-03-26"),
("1709","391","حاضر","2023-03-26"),
("1710","396","حاضر","2023-03-26"),
("1711","395","حاضر","2023-03-26"),
("1712","397","حاضر","2023-03-26"),
("1713","398","حاضر","2023-03-26"),
("1714","400","حاضر","2023-03-26"),
("1715","403","حاضر","2023-03-26"),
("1716","412","حاضر","2023-03-26"),
("1717","421","حاضر","2023-03-26"),
("1718","425","حاضر","2023-03-26"),
("1719","427","حاضر","2023-03-26"),
("1720","432","حاضر","2023-03-26"),
("1721","874","حاضر","2023-03-26"),
("1722","876","حاضر","2023-03-26"),
("1723","877","حاضر","2023-03-26"),
("1724","914","حاضر","2023-03-26"),
("1725","964","حاضر","2023-03-26"),
("1726","966","حاضر","2023-03-26"),
("1727","1053","حاضر","2023-03-26"),
("1728","1054","حاضر","2023-03-26"),
("1729","1084","حاضر","2023-03-26"),
("1730","1085","حاضر","2023-03-26"),
("1731","338","حاضر","2023-03-26"),
("1732","339","حاضر","2023-03-26"),
("1733","352","حاضر","2023-03-26"),
("1734","363","حاضر","2023-03-26"),
("1735","370","حاضر","2023-03-26"),
("1736","392","حاضر","2023-03-26"),
("1737","394","حاضر","2023-03-26"),
("1738","409","حاضر","2023-03-26");
INSERT INTO stat VALUES
("1739","414","حاضر","2023-03-26"),
("1740","415","حاضر","2023-03-26"),
("1741","419","حاضر","2023-03-26"),
("1742","422","حاضر","2023-03-26"),
("1743","875","حاضر","2023-03-26"),
("1744","916","حاضر","2023-03-26"),
("1745","1050","حاضر","2023-03-26"),
("1746","334","حاضر","2023-03-26"),
("1747","351","حاضر","2023-03-26"),
("1748","357","حاضر","2023-03-26"),
("1749","366","حاضر","2023-03-26"),
("1750","375","حاضر","2023-03-26"),
("1751","337","حاضر","2023-03-26"),
("1752","380","حاضر","2023-03-26"),
("1753","381","حاضر","2023-03-26"),
("1754","384","حاضر","2023-03-26"),
("1755","393","حاضر","2023-03-26"),
("1756","404","حاضر","2023-03-26"),
("1757","408","حاضر","2023-03-26"),
("1758","411","حاضر","2023-03-26"),
("1759","413","حاضر","2023-03-26"),
("1760","420","حاضر","2023-03-26"),
("1761","423","حاضر","2023-03-26"),
("1762","428","حاضر","2023-03-26"),
("1763","816","حاضر","2023-03-26"),
("1764","913","حاضر","2023-03-26"),
("1765","873","حاضر","2023-03-26"),
("1766","965","حاضر","2023-03-26"),
("1767","1046","حاضر","2023-03-26"),
("1768","1049","حاضر","2023-03-26"),
("1769","1074","حاضر","2023-03-26"),
("1770","1075","حاضر","2023-03-26"),
("1771","1083","حاضر","2023-03-26"),
("1772","1086","حاضر","2023-03-26"),
("1773","1095","حاضر","2023-03-26"),
("1774","536","حاضر","2023-03-26"),
("1775","538","حاضر","2023-03-26"),
("1776","551","حاضر","2023-03-26"),
("1777","572","حاضر","2023-03-26"),
("1778","575","حاضر","2023-03-26"),
("1779","576","حاضر","2023-03-26"),
("1780","582","حاضر","2023-03-26"),
("1781","579","حاضر","2023-03-26"),
("1782","586","حاضر","2023-03-26"),
("1783","594","حاضر","2023-03-26"),
("1784","599","حاضر","2023-03-26"),
("1785","600","حاضر","2023-03-26"),
("1786","608","حاضر","2023-03-26"),
("1787","609","حاضر","2023-03-26"),
("1788","610","حاضر","2023-03-26"),
("1789","614","حاضر","2023-03-26"),
("1790","611","حاضر","2023-03-26"),
("1791","810","حاضر","2023-03-26"),
("1792","968","حاضر","2023-03-26"),
("1793","535","حاضر","2023-03-26"),
("1794","541","حاضر","2023-03-26"),
("1795","540","حاضر","2023-03-26"),
("1796","550","حاضر","2023-03-26"),
("1797","578","حاضر","2023-03-26"),
("1798","583","حاضر","2023-03-26"),
("1799","590","حاضر","2023-03-26"),
("1800","601","حاضر","2023-03-26"),
("1801","613","حاضر","2023-03-26"),
("1802","820","حاضر","2023-03-26"),
("1803","910","حاضر","2023-03-26"),
("1804","603","حاضر","2023-03-26"),
("1805","444","حاضر","2023-03-26"),
("1806","447","حاضر","2023-03-26"),
("1807","451","حاضر","2023-03-26"),
("1808","452","حاضر","2023-03-26"),
("1809","454","حاضر","2023-03-26"),
("1810","459","حاضر","2023-03-26"),
("1811","464","حاضر","2023-03-26"),
("1812","465","حاضر","2023-03-26"),
("1813","466","حاضر","2023-03-26"),
("1814","470","حاضر","2023-03-26"),
("1815","1071","حاضر","2023-03-26"),
("1816","1057","حاضر","2023-03-26"),
("1817","1038","حاضر","2023-03-26"),
("1818","1033","حاضر","2023-03-26"),
("1819","1037","حاضر","2023-03-26"),
("1820","1058","حاضر","2023-03-26"),
("1821","950","حاضر","2023-03-26"),
("1822","958","حاضر","2023-03-26"),
("1823","961","حاضر","2023-03-26"),
("1824","1029","حاضر","2023-03-26"),
("1825","483","حاضر","2023-03-26"),
("1826","489","حاضر","2023-03-26"),
("1827","495","حاضر","2023-03-26"),
("1828","474","حاضر","2023-03-26"),
("1829","477","حاضر","2023-03-26"),
("1830","467","حاضر","2023-03-26"),
("1831","471","حاضر","2023-03-26"),
("1832","482","حاضر","2023-03-26"),
("1833","496","حاضر","2023-03-26"),
("1834","872","حاضر","2023-03-26"),
("1835","1031","حاضر","2023-03-26"),
("1836","1042","حاضر","2023-03-26"),
("1837","435","حاضر","2023-03-26"),
("1838","437","حاضر","2023-03-26");
INSERT INTO stat VALUES
("1839","441","حاضر","2023-03-26"),
("1840","955","حاضر","2023-03-26"),
("1841","954","حاضر","2023-03-26"),
("1842","948","حاضر","2023-03-26"),
("1843","949","حاضر","2023-03-26"),
("1844","946","حاضر","2023-03-26"),
("1845","945","حاضر","2023-03-26"),
("1846","919","حاضر","2023-03-26"),
("1847","468","حاضر","2023-03-26"),
("1848","486","حاضر","2023-03-26"),
("1849","487","حاضر","2023-03-26"),
("1850","491","حاضر","2023-03-26"),
("1851","453","حاضر","2023-03-26"),
("1852","455","حاضر","2023-03-26"),
("1853","458","حاضر","2023-03-26"),
("1854","460","حاضر","2023-03-26"),
("1855","461","حاضر","2023-03-26"),
("1856","493","حاضر","2023-03-26"),
("1857","920","حاضر","2023-03-26"),
("1858","463","حاضر","2023-03-26"),
("1859","1002","حاضر","2023-03-26"),
("1860","434","حاضر","2023-03-26"),
("1861","469","حاضر","2023-03-26"),
("1862","457","حاضر","2023-03-26"),
("1863","456","حاضر","2023-03-26"),
("1864","445","حاضر","2023-03-26"),
("1865","443","حاضر","2023-03-26"),
("1866","1043","حاضر","2023-03-26"),
("1867","1040","حاضر","2023-03-26"),
("1868","1030","حاضر","2023-03-26"),
("1869","1001","حاضر","2023-03-26"),
("1870","944","حاضر","2023-03-26"),
("1871","943","حاضر","2023-03-26"),
("1872","494","حاضر","2023-03-26"),
("1873","492","حاضر","2023-03-26"),
("1874","490","حاضر","2023-03-26"),
("1875","475","حاضر","2023-03-26"),
("1876","478","حاضر","2023-03-26"),
("1877","472","حاضر","2023-03-26"),
("1878","1028","حاضر","2023-03-26"),
("1879","959","حاضر","2023-03-26"),
("1880","957","حاضر","2023-03-26"),
("1881","947","حاضر","2023-03-26"),
("1882","485","حاضر","2023-03-26"),
("1883","484","حاضر","2023-03-26"),
("1884","1076","حاضر","2023-03-26"),
("1885","1064","حاضر","2023-03-26"),
("1886","1070","حاضر","2023-03-26"),
("1887","1062","حاضر","2023-03-26"),
("1888","1056","حاضر","2023-03-26"),
("1889","335","حاضر","2023-03-28"),
("1890","343","حاضر","2023-03-28"),
("1891","356","حاضر","2023-03-28"),
("1892","367","حاضر","2023-03-28"),
("1893","373","حاضر","2023-03-28"),
("1894","386","حاضر","2023-03-28"),
("1895","387","حاضر","2023-03-28"),
("1896","391","حاضر","2023-03-28"),
("1897","395","حاضر","2023-03-28"),
("1898","396","حاضر","2023-03-28"),
("1899","397","حاضر","2023-03-28"),
("1900","398","حاضر","2023-03-28"),
("1901","403","حاضر","2023-03-28"),
("1902","412","حاضر","2023-03-28"),
("1903","417","حاضر","2023-03-28"),
("1904","874","حاضر","2023-03-28"),
("1905","876","حاضر","2023-03-28"),
("1906","914","حاضر","2023-03-28"),
("1907","964","حاضر","2023-03-28"),
("1908","966","حاضر","2023-03-28"),
("1909","1047","حاضر","2023-03-28"),
("1910","1053","حاضر","2023-03-28"),
("1911","1054","حاضر","2023-03-28"),
("1912","1084","حاضر","2023-03-28"),
("1913","1098","حاضر","2023-03-28"),
("1914","338","حاضر","2023-03-28"),
("1915","339","حاضر","2023-03-28"),
("1916","352","حاضر","2023-03-28"),
("1917","369","حاضر","2023-03-28"),
("1918","376","حاضر","2023-03-28"),
("1919","394","حاضر","2023-03-28"),
("1920","392","حاضر","2023-03-28"),
("1921","409","حاضر","2023-03-28"),
("1922","414","حاضر","2023-03-28"),
("1923","415","حاضر","2023-03-28"),
("1924","419","حاضر","2023-03-28"),
("1925","426","حاضر","2023-03-28"),
("1926","875","حاضر","2023-03-28"),
("1927","917","حاضر","2023-03-28"),
("1928","918","حاضر","2023-03-28"),
("1929","1048","حاضر","2023-03-28"),
("1930","1051","حاضر","2023-03-28"),
("1931","334","حاضر","2023-03-28"),
("1932","337","حاضر","2023-03-28"),
("1933","351","حاضر","2023-03-28"),
("1934","357","حاضر","2023-03-28"),
("1935","366","حاضر","2023-03-28"),
("1936","375","حاضر","2023-03-28"),
("1937","380","حاضر","2023-03-28"),
("1938","381","حاضر","2023-03-28");
INSERT INTO stat VALUES
("1939","382","حاضر","2023-03-28"),
("1940","384","حاضر","2023-03-28"),
("1941","393","حاضر","2023-03-28"),
("1942","401","حاضر","2023-03-28"),
("1943","404","حاضر","2023-03-28"),
("1944","406","حاضر","2023-03-28"),
("1945","408","حاضر","2023-03-28"),
("1946","411","حاضر","2023-03-28"),
("1947","413","حاضر","2023-03-28"),
("1948","420","حاضر","2023-03-28"),
("1949","423","حاضر","2023-03-28"),
("1950","428","حاضر","2023-03-28"),
("1951","816","حاضر","2023-03-28"),
("1952","873","حاضر","2023-03-28"),
("1953","913","حاضر","2023-03-28"),
("1954","915","حاضر","2023-03-28"),
("1955","965","حاضر","2023-03-28"),
("1956","1046","حاضر","2023-03-28"),
("1957","1049","حاضر","2023-03-28"),
("1958","1075","حاضر","2023-03-28"),
("1959","1083","حاضر","2023-03-28"),
("1960","1086","حاضر","2023-03-28"),
("1961","1095","حاضر","2023-03-28"),
("1962","1032","حاضر","2023-03-28"),
("1963","1028","حاضر","2023-03-28"),
("1964","957","حاضر","2023-03-28"),
("1965","484","حاضر","2023-03-28"),
("1966","485","حاضر","2023-03-28"),
("1967","953","حاضر","2023-03-28"),
("1968","956","حاضر","2023-03-28"),
("1969","959","حاضر","2023-03-28"),
("1970","433","حاضر","2023-03-28"),
("1971","436","حاضر","2023-03-28"),
("1972","439","حاضر","2023-03-28"),
("1973","438","حاضر","2023-03-28"),
("1974","442","حاضر","2023-03-28"),
("1975","454","حاضر","2023-03-28"),
("1976","451","حاضر","2023-03-28"),
("1977","447","حاضر","2023-03-28"),
("1978","444","حاضر","2023-03-28"),
("1979","464","حاضر","2023-03-28"),
("1980","465","حاضر","2023-03-28"),
("1981","467","حاضر","2023-03-28"),
("1982","470","حاضر","2023-03-28"),
("1983","471","حاضر","2023-03-28"),
("1984","479","حاضر","2023-03-28"),
("1985","477","حاضر","2023-03-28"),
("1986","482","حاضر","2023-03-28"),
("1987","483","حاضر","2023-03-28"),
("1988","495","حاضر","2023-03-28"),
("1989","489","حاضر","2023-03-28"),
("1990","496","حاضر","2023-03-28"),
("1991","872","حاضر","2023-03-28"),
("1992","952","حاضر","2023-03-28"),
("1993","961","حاضر","2023-03-28"),
("1994","950","حاضر","2023-03-28"),
("1995","958","حاضر","2023-03-28"),
("1996","1031","حاضر","2023-03-28"),
("1997","1033","حاضر","2023-03-28"),
("1998","1038","حاضر","2023-03-28"),
("1999","1057","حاضر","2023-03-28"),
("2000","1037","حاضر","2023-03-28"),
("2001","1042","حاضر","2023-03-28"),
("2002","1071","حاضر","2023-03-28"),
("2003","1069","حاضر","2023-03-28"),
("2004","955","حاضر","2023-03-28"),
("2005","954","حاضر","2023-03-28"),
("2006","1002","حاضر","2023-03-28"),
("2007","1003","حاضر","2023-03-28"),
("2008","951","حاضر","2023-03-28"),
("2009","949","حاضر","2023-03-28"),
("2010","948","حاضر","2023-03-28"),
("2011","946","حاضر","2023-03-28"),
("2012","920","حاضر","2023-03-28"),
("2013","945","حاضر","2023-03-28"),
("2014","919","حاضر","2023-03-28"),
("2015","493","حاضر","2023-03-28"),
("2016","491","حاضر","2023-03-28"),
("2017","486","حاضر","2023-03-28"),
("2018","487","حاضر","2023-03-28"),
("2019","468","حاضر","2023-03-28"),
("2020","461","حاضر","2023-03-28"),
("2021","458","حاضر","2023-03-28"),
("2022","446","حاضر","2023-03-28"),
("2023","455","حاضر","2023-03-28"),
("2024","437","حاضر","2023-03-28"),
("2025","435","حاضر","2023-03-28"),
("2026","441","حاضر","2023-03-28"),
("2027","469","حاضر","2023-03-28"),
("2028","456","حاضر","2023-03-28"),
("2029","457","حاضر","2023-03-28"),
("2030","445","حاضر","2023-03-28"),
("2031","443","حاضر","2023-03-28"),
("2032","434","حاضر","2023-03-28"),
("2033","475","حاضر","2023-03-28"),
("2034","480","حاضر","2023-03-28"),
("2035","490","حاضر","2023-03-28"),
("2036","492","حاضر","2023-03-28"),
("2037","943","حاضر","2023-03-28"),
("2038","944","حاضر","2023-03-28");
INSERT INTO stat VALUES
("2039","494","حاضر","2023-03-28"),
("2040","1067","حاضر","2023-03-28"),
("2041","1061","حاضر","2023-03-28"),
("2042","1043","حاضر","2023-03-28"),
("2043","1040","حاضر","2023-03-28"),
("2044","1036","حاضر","2023-03-28"),
("2045","1034","حاضر","2023-03-28"),
("2046","1001","حاضر","2023-03-28"),
("2047","1030","حاضر","2023-03-28"),
("2048","481","حاضر","2023-03-28"),
("2049","497","حاضر","2023-03-28"),
("2050","834","حاضر","2023-03-28"),
("2051","900","حاضر","2023-03-28"),
("2052","904","حاضر","2023-03-28"),
("2053","995","حاضر","2023-03-28"),
("2054","1093","حاضر","2023-03-28"),
("2055","501","حاضر","2023-03-28"),
("2056","509","حاضر","2023-03-28"),
("2057","518","حاضر","2023-03-28"),
("2058","526","حاضر","2023-03-28"),
("2059","520","حاضر","2023-03-28"),
("2060","528","حاضر","2023-03-28"),
("2061","534","حاضر","2023-03-28"),
("2062","835","حاضر","2023-03-28"),
("2063","837","حاضر","2023-03-28"),
("2064","884","حاضر","2023-03-28"),
("2065","903","حاضر","2023-03-28"),
("2066","523","حاضر","2023-03-28"),
("2067","503","حاضر","2023-03-28"),
("2068","524","حاضر","2023-03-28"),
("2069","525","حاضر","2023-03-28"),
("2070","829","حاضر","2023-03-28"),
("2071","897","حاضر","2023-03-28"),
("2072","990","حاضر","2023-03-28"),
("2073","537","حاضر","2023-03-29"),
("2074","545","حاضر","2023-03-29"),
("2075","546","حاضر","2023-03-29"),
("2076","567","حاضر","2023-03-29"),
("2077","573","حاضر","2023-03-29"),
("2078","589","حاضر","2023-03-29"),
("2079","587","حاضر","2023-03-29"),
("2080","356","حاضر","2023-03-29"),
("2081","367","حاضر","2023-03-29"),
("2082","373","حاضر","2023-03-29"),
("2083","386","حاضر","2023-03-29"),
("2084","387","حاضر","2023-03-29"),
("2085","391","حاضر","2023-03-29"),
("2086","395","حاضر","2023-03-29"),
("2087","396","حاضر","2023-03-29"),
("2088","397","حاضر","2023-03-29"),
("2089","398","حاضر","2023-03-29"),
("2090","400","حاضر","2023-03-29"),
("2091","402","حاضر","2023-03-29"),
("2092","403","حاضر","2023-03-29"),
("2093","412","حاضر","2023-03-29"),
("2094","425","حاضر","2023-03-29"),
("2095","874","حاضر","2023-03-29"),
("2096","876","حاضر","2023-03-29"),
("2097","914","حاضر","2023-03-29"),
("2098","966","حاضر","2023-03-29"),
("2099","1052","حاضر","2023-03-29"),
("2100","1047","حاضر","2023-03-29"),
("2101","1053","حاضر","2023-03-29"),
("2102","1054","حاضر","2023-03-29"),
("2103","1084","حاضر","2023-03-29"),
("2104","1085","حاضر","2023-03-29"),
("2105","1098","حاضر","2023-03-29"),
("2106","334","حاضر","2023-03-29"),
("2107","337","حاضر","2023-03-29"),
("2108","351","حاضر","2023-03-29"),
("2109","357","حاضر","2023-03-29"),
("2110","359","حاضر","2023-03-29"),
("2111","361","حاضر","2023-03-29"),
("2112","362","حاضر","2023-03-29"),
("2113","366","حاضر","2023-03-29"),
("2114","375","حاضر","2023-03-29"),
("2115","380","حاضر","2023-03-29"),
("2116","381","حاضر","2023-03-29"),
("2117","382","حاضر","2023-03-29"),
("2118","384","حاضر","2023-03-29"),
("2119","393","حاضر","2023-03-29"),
("2120","401","حاضر","2023-03-29"),
("2121","404","حاضر","2023-03-29"),
("2122","406","حاضر","2023-03-29"),
("2123","408","حاضر","2023-03-29"),
("2124","411","حاضر","2023-03-29"),
("2125","413","حاضر","2023-03-29"),
("2126","420","حاضر","2023-03-29"),
("2127","423","حاضر","2023-03-29"),
("2128","428","حاضر","2023-03-29"),
("2129","816","حاضر","2023-03-29"),
("2130","817","حاضر","2023-03-29"),
("2131","882","حاضر","2023-03-29"),
("2132","913","حاضر","2023-03-29"),
("2133","915","حاضر","2023-03-29"),
("2134","965","حاضر","2023-03-29"),
("2135","1046","حاضر","2023-03-29"),
("2136","1049","حاضر","2023-03-29"),
("2137","1074","حاضر","2023-03-29"),
("2138","1075","حاضر","2023-03-29");
INSERT INTO stat VALUES
("2139","1083","حاضر","2023-03-29"),
("2140","1086","حاضر","2023-03-29"),
("2141","1095","حاضر","2023-03-29"),
("2142","338","حاضر","2023-03-29"),
("2143","339","حاضر","2023-03-29"),
("2144","352","حاضر","2023-03-29"),
("2145","360","حاضر","2023-03-29"),
("2146","365","حاضر","2023-03-29"),
("2147","369","حاضر","2023-03-29"),
("2148","370","حاضر","2023-03-29"),
("2149","371","حاضر","2023-03-29"),
("2150","376","حاضر","2023-03-29"),
("2151","392","حاضر","2023-03-29"),
("2152","394","حاضر","2023-03-29"),
("2153","409","حاضر","2023-03-29"),
("2154","414","حاضر","2023-03-29"),
("2155","419","حاضر","2023-03-29"),
("2156","415","حاضر","2023-03-29"),
("2157","426","حاضر","2023-03-29"),
("2158","917","حاضر","2023-03-29"),
("2159","918","حاضر","2023-03-29"),
("2160","1048","حاضر","2023-03-29"),
("2161","1050","حاضر","2023-03-29"),
("2162","1051","حاضر","2023-03-29"),
("2163","732","حاضر","2023-03-29"),
("2164","739","حاضر","2023-03-29"),
("2165","756","حاضر","2023-03-29"),
("2166","765","حاضر","2023-03-29"),
("2167","745","حاضر","2023-03-29"),
("2168","775","حاضر","2023-03-29"),
("2169","513","حاضر","2023-03-30"),
("2170","515","حاضر","2023-03-30"),
("2171","834","حاضر","2023-03-30"),
("2172","836","حاضر","2023-03-30"),
("2173","886","حاضر","2023-03-30"),
("2174","887","حاضر","2023-03-30"),
("2175","892","حاضر","2023-03-30"),
("2176","900","حاضر","2023-03-30"),
("2177","501","حاضر","2023-03-30"),
("2178","534","حاضر","2023-03-30"),
("2179","528","حاضر","2023-03-30"),
("2180","520","حاضر","2023-03-30"),
("2181","903","حاضر","2023-03-30"),
("2182","891","حاضر","2023-03-30"),
("2183","899","حاضر","2023-03-30"),
("2184","888","حاضر","2023-03-30"),
("2185","837","حاضر","2023-03-30"),
("2186","503","حاضر","2023-03-30"),
("2187","512","حاضر","2023-03-30"),
("2188","523","حاضر","2023-03-30"),
("2189","684","حاضر","2023-03-30"),
("2190","686","حاضر","2023-03-30"),
("2191","689","حاضر","2023-03-30"),
("2192","691","حاضر","2023-03-30"),
("2193","803","حاضر","2023-03-30"),
("2194","714","حاضر","2023-03-30"),
("2195","804","حاضر","2023-03-30"),
("2196","1097","حاضر","2023-03-30"),
("2197","659","حاضر","2023-03-30"),
("2198","661","حاضر","2023-03-30"),
("2199","669","حاضر","2023-03-30"),
("2200","670","حاضر","2023-03-30"),
("2201","674","حاضر","2023-03-30"),
("2202","676","حاضر","2023-03-30"),
("2203","679","حاضر","2023-03-30"),
("2204","681","حاضر","2023-03-30"),
("2205","685","حاضر","2023-03-30"),
("2206","703","حاضر","2023-03-30"),
("2207","704","حاضر","2023-03-30"),
("2208","699","حاضر","2023-03-30"),
("2209","710","حاضر","2023-03-30"),
("2210","802","حاضر","2023-03-30"),
("2211","805","حاضر","2023-03-30"),
("2212","1096","حاضر","2023-03-30"),
("2213","1094","حاضر","2023-03-30"),
("2214","698","حاضر","2023-03-30"),
("2215","706","حاضر","2023-03-30"),
("2216","712","حاضر","2023-03-30"),
("2217","827","حاضر","2023-03-30"),
("2218","1044","حاضر","2023-03-30"),
("2219","935","حاضر","2023-03-30"),
("2220","654","حاضر","2023-03-30"),
("2221","657","حاضر","2023-03-30"),
("2222","658","حاضر","2023-03-30"),
("2223","663","حاضر","2023-03-30"),
("2224","668","حاضر","2023-03-30"),
("2225","683","حاضر","2023-03-30"),
("2226","688","حاضر","2023-03-30"),
("2227","694","حاضر","2023-03-30"),
("2228","705","حاضر","2023-03-30"),
("2229","709","حاضر","2023-03-30"),
("2230","711","حاضر","2023-03-30"),
("2231","713","حاضر","2023-03-30"),
("2232","828","حاضر","2023-03-30"),
("2233","940","حاضر","2023-03-30"),
("2234","438","حاضر","2023-03-30"),
("2235","442","حاضر","2023-03-30"),
("2236","444","حاضر","2023-03-30"),
("2237","447","حاضر","2023-03-30"),
("2238","436","حاضر","2023-03-30");
INSERT INTO stat VALUES
("2239","451","حاضر","2023-03-30"),
("2240","452","حاضر","2023-03-30"),
("2241","464","حاضر","2023-03-30"),
("2242","454","حاضر","2023-03-30"),
("2243","467","حاضر","2023-03-30"),
("2244","470","حاضر","2023-03-30"),
("2245","474","حاضر","2023-03-30"),
("2246","477","حاضر","2023-03-30"),
("2247","1071","حاضر","2023-03-30"),
("2248","1069","حاضر","2023-03-30"),
("2249","1058","حاضر","2023-03-30"),
("2250","1042","حاضر","2023-03-30"),
("2251","1033","حاضر","2023-03-30"),
("2252","1029","حاضر","2023-03-30"),
("2253","1031","حاضر","2023-03-30"),
("2254","961","حاضر","2023-03-30"),
("2255","958","حاضر","2023-03-30"),
("2256","471","حاضر","2023-03-30"),
("2257","482","حاضر","2023-03-30"),
("2258","483","حاضر","2023-03-30"),
("2259","489","حاضر","2023-03-30"),
("2260","495","حاضر","2023-03-30"),
("2261","496","حاضر","2023-03-30"),
("2262","872","حاضر","2023-03-30"),
("2263","950","حاضر","2023-03-30"),
("2264","952","حاضر","2023-03-30"),
("2265","493","حاضر","2023-03-30"),
("2266","487","حاضر","2023-03-30"),
("2267","441","حاضر","2023-03-30"),
("2268","437","حاضر","2023-03-30"),
("2269","948","حاضر","2023-03-30"),
("2270","945","حاضر","2023-03-30"),
("2271","920","حاضر","2023-03-30"),
("2272","919","حاضر","2023-03-30"),
("2273","486","حاضر","2023-03-30"),
("2274","463","حاضر","2023-03-30"),
("2275","1113","حاضر","2023-03-30"),
("2276","1101","حاضر","2023-03-30"),
("2277","1002","حاضر","2023-03-30"),
("2278","1100","حاضر","2023-03-30"),
("2279","951","حاضر","2023-03-30"),
("2280","949","حاضر","2023-03-30"),
("2281","1067","حاضر","2023-03-30"),
("2282","1030","حاضر","2023-03-30"),
("2283","1001","حاضر","2023-03-30"),
("2284","1043","حاضر","2023-03-30"),
("2285","1061","حاضر","2023-03-30"),
("2286","944","حاضر","2023-03-30"),
("2287","494","حاضر","2023-03-30"),
("2288","1040","حاضر","2023-03-30"),
("2289","943","حاضر","2023-03-30"),
("2290","481","حاضر","2023-03-30"),
("2291","490","حاضر","2023-03-30"),
("2292","492","حاضر","2023-03-30"),
("2293","445","حاضر","2023-03-30"),
("2294","457","حاضر","2023-03-30"),
("2295","472","حاضر","2023-03-30"),
("2296","475","حاضر","2023-03-30"),
("2297","478","حاضر","2023-03-30"),
("2298","456","حاضر","2023-03-30"),
("2299","439","حاضر","2023-03-30"),
("2300","435","حاضر","2023-03-30"),
("2301","434","حاضر","2023-03-30"),
("2302","453","حاضر","2023-03-30"),
("2303","455","حاضر","2023-03-30"),
("2304","484","حاضر","2023-03-30"),
("2305","953","حاضر","2023-03-30"),
("2306","947","حاضر","2023-03-30"),
("2307","946","حاضر","2023-03-30"),
("2308","485","حاضر","2023-03-30"),
("2309","461","حاضر","2023-03-30"),
("2310","954","حاضر","2023-03-30"),
("2311","491","حاضر","2023-03-30"),
("2312","1076","حاضر","2023-03-30"),
("2313","1070","حاضر","2023-03-30"),
("2314","1068","حاضر","2023-03-30"),
("2315","1066","حاضر","2023-03-30"),
("2316","1062","حاضر","2023-03-30"),
("2317","955","حاضر","2023-03-30"),
("2318","956","حاضر","2023-03-30"),
("2319","1003","حاضر","2023-03-30"),
("2320","957","حاضر","2023-03-30"),
("2321","959","حاضر","2023-03-30"),
("2322","1028","حاضر","2023-03-30"),
("2323","1056","حاضر","2023-03-30"),
("2324","1039","حاضر","2023-03-30"),
("2325","1041","حاضر","2023-03-30"),
("2326","1032","حاضر","2023-03-30"),
("2327","334","حاضر","2023-04-02"),
("2328","337","حاضر","2023-04-02"),
("2329","357","حاضر","2023-04-02"),
("2330","359","حاضر","2023-04-02"),
("2331","361","حاضر","2023-04-02"),
("2332","362","حاضر","2023-04-02"),
("2333","366","حاضر","2023-04-02"),
("2334","380","حاضر","2023-04-02"),
("2335","381","حاضر","2023-04-02"),
("2336","382","حاضر","2023-04-02"),
("2337","401","حاضر","2023-04-02"),
("2338","404","حاضر","2023-04-02");
INSERT INTO stat VALUES
("2339","413","حاضر","2023-04-02"),
("2340","420","حاضر","2023-04-02"),
("2341","423","حاضر","2023-04-02"),
("2342","428","حاضر","2023-04-02"),
("2343","816","حاضر","2023-04-02"),
("2344","873","حاضر","2023-04-02"),
("2345","882","حاضر","2023-04-02"),
("2346","913","حاضر","2023-04-02"),
("2347","915","حاضر","2023-04-02"),
("2348","965","حاضر","2023-04-02"),
("2349","1046","حاضر","2023-04-02"),
("2350","1049","حاضر","2023-04-02"),
("2351","1074","حاضر","2023-04-02"),
("2352","1075","حاضر","2023-04-02"),
("2353","1083","حاضر","2023-04-02"),
("2354","1086","حاضر","2023-04-02"),
("2355","1095","حاضر","2023-04-02"),
("2356","1110","حاضر","2023-04-02"),
("2357","351","حاضر","2023-04-02"),
("2358","817","حاضر","2023-04-02"),
("2359","346","حاضر","2023-04-02"),
("2360","364","حاضر","2023-04-02"),
("2361","356","حاضر","2023-04-02"),
("2362","373","حاضر","2023-04-02"),
("2363","386","حاضر","2023-04-02"),
("2364","387","حاضر","2023-04-02"),
("2365","396","حاضر","2023-04-02"),
("2366","391","حاضر","2023-04-02"),
("2367","400","حاضر","2023-04-02"),
("2368","403","حاضر","2023-04-02"),
("2369","397","حاضر","2023-04-02"),
("2370","412","حاضر","2023-04-02"),
("2371","417","حاضر","2023-04-02"),
("2372","421","حاضر","2023-04-02"),
("2373","874","حاضر","2023-04-02"),
("2374","876","حاضر","2023-04-02"),
("2375","914","حاضر","2023-04-02"),
("2376","964","حاضر","2023-04-02"),
("2377","966","حاضر","2023-04-02"),
("2378","1047","حاضر","2023-04-02"),
("2379","1052","حاضر","2023-04-02"),
("2380","1053","حاضر","2023-04-02"),
("2381","1054","حاضر","2023-04-02"),
("2382","1084","حاضر","2023-04-02"),
("2383","1085","حاضر","2023-04-02"),
("2384","1098","حاضر","2023-04-02"),
("2385","1116","حاضر","2023-04-02"),
("2386","338","حاضر","2023-04-02"),
("2387","339","حاضر","2023-04-02"),
("2388","352","حاضر","2023-04-02"),
("2389","360","حاضر","2023-04-02"),
("2390","365","حاضر","2023-04-02"),
("2391","369","حاضر","2023-04-02"),
("2392","370","حاضر","2023-04-02"),
("2393","371","حاضر","2023-04-02"),
("2394","376","حاضر","2023-04-02"),
("2395","392","حاضر","2023-04-02"),
("2396","414","حاضر","2023-04-02"),
("2397","415","حاضر","2023-04-02"),
("2398","419","حاضر","2023-04-02"),
("2399","422","حاضر","2023-04-02"),
("2400","424","حاضر","2023-04-02"),
("2401","426","حاضر","2023-04-02"),
("2402","429","حاضر","2023-04-02"),
("2403","875","حاضر","2023-04-02"),
("2404","917","حاضر","2023-04-02"),
("2405","918","حاضر","2023-04-02"),
("2406","1048","حاضر","2023-04-02"),
("2407","1050","حاضر","2023-04-02"),
("2408","1051","حاضر","2023-04-02"),
("2409","1111","حاضر","2023-04-02"),
("2410","1112","حاضر","2023-04-02"),
("2411","662","حاضر","2023-04-02"),
("2412","686","حاضر","2023-04-02"),
("2413","684","حاضر","2023-04-02"),
("2414","691","حاضر","2023-04-02"),
("2415","702","حاضر","2023-04-02"),
("2416","804","حاضر","2023-04-02"),
("2417","803","حاضر","2023-04-02"),
("2418","714","حاضر","2023-04-02"),
("2419","689","حاضر","2023-04-02"),
("2420","697","حاضر","2023-04-02"),
("2421","1045","حاضر","2023-04-02"),
("2422","1097","حاضر","2023-04-02"),
("2423","1115","حاضر","2023-04-02"),
("2424","657","حاضر","2023-04-02"),
("2425","654","حاضر","2023-04-02"),
("2426","663","حاضر","2023-04-02"),
("2427","658","حاضر","2023-04-02"),
("2428","668","حاضر","2023-04-02"),
("2429","688","حاضر","2023-04-02"),
("2430","677","حاضر","2023-04-02"),
("2431","683","حاضر","2023-04-02"),
("2432","675","حاضر","2023-04-02"),
("2433","664","حاضر","2023-04-02"),
("2434","808","حاضر","2023-04-02"),
("2435","940","حاضر","2023-04-02"),
("2436","828","حاضر","2023-04-02"),
("2437","713","حاضر","2023-04-02"),
("2438","711","حاضر","2023-04-02");
INSERT INTO stat VALUES
("2439","709","حاضر","2023-04-02"),
("2440","705","حاضر","2023-04-02"),
("2441","708","حاضر","2023-04-02"),
("2442","659","حاضر","2023-04-02"),
("2443","660","حاضر","2023-04-02"),
("2444","669","حاضر","2023-04-02"),
("2445","661","حاضر","2023-04-02"),
("2446","670","حاضر","2023-04-02"),
("2447","674","حاضر","2023-04-02"),
("2448","679","حاضر","2023-04-02"),
("2449","681","حاضر","2023-04-02"),
("2450","685","حاضر","2023-04-02"),
("2451","676","حاضر","2023-04-02"),
("2452","699","حاضر","2023-04-02"),
("2453","690","حاضر","2023-04-02"),
("2454","703","حاضر","2023-04-02"),
("2455","704","حاضر","2023-04-02"),
("2456","1080","حاضر","2023-04-02"),
("2457","1094","حاضر","2023-04-02"),
("2458","1096","حاضر","2023-04-02"),
("2459","1099","حاضر","2023-04-02"),
("2460","1114","حاضر","2023-04-02"),
("2461","698","حاضر","2023-04-02"),
("2462","700","حاضر","2023-04-02"),
("2463","706","حاضر","2023-04-02"),
("2464","701","حاضر","2023-04-02"),
("2465","938","حاضر","2023-04-02"),
("2466","1044","حاضر","2023-04-02"),
("2467","1079","حاضر","2023-04-02"),
("2468","935","حاضر","2023-04-02"),
("2469","738","حاضر","2023-04-05"),
("2470","743","حاضر","2023-04-05"),
("2471","751","حاضر","2023-04-05"),
("2472","769","حاضر","2023-04-05"),
("2473","785","حاضر","2023-04-05"),
("2474","757","حاضر","2023-04-05"),
("2475","351","حاضر","2023-04-05"),
("2476","357","حاضر","2023-04-05"),
("2477","359","حاضر","2023-04-05"),
("2478","361","حاضر","2023-04-05"),
("2479","362","حاضر","2023-04-05"),
("2480","366","حاضر","2023-04-05"),
("2481","375","حاضر","2023-04-05"),
("2482","384","حاضر","2023-04-05"),
("2483","401","حاضر","2023-04-05"),
("2484","404","حاضر","2023-04-05"),
("2485","406","حاضر","2023-04-05"),
("2486","411","حاضر","2023-04-05"),
("2487","420","حاضر","2023-04-05"),
("2488","423","حاضر","2023-04-05"),
("2489","428","حاضر","2023-04-05"),
("2490","816","حاضر","2023-04-05"),
("2491","817","حاضر","2023-04-05"),
("2492","873","حاضر","2023-04-05"),
("2493","882","حاضر","2023-04-05"),
("2494","965","حاضر","2023-04-05"),
("2495","1046","حاضر","2023-04-05"),
("2496","1049","حاضر","2023-04-05"),
("2497","1074","حاضر","2023-04-05"),
("2498","1075","حاضر","2023-04-05"),
("2499","1083","حاضر","2023-04-05"),
("2500","1086","حاضر","2023-04-05"),
("2501","1095","حاضر","2023-04-05"),
("2502","1110","حاضر","2023-04-05"),
("2503","340","حاضر","2023-04-05"),
("2504","346","حاضر","2023-04-05"),
("2505","356","حاضر","2023-04-05"),
("2506","364","حاضر","2023-04-05"),
("2507","373","حاضر","2023-04-05"),
("2508","386","حاضر","2023-04-05"),
("2509","387","حاضر","2023-04-05"),
("2510","397","حاضر","2023-04-05"),
("2511","403","حاضر","2023-04-05"),
("2512","396","حاضر","2023-04-05"),
("2513","391","حاضر","2023-04-05"),
("2514","412","حاضر","2023-04-05"),
("2515","421","حاضر","2023-04-05"),
("2516","427","حاضر","2023-04-05"),
("2517","432","حاضر","2023-04-05"),
("2518","874","حاضر","2023-04-05"),
("2519","876","حاضر","2023-04-05"),
("2520","964","حاضر","2023-04-05"),
("2521","966","حاضر","2023-04-05"),
("2522","1047","حاضر","2023-04-05"),
("2523","1053","حاضر","2023-04-05"),
("2524","1084","حاضر","2023-04-05"),
("2525","1054","حاضر","2023-04-05"),
("2526","1085","حاضر","2023-04-05"),
("2527","1116","حاضر","2023-04-05"),
("2528","1098","حاضر","2023-04-05"),
("2529","352","حاضر","2023-04-05"),
("2530","360","حاضر","2023-04-05"),
("2531","363","حاضر","2023-04-05"),
("2532","365","حاضر","2023-04-05"),
("2533","370","حاضر","2023-04-05"),
("2534","371","حاضر","2023-04-05"),
("2535","376","حاضر","2023-04-05"),
("2536","392","حاضر","2023-04-05"),
("2537","422","حاضر","2023-04-05"),
("2538","875","حاضر","2023-04-05");
INSERT INTO stat VALUES
("2539","917","حاضر","2023-04-05"),
("2540","918","حاضر","2023-04-05"),
("2541","1048","حاضر","2023-04-05"),
("2542","1050","حاضر","2023-04-05"),
("2543","1051","حاضر","2023-04-05"),
("2544","1111","حاضر","2023-04-05"),
("2545","1112","حاضر","2023-04-05"),
("2546","1120","حاضر","2023-04-05"),
("2547","748","حاضر","2023-04-05"),
("2548","745","حاضر","2023-04-05"),
("2549","756","حاضر","2023-04-05"),
("2550","765","حاضر","2023-04-05"),
("2551","770","حاضر","2023-04-05"),
("2552","775","حاضر","2023-04-05"),
("2553","739","حاضر","2023-04-05"),
("2554","734","حاضر","2023-04-05"),
("2555","732","حاضر","2023-04-05"),
("2556","722","حاضر","2023-04-05"),
("2557","731","حاضر","2023-04-05"),
("2558","736","حاضر","2023-04-05"),
("2559","744","حاضر","2023-04-05"),
("2560","750","حاضر","2023-04-05"),
("2561","759","حاضر","2023-04-05"),
("2562","767","حاضر","2023-04-05"),
("2563","772","حاضر","2023-04-05"),
("2564","776","حاضر","2023-04-05"),
("2565","777","حاضر","2023-04-05"),
("2566","792","حاضر","2023-04-05"),
("2567","794","حاضر","2023-04-05"),
("2568","796","حاضر","2023-04-05"),
("2569","1022","حاضر","2023-04-05"),
("2570","1023","حاضر","2023-04-05"),
("2571","1024","حاضر","2023-04-05"),
("2572","782","حاضر","2023-04-05"),
("2573","784","حاضر","2023-04-05"),
("2574","790","حاضر","2023-04-05"),
("2575","970","حاضر","2023-04-05"),
("2576","1025","حاضر","2023-04-05"),
("2577","1109","حاضر","2023-04-05"),
("2578","715","حاضر","2023-04-05"),
("2579","719","حاضر","2023-04-05"),
("2580","720","حاضر","2023-04-05"),
("2581","740","حاضر","2023-04-05"),
("2582","749","حاضر","2023-04-05"),
("2583","758","حاضر","2023-04-05"),
("2584","773","حاضر","2023-04-05"),
("2585","778","حاضر","2023-04-05"),
("2586","780","حاضر","2023-04-05"),
("2587","783","حاضر","2023-04-05"),
("2588","788","حاضر","2023-04-05"),
("2589","791","حاضر","2023-04-05"),
("2590","798","حاضر","2023-04-05"),
("2591","842","حاضر","2023-04-05"),
("2592","797","حاضر","2023-04-05"),
("2593","843","حاضر","2023-04-05"),
("2594","844","حاضر","2023-04-05"),
("2595","848","حاضر","2023-04-05"),
("2596","503","حاضر","2023-04-05"),
("2597","512","حاضر","2023-04-05"),
("2598","524","حاضر","2023-04-05"),
("2599","525","حاضر","2023-04-05"),
("2600","829","حاضر","2023-04-05"),
("2601","509","حاضر","2023-04-05"),
("2602","526","حاضر","2023-04-05"),
("2603","528","حاضر","2023-04-05"),
("2604","837","حاضر","2023-04-05"),
("2605","884","حاضر","2023-04-05"),
("2606","888","حاضر","2023-04-05"),
("2607","889","حاضر","2023-04-05"),
("2608","899","حاضر","2023-04-05"),
("2609","1122","حاضر","2023-04-05"),
("2610","1092","حاضر","2023-04-05"),
("2611","497","حاضر","2023-04-05"),
("2612","513","حاضر","2023-04-05"),
("2613","515","حاضر","2023-04-05"),
("2614","522","حاضر","2023-04-05"),
("2615","840","حاضر","2023-04-05"),
("2616","886","حاضر","2023-04-05"),
("2617","887","حاضر","2023-04-05"),
("2618","995","حاضر","2023-04-05"),
("2619","438","حاضر","2023-04-05"),
("2620","442","حاضر","2023-04-05"),
("2621","452","حاضر","2023-04-05"),
("2622","447","حاضر","2023-04-05"),
("2623","444","حاضر","2023-04-05"),
("2624","470","حاضر","2023-04-05"),
("2625","482","حاضر","2023-04-05"),
("2626","477","حاضر","2023-04-05"),
("2627","495","حاضر","2023-04-05"),
("2628","496","حاضر","2023-04-05"),
("2629","1071","حاضر","2023-04-05"),
("2630","1069","حاضر","2023-04-05"),
("2631","958","حاضر","2023-04-05"),
("2632","952","حاضر","2023-04-05"),
("2633","872","حاضر","2023-04-05"),
("2634","950","حاضر","2023-04-05"),
("2635","919","حاضر","2023-04-05"),
("2636","493","حاضر","2023-04-05"),
("2637","468","حاضر","2023-04-05"),
("2638","437","حاضر","2023-04-05");
INSERT INTO stat VALUES
("2639","446","حاضر","2023-04-05"),
("2640","951","حاضر","2023-04-05"),
("2641","949","حاضر","2023-04-05"),
("2642","948","حاضر","2023-04-05"),
("2643","463","حاضر","2023-04-05"),
("2644","1002","حاضر","2023-04-05"),
("2645","1101","حاضر","2023-04-05"),
("2646","1100","حاضر","2023-04-05"),
("2647","1119","حاضر","2023-04-05"),
("2648","433","حاضر","2023-04-05"),
("2649","434","حاضر","2023-04-05"),
("2650","439","حاضر","2023-04-05"),
("2651","443","حاضر","2023-04-05"),
("2652","457","حاضر","2023-04-05"),
("2653","456","حاضر","2023-04-05"),
("2654","445","حاضر","2023-04-05"),
("2655","469","حاضر","2023-04-05"),
("2656","492","حاضر","2023-04-05"),
("2657","494","حاضر","2023-04-05"),
("2658","1118","حاضر","2023-04-05"),
("2659","1067","حاضر","2023-04-05"),
("2660","1061","حاضر","2023-04-05"),
("2661","1001","حاضر","2023-04-05"),
("2662","453","حاضر","2023-04-05"),
("2663","455","حاضر","2023-04-05"),
("2664","1076","حاضر","2023-04-05"),
("2665","1070","حاضر","2023-04-05"),
("2666","1064","حاضر","2023-04-05"),
("2667","1066","حاضر","2023-04-05"),
("2668","1068","حاضر","2023-04-05"),
("2669","959","حاضر","2023-04-05"),
("2670","1003","حاضر","2023-04-05"),
("2671","1062","حاضر","2023-04-05"),
("2672","956","حاضر","2023-04-05"),
("2673","461","حاضر","2023-04-05"),
("2674","484","حاضر","2023-04-05"),
("2675","491","حاضر","2023-04-05"),
("2676","947","حاضر","2023-04-05"),
("2677","946","حاضر","2023-04-05"),
("2678","953","حاضر","2023-04-05"),
("2679","954","حاضر","2023-04-05"),
("2680","734","حاضر","2023-04-06"),
("2681","741","حاضر","2023-04-06"),
("2682","745","حاضر","2023-04-06"),
("2683","756","حاضر","2023-04-06"),
("2684","765","حاضر","2023-04-06"),
("2685","775","حاضر","2023-04-06"),
("2686","784","حاضر","2023-04-06"),
("2687","790","حاضر","2023-04-06"),
("2688","1025","حاضر","2023-04-06"),
("2689","1127","حاضر","2023-04-06"),
("2690","1125","حاضر","2023-04-06"),
("2691","1109","حاضر","2023-04-06"),
("2692","1108","حاضر","2023-04-06"),
("2693","770","حاضر","2023-04-06"),
("2694","841","حاضر","2023-04-06"),
("2695","716","حاضر","2023-04-06"),
("2696","721","حاضر","2023-04-06"),
("2697","722","حاضر","2023-04-06"),
("2698","731","حاضر","2023-04-06"),
("2699","750","حاضر","2023-04-06"),
("2700","759","حاضر","2023-04-06"),
("2701","772","حاضر","2023-04-06"),
("2702","776","حاضر","2023-04-06"),
("2703","777","حاضر","2023-04-06"),
("2704","792","حاضر","2023-04-06"),
("2705","1022","حاضر","2023-04-06"),
("2706","1023","حاضر","2023-04-06"),
("2707","1024","حاضر","2023-04-06"),
("2708","1126","حاضر","2023-04-06"),
("2709","717","حاضر","2023-04-06"),
("2710","738","حاضر","2023-04-06"),
("2711","742","حاضر","2023-04-06"),
("2712","751","حاضر","2023-04-06"),
("2713","757","حاضر","2023-04-06"),
("2714","763","حاضر","2023-04-06"),
("2715","769","حاضر","2023-04-06"),
("2716","785","حاضر","2023-04-06"),
("2717","715","حاضر","2023-04-06"),
("2718","719","حاضر","2023-04-06"),
("2719","720","حاضر","2023-04-06"),
("2720","740","حاضر","2023-04-06"),
("2721","749","حاضر","2023-04-06"),
("2722","758","حاضر","2023-04-06"),
("2723","762","حاضر","2023-04-06"),
("2724","773","حاضر","2023-04-06"),
("2725","778","حاضر","2023-04-06"),
("2726","780","حاضر","2023-04-06"),
("2727","788","حاضر","2023-04-06"),
("2728","791","حاضر","2023-04-06"),
("2729","798","حاضر","2023-04-06"),
("2730","996","حاضر","2023-04-06"),
("2731","438","حاضر","2023-04-06"),
("2732","442","حاضر","2023-04-06"),
("2733","444","حاضر","2023-04-06"),
("2734","447","حاضر","2023-04-06"),
("2735","464","حاضر","2023-04-06"),
("2736","452","حاضر","2023-04-06"),
("2737","467","حاضر","2023-04-06"),
("2738","470","حاضر","2023-04-06");
INSERT INTO stat VALUES
("2739","495","حاضر","2023-04-06"),
("2740","496","حاضر","2023-04-06"),
("2741","952","حاضر","2023-04-06"),
("2742","1071","حاضر","2023-04-06"),
("2743","1058","حاضر","2023-04-06"),
("2744","961","حاضر","2023-04-06"),
("2745","1031","حاضر","2023-04-06"),
("2746","950","حاضر","2023-04-06"),
("2747","437","حاضر","2023-04-06"),
("2748","446","حاضر","2023-04-06"),
("2749","493","حاضر","2023-04-06"),
("2750","468","حاضر","2023-04-06"),
("2751","919","حاضر","2023-04-06"),
("2752","463","حاضر","2023-04-06"),
("2753","434","حاضر","2023-04-06"),
("2754","443","حاضر","2023-04-06"),
("2755","439","حاضر","2023-04-06"),
("2756","457","حاضر","2023-04-06"),
("2757","475","حاضر","2023-04-06"),
("2758","469","حاضر","2023-04-06"),
("2759","1118","حاضر","2023-04-06"),
("2760","1061","حاضر","2023-04-06"),
("2761","1034","حاضر","2023-04-06"),
("2762","1001","حاضر","2023-04-06"),
("2763","476","حاضر","2023-04-06"),
("2764","480","حاضر","2023-04-06"),
("2765","490","حاضر","2023-04-06"),
("2766","492","حاضر","2023-04-06"),
("2767","494","حاضر","2023-04-06"),
("2768","445","حاضر","2023-04-06"),
("2769","453","حاضر","2023-04-06"),
("2770","455","حاضر","2023-04-06"),
("2771","458","حاضر","2023-04-06"),
("2772","461","حاضر","2023-04-06"),
("2773","484","حاضر","2023-04-06"),
("2774","491","حاضر","2023-04-06"),
("2775","946","حاضر","2023-04-06"),
("2776","953","حاضر","2023-04-06"),
("2777","346","حاضر","2023-04-09"),
("2778","356","حاضر","2023-04-09"),
("2779","386","حاضر","2023-04-09"),
("2780","387","حاضر","2023-04-09"),
("2781","391","حاضر","2023-04-09"),
("2782","390","حاضر","2023-04-09"),
("2783","395","حاضر","2023-04-09"),
("2784","396","حاضر","2023-04-09"),
("2785","397","حاضر","2023-04-09"),
("2786","400","حاضر","2023-04-09"),
("2787","421","حاضر","2023-04-09"),
("2788","874","حاضر","2023-04-09"),
("2789","876","حاضر","2023-04-09"),
("2790","877","حاضر","2023-04-09"),
("2791","914","حاضر","2023-04-09"),
("2792","1053","حاضر","2023-04-09"),
("2793","1084","حاضر","2023-04-09"),
("2794","1116","حاضر","2023-04-09"),
("2795","334","حاضر","2023-04-09"),
("2796","359","حاضر","2023-04-09"),
("2797","361","حاضر","2023-04-09"),
("2798","362","حاضر","2023-04-09"),
("2799","366","حاضر","2023-04-09"),
("2800","375","حاضر","2023-04-09"),
("2801","380","حاضر","2023-04-09"),
("2802","381","حاضر","2023-04-09"),
("2803","382","حاضر","2023-04-09"),
("2804","384","حاضر","2023-04-09"),
("2805","393","حاضر","2023-04-09"),
("2806","401","حاضر","2023-04-09"),
("2807","404","حاضر","2023-04-09"),
("2808","413","حاضر","2023-04-09"),
("2809","411","حاضر","2023-04-09"),
("2810","420","حاضر","2023-04-09"),
("2811","423","حاضر","2023-04-09"),
("2812","428","حاضر","2023-04-09"),
("2813","816","حاضر","2023-04-09"),
("2814","817","حاضر","2023-04-09"),
("2815","873","حاضر","2023-04-09"),
("2816","915","حاضر","2023-04-09"),
("2817","1046","حاضر","2023-04-09"),
("2818","1075","حاضر","2023-04-09"),
("2819","1083","حاضر","2023-04-09"),
("2820","1095","حاضر","2023-04-09"),
("2821","339","حاضر","2023-04-09"),
("2822","352","حاضر","2023-04-09"),
("2823","360","حاضر","2023-04-09"),
("2824","365","حاضر","2023-04-09"),
("2825","369","حاضر","2023-04-09"),
("2826","370","حاضر","2023-04-09"),
("2827","371","حاضر","2023-04-09"),
("2828","376","حاضر","2023-04-09"),
("2829","385","حاضر","2023-04-09"),
("2830","392","حاضر","2023-04-09"),
("2831","394","حاضر","2023-04-09"),
("2832","414","حاضر","2023-04-09"),
("2833","415","حاضر","2023-04-09"),
("2834","419","حاضر","2023-04-09"),
("2835","422","حاضر","2023-04-09"),
("2836","875","حاضر","2023-04-09"),
("2837","917","حاضر","2023-04-09"),
("2838","918","حاضر","2023-04-09");
INSERT INTO stat VALUES
("2839","1112","حاضر","2023-04-09"),
("2840","1129","حاضر","2023-04-09");




CREATE TABLE `stud_pay` (
  `id_P` int(100) NOT NULL AUTO_INCREMENT,
  `id_pay` varchar(250) NOT NULL,
  `cash_stud` float NOT NULL,
  `date_exp` date NOT NULL,
  `id_stud` int(11) NOT NULL,
  PRIMARY KEY (`id_P`),
  KEY `id_stud` (`id_stud`),
  CONSTRAINT `stud_pay_ibfk_1` FOREIGN KEY (`id_stud`) REFERENCES `stud_tb` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1129 DEFAULT CHARSET=utf8mb4;


INSERT INTO stud_pay VALUES
("305","670","75000","2023-05-09","334"),
("306","563","100000","2023-04-14","335"),
("308","574","75000","2023-04-19","337"),
("309","575","75000","2023-04-19","338"),
("310","585","100000","2023-04-18","339"),
("311","550","100000","2023-04-11","340"),
("312","477","75000","2023-03-24","341"),
("313","478","75000","2023-03-24","342"),
("314","566","100000","2023-04-15","343"),
("317","639","75000","2023-05-22","346"),
("318","640","75000","2023-05-02","347"),
("321","495","75000","2023-03-29","350"),
("322","580","100000","2023-04-19","351"),
("323","591","100000","2023-04-19","352"),
("327","583","100000","2023-04-18","356"),
("328","578","100000","2023-04-18","357"),
("330","620","75000","2023-04-20","359"),
("331","621","75000","2023-04-20","360"),
("332","622","50000","2023-04-20","361"),
("333","623","50000","2023-04-20","362"),
("334","629","100000","2023-04-28","363"),
("335","618","75000","2023-04-27","364"),
("336","619","75000","2023-04-27","365"),
("337","579","100000","2023-04-18","366"),
("338","602","75000","2023-04-22","367"),
("339","603","75000","2023-04-22","368"),
("340","601","100000","2023-04-21","369"),
("341","557","75000","2023-04-11","370"),
("342","558","75000","2023-04-11","371"),
("344","587","100000","2023-04-21","373"),
("346","564","100000","2023-04-14","375"),
("347","604","100000","2023-04-25","376"),
("348","467","75000","2023-03-24","377"),
("349","466","75000","2023-03-24","378"),
("351","597","75000","2023-04-19","380"),
("352","596","75000","2023-04-19","381"),
("353","513","100000","2023-04-21","382"),
("355","657","100000","2023-05-01","384"),
("356","600","100000","2023-04-25","385"),
("357","599","100000","2023-04-25","386"),
("358","610","100000","2023-04-25","387"),
("359","511","100000","2023-03-31","388"),
("360","633","75000","2023-05-02","389"),
("361","634","75000","2023-05-02","390"),
("362","630","75000","2023-04-29","391"),
("363","631","75000","2023-04-29","392"),
("364","669","100000","2023-05-02","393"),
("365","654","75000","2023-05-01","394"),
("366","655","75000","2023-05-01","395"),
("367","483","75000","2023-04-21","396"),
("368","643","100000","2023-05-02","397"),
("369","537","100000","2023-04-05","398"),
("371","542","100000","2023-04-21","400"),
("372","652","100000","2023-05-05","401"),
("374","611","100000","2023-05-05","403"),
("375","641","100000","2023-05-02","404"),
("376","536","100000","2023-04-05","405"),
("377","611","100000","2023-05-05","406"),
("378","546","100000","2023-04-21","407"),
("379","524","75000","2023-03-31","408"),
("380","525","75000","2023-03-31","409"),
("381","526","75000","2023-03-31","410"),
("382","646","100000","2023-05-02","411"),
("383","613","100000","2023-05-01","412"),
("384","648","100000","2023-05-02","413"),
("385","673","75000","2023-05-06","414"),
("386","672","75000","2023-05-06","415"),
("388","665","75000","2023-05-06","417"),
("389","666","75000","2023-05-06","418"),
("390","667","100000","2023-05-06","419"),
("391","650","100000","2023-05-05","420"),
("392","674","75000","2023-05-09","421"),
("393","675","75000","2023-05-09","422"),
("394","642","100000","2023-05-02","423"),
("395","552","100000","2023-04-10","424"),
("397","607","100000","2023-04-09","426"),
("398","551","100000","2023-04-09","427"),
("399","553","100000","2023-04-12","428"),
("400","554","100000","2023-04-11","429"),
("403","556","100000","2023-04-11","432"),
("404","292","100000","2023-05-02","433"),
("405","259","100000","2023-04-25","434"),
("406","258","100000","2023-04-25","435"),
("407","214","75000","2023-04-04","436"),
("408","245","50000","2023-04-19","437"),
("409","250","100000","2023-04-21","438"),
("410","254","100000","2023-04-22","439"),
("412","202","75000","2023-04-04","441"),
("413","264","100000","2023-04-25","442"),
("414","276","75000","2023-04-28","443"),
("415","251","100000","2023-04-22","444"),
("416","256","100000","2023-04-25","445"),
("417","288","100000","2023-05-02","446"),
("418","268","100000","2023-04-25","447"),
("419","100","100000","2023-02-24","448"),
("421","101","75000","2023-02-23","450"),
("422","215","100000","2023-04-05","451"),
("423","280","100000","2023-04-29","452"),
("424","274","100000","2023-04-27","453"),
("425","216","100000","2023-04-05","454");
INSERT INTO stud_pay VALUES
("426","265","100000","2023-04-25","455"),
("427","263","75000","2023-04-25","456"),
("428","263","75000","2023-04-25","457"),
("429","293","100000","2023-05-03","458"),
("430","197","100000","2023-03-31","459"),
("431","180","100000","2023-03-28","460"),
("432","302","100000","2023-05-05","461"),
("434","275","100000","2023-04-27","463"),
("435","224","100000","2023-04-11","464"),
("436","217","75000","2023-04-05","465"),
("437","217","75000","2023-04-05","466"),
("438","305","100000","2023-05-06","467"),
("439","281","75000","2023-04-29","468"),
("440","281","75000","2023-04-29","469"),
("441","299","100000","2023-05-04","470"),
("442","218","100000","2023-04-05","471"),
("443","205","100000","2023-04-04","472"),
("445","225","100000","2023-04-11","474"),
("446","226","100000","2023-04-11","475"),
("447","304","100000","2023-05-05","476"),
("448","238","100000","2023-04-18","477"),
("449","219","100000","2023-04-05","478"),
("450","286","75000","2023-05-02","479"),
("451","286","75000","2023-05-02","480"),
("452","204","100000","2023-04-05","481"),
("453","298","100000","2023-05-04","482"),
("454","303","100000","2023-05-05","483"),
("455","301","100000","2023-05-05","484"),
("456","220","75000","2023-04-05","485"),
("457","220","75000","2023-04-05","486"),
("458","220","75000","2023-04-05","487"),
("460","223","100000","2023-04-11","489"),
("461","291","100000","2023-05-02","490"),
("462","242","100000","2023-04-18","491"),
("463","227","75000","2023-04-11","492"),
("464","227","75000","2023-04-11","493"),
("465","244","100000","2023-04-19","494"),
("466","233","100000","2023-04-11","495"),
("467","229","100000","2023-04-11","496"),
("468","81","100000","2023-04-18","497"),
("472","76","100000","2023-04-17","501"),
("474","75","100000","2023-04-18","503"),
("477","91","100000","2023-04-18","506"),
("480","90","100000","2023-04-18","509"),
("483","92","75000","2023-04-24","512"),
("484","95","75000","2023-04-23","513"),
("485","101","100000","2023-04-24","514"),
("486","95","75000","2023-04-23","515"),
("489","67","75000","2023-04-04","518"),
("490","67","75000","2023-04-04","519"),
("491","54","100000","2023-03-31","520"),
("493","103","100000","2023-04-27","522"),
("494","59","100000","2023-04-04","523"),
("495","112","100000","2023-05-01","524"),
("496","110","100000","2023-05-02","525"),
("497","56","100000","2023-04-06","526"),
("499","58","75000","2023-04-07","528"),
("505","60","75000","2023-04-05","534"),
("506","643","100000","2023-04-18","535"),
("507","653","75000","2023-04-19","536"),
("508","653","75000","2023-04-19","537"),
("509","630","75000","2023-04-10","538"),
("510","630","75000","2023-04-10","539"),
("511","626","100000","2023-04-11","540"),
("512","651","75000","2023-04-16","541"),
("513","651","75000","2023-04-16","542"),
("514","446","100000","2023-04-18","543"),
("515","644","100000","2023-04-19","544"),
("516","642","100000","2023-04-19","545"),
("517","692","100000","2023-05-05","546"),
("518","645","100000","2023-04-15","547"),
("519","658","100000","2023-04-21","548"),
("520","663","100000","2023-04-25","549"),
("521","649","100000","2023-04-20","550"),
("522","659","100000","2023-04-18","551"),
("523","667","75000","2023-04-19","552"),
("524","647","75000","2023-04-19","553"),
("525","673","100000","2023-04-21","554"),
("526","669","100000","2023-04-22","555"),
("532","665","100000","2023-04-22","561"),
("533","616","100000","2023-03-31","562"),
("534","684","100000","2023-05-02","563"),
("535","666","100000","2023-04-25","564"),
("537","600","75000","2023-03-31","566"),
("538","600","75000","2023-03-31","567"),
("539","675","100000","2023-04-21","568"),
("540","676","100000","2023-05-02","569"),
("542","683","100000","2023-05-01","571"),
("543","593","100000","2023-03-31","572"),
("544","619","100000","2023-04-01","573"),
("546","592","75000","2023-03-31","575"),
("547","592","75000","2023-03-31","576"),
("548","681","75000","2023-05-01","577"),
("549","781","75000","2023-05-01","578"),
("550","696","0","2023-05-07","579"),
("551","689","75000","2023-05-01","580"),
("552","685","75000","2023-05-01","581"),
("553","685","75000","2023-05-01","582"),
("554","680","100000","2023-05-01","583"),
("555","672","100000","2023-05-02","584");
INSERT INTO stud_pay VALUES
("557","688","75000","2023-05-04","586"),
("558","688","75000","2023-05-04","587"),
("559","682","75000","2023-05-03","588"),
("560","682","75000","2023-05-03","589"),
("561","617","75000","2023-04-05","590"),
("562","617","75000","2023-04-05","591"),
("563","691","75000","2023-05-05","592"),
("564","691","75000","2023-05-05","593"),
("565","632","100000","2023-04-11","594"),
("566","629","75000","2023-04-08","595"),
("567","629","75000","2023-04-08","596"),
("569","692","100000","2023-05-07","598"),
("570","629","75000","2023-04-07","599"),
("571","629","75000","2023-04-07","600"),
("572","628","100000","2023-04-07","601"),
("573","636","75000","2023-04-18","602"),
("574","636","75000","2023-04-18","603"),
("576","661","75000","2023-04-19","605"),
("577","661","75000","2023-04-19","606"),
("578","596","75000","2023-04-19","607"),
("579","639","75000","2023-04-19","608"),
("580","637","100000","2023-04-17","609"),
("581","631","75000","2023-04-07","610"),
("582","631","75000","2023-04-07","611"),
("583","656","100000","2023-04-19","612"),
("584","588","75000","2023-04-19","613"),
("585","657","75000","2023-04-19","614"),
("588","652","100000","2023-04-17","617"),
("589","317","100000","2023-04-11","618"),
("591","224","75000","2023-04-09","620"),
("592","324","75000","2023-04-09","621"),
("593","328","100000","2023-04-11","622"),
("597","327","75000","2023-04-10","626"),
("598","327","75000","2023-04-10","627"),
("599","320","100000","2023-04-07","628"),
("600","326","100000","2023-04-11","629"),
("601","264","75000","2023-03-29","630"),
("602","367","75000","2023-04-28","631"),
("603","344","100000","2023-04-18","632"),
("604","310","100000","2023-04-04","633"),
("605","399","100000","2023-05-09","634"),
("606","330","100000","2023-04-13","635"),
("607","332","75000","2023-04-14","636"),
("608","332","75000","2023-04-14","637"),
("609","162","100000","2023-02-15","638"),
("611","336","100000","2023-04-14","640"),
("614","293","100000","2023-04-04","643"),
("617","264","75000","2023-04-26","646"),
("618","283","100000","2023-04-04","647"),
("619","371","75000","2023-04-27","648"),
("620","269","75000","2023-04-21","649"),
("621","300","75000","2023-04-05","650"),
("622","300","75000","2023-04-05","651"),
("624","354","100000","2023-04-13","653"),
("625","617","100","2023-04-11","654"),
("628","625","75000","2023-04-12","657"),
("629","624","75000","2023-04-12","658"),
("630","629","100","2023-04-20","659"),
("631","642","100","2023-04-18","660"),
("632","632","75000","2023-04-18","661"),
("633","627","100","2023-04-17","662"),
("634","635","100","2023-04-18","663"),
("635","639","100","2023-04-22","664"),
("638","596","100","2023-03-31","667"),
("639","643","75000","2023-04-20","668"),
("640","645","75000","2023-04-20","669"),
("641","644","75000","2023-04-20","670"),
("645","637","75000","2023-04-22","674"),
("646","636","75000","2023-04-22","675"),
("647","641","100","2023-04-19","676"),
("648","609","100","2023-04-04","677"),
("649","551","100","2023-03-19","678"),
("650","651","100","2023-04-22","679"),
("652","633","75000","2023-05-01","681"),
("653","586","100","2023-03-25","682"),
("654","652","75000","2023-05-01","683"),
("655","653","75000","2023-05-01","684"),
("656","649","75000","2023-04-27","685"),
("657","650","75000","2023-04-27","686"),
("659","655","75000","2023-04-23","688"),
("660","656","75000","2023-04-23","689"),
("661","657","75000","2023-05-01","690"),
("662","658","75000","2023-05-01","691"),
("665","584","75000","2023-03-31","694"),
("666","585","75000","2023-03-31","695"),
("667","603","75000","2023-03-31","696"),
("668","604","75000","2023-04-03","697"),
("669","605","75000","2023-04-04","698"),
("670","606","75000","2023-04-04","699"),
("671","601","75000","2023-04-04","700"),
("672","602","75000","2023-04-04","701"),
("673","610","100","2023-04-03","702"),
("674","607","75000","2023-04-07","703"),
("675","608","75000","2023-04-07","704"),
("676","612","75000","2023-04-07","705"),
("677","613","75000","2023-04-07","706"),
("679","611","100","2023-04-09","708"),
("680","614","75000","2023-04-08","709"),
("681","614","75000","2023-04-08","710"),
("682","618","75000","2023-04-12","711");
INSERT INTO stud_pay VALUES
("683","619","75000","2023-04-12","712"),
("684","622","75000","2023-04-12","713"),
("685","623","75000","2023-04-12","714"),
("686","66","100000","2023-04-12","715"),
("687","64","75000","2023-04-12","716"),
("688","65","75000","2023-04-12","717"),
("690","86","100000","2023-04-17","719"),
("691","81","100000","2023-04-18","720"),
("692","85","100000","2023-04-18","721"),
("693","91","100000","2023-04-18","722"),
("700","76","75000","2923-04-18","729"),
("701","75","75000","2023-04-18","730"),
("702","104","100000","2023-04-21","731"),
("703","74","75000","2023-04-18","732"),
("704","000","0","2023-04-18","733"),
("705","68","100000","2023-04-18","734"),
("707","88","100000","2023-04-18","736"),
("709","70","100000","2023-04-18","738"),
("710","101","100000","2023-04-18","739"),
("711","79","100000","2023-04-20","740"),
("712","72","75000","2023-04-17","741"),
("713","71","75000","2023-04-17","742"),
("714","73","100000","2023-04-18","743"),
("715","98","100000","2023-04-19","744"),
("716","80","100000","2023-04-19","745"),
("719","97","100000","2023-04-27","748"),
("720","82","100000","2023-04-25","749"),
("721","84","75000","2023-04-22","750"),
("722","83","75000","2023-04-22","751"),
("727","78","75000","2023-04-18","756"),
("728","77","75000","2023-04-18","757"),
("729","109","100000","2023-05-02","758"),
("730","94","100000","2023-04-25","759"),
("733","112","75000","2023-05-02","762"),
("734","111","75000","2023-05-02","763"),
("736","93","100000","2023-04-25","765"),
("738","105","100000","2023-05-05","767"),
("740","110","100000","2023-05-01","769"),
("741","116","100000","2023-05-02","770"),
("742","124","100000","2023-05-05","771"),
("743","99","75000","2023-05-02","772"),
("744","100","75000","2023-05-02","773"),
("745","33","100000","2023-04-04","774"),
("746","122","100000","2023-05-04","775"),
("747","115","100000","2023-05-02","776"),
("748","120","75000","2023-05-06","777"),
("749","121","75000","2023-05-06","778"),
("751","117","100000","2023-05-02","780"),
("753","125","75000","2023-05-07","782"),
("754","126","75000","2023-05-07","783"),
("755","127","75000","2023-05-09","784"),
("756","128","75000","2023-05-09","785"),
("759","56","100000","2023-04-07","788"),
("761","54","75000","2023-04-09","790"),
("762","55","75000","2023-04-09","791"),
("763","60","100000","2023-04-10","792"),
("764","62","50000","2023-04-11","793"),
("765","61","50000","2023-04-11","794"),
("767","57","100000","2023-04-08","796"),
("768","50","100000","2023-04-14","797"),
("769","49","100000","2023-04-23","798"),
("771","296","100000","2023-04-04","800"),
("773","620","75000","2023-04-12","802"),
("774","621","75000","2023-04-12","803"),
("775","628","100","2023-04-18","804"),
("776","638","100","2023-04-18","805"),
("777","557","100","2023-03-21","806"),
("779","654","100","2023-05-01","808"),
("780","573","75000","2023-03-28","809"),
("781","633","50000","2023-04-07","810"),
("782","560","560","2023-03-10","811"),
("783","664","100000","2023-04-23","812"),
("784","660","75000","2023-04-18","813"),
("785","660","75000","2023-04-18","814"),
("787","416","100000","2023-04-27","816"),
("788","627","100000","2023-04-28","817"),
("789","694","100000","2023-05-09","818"),
("790","569","100","2023-03-21","819"),
("791","687","100000","2023-05-05","820"),
("792","667","75000","2023-04-18","821"),
("793","667","75000","2023-04-18","822"),
("794","576","100","2023-03-29","823"),
("795","577","100","2023-03-29","824"),
("796","578","100000","2023-03-21","825"),
("798","581","100","2023-03-31","827"),
("799","634","75000","2023-05-01","828"),
("800","58","75000","2023-04-07","829"),
("805","60","75000","2023-04-05","834"),
("806","62","100000","2023-04-05","835"),
("807","68","100000","2023-04-08","836"),
("808","70","100000","2023-04-11","837"),
("811","83","100000","2023-04-14","840"),
("812","95","100000","2023-04-18","841"),
("813","87","100000","2023-04-26","842"),
("814","96","100000","2023-04-25","843"),
("815","89","100000","2023-04-25","844"),
("819","92","100000","2023-04-25","848"),
("820","309","75000","2023-04-06","849"),
("821","309","75000","2023-04-06","850"),
("822","364","100000","2023-04-28","851");
INSERT INTO stud_pay VALUES
("823","352","100000","2023-04-27","852"),
("826","360","100000","2023-04-28","855"),
("827","274","75000","2023-03-27","856"),
("829","287","100000","2023-04-04","858"),
("830","271","100000","2023-03-30","859"),
("831","276","75000","2023-04-27","860"),
("832","276","75000","2023-04-27","861"),
("833","272","100000","2023-03-30","862"),
("834","270","100000","2023-03-30","863"),
("835","329","100000","2023-04-14","864"),
("836","301","75000","2023-04-01","865"),
("837","301","75000","2023-04-01","866"),
("838","304","100000","2023-03-31","867"),
("839","275","75000","2023-03-31","868"),
("840","275","75000","2023-03-31","869"),
("841","334","75000","2023-04-14","870"),
("842","334","75000","2023-04-14","871"),
("843","245","50000","2023-04-19","872"),
("844","635","75000","2023-04-26","873"),
("845","636","75000","2023-04-26","874"),
("846","637","75000","2023-04-26","875"),
("847","615","100000","2023-04-27","876"),
("848","510","100000","2023-04-11","877"),
("850","109","100000","2023-05-02","879"),
("851","115","50000","2023-05-02","880"),
("852","115","50000","2023-05-02","881"),
("853","628","100000","2023-04-19","882"),
("855","94","100000","2023-04-18","884"),
("856","97","100000","2023-04-22","885"),
("857","92","75000","2023-04-24","886"),
("858","99","75000","2023-04-25","887"),
("859","99","75000","2023-04-25","888"),
("860","108","100000","2023-04-25","889"),
("861","37","100000","2023-03-28","890"),
("862","100","75000","2023-04-25","891"),
("863","100","75000","2023-04-25","892"),
("864","41","100000","2023-03-28","893"),
("865","42","100000","2023-03-28","894"),
("866","43","100000","2023-03-27","895"),
("867","44","100000","2023-03-29","896"),
("868","107","100000","2023-04-26","897"),
("869","109","100000","2023-05-02","898"),
("870","98","100000","2023-04-22","899"),
("871","49","100000","2023-03-31","900"),
("872","52","50000","2023-03-28","901"),
("873","52","500000","2023-03-28","902"),
("874","53","75000","2023-04-01","903"),
("875","53","75000","2023-04-01","904"),
("876","285","100000","2023-04-04","905"),
("877","650","100000","2023-04-18","906"),
("878","648","100000","2023-04-21","907"),
("880","668","100000","2023-04-25","909"),
("881","690","100000","2023-04-27","910"),
("883","277","75000","2023-03-31","912"),
("884","527","100000","2023-04-04","913"),
("885","668","100000","2023-05-06","914"),
("886","656","100000","2023-05-04","915"),
("888","569","75000","2023-04-18","917"),
("889","570","75000","2023-04-18","918"),
("890","227","75000","2023-04-11","919"),
("891","214","75000","2023-04-04","920"),
("892","303","100000","2023-04-05","921"),
("893","298","100000","2023-04-01","922"),
("895","298","75000","2023-04-01","924"),
("896","299","75000","2023-04-04","925"),
("897","299","75000","2023-04-04","926"),
("898","312","75000","2023-04-07","927"),
("899","321","75000","2023-04-09","928"),
("900","284","100000","2023-04-04","929"),
("901","41","100000","2023-04-05","930"),
("903","39","100000","2023-04-05","932"),
("904","587","100","2023-03-31","933"),
("905","588","100","2023-04-01","934"),
("906","659","100","2023-05-02","935"),
("907","40","100000","2023-04-05","936"),
("908","594","100","2023-04-04","937"),
("909","595","100","2023-04-04","938"),
("910","597","100","2023-04-04","939"),
("911","598","100","2023-04-05","940"),
("912","599","75000","2023-03-31","941"),
("913","600","75000","2023-03-31","942"),
("914","237","75000","2023-04-13","943"),
("915","237","75000","2023-04-13","944"),
("916","232","100000","2023-04-11","945"),
("917","235","100000","2023-04-14","946"),
("918","146","100000","2023-04-18","947"),
("919","236","75000","2023-04-14","948"),
("920","236","75000","2023-04-14","949"),
("921","247","100000","2023-04-14","950"),
("922","260","75000","2023-04-25","951"),
("923","260","75000","2023-04-25","952"),
("924","261","100000","2023-04-25","953"),
("925","252","75000","2023-04-19","954"),
("926","252","75000","2023-04-19","955"),
("927","262","100000","2023-04-25","956"),
("928","305","100000","2023-05-06","957"),
("929","245","50000","2023-04-19","958"),
("930","241","100000","2023-04-18","959"),
("931","152","100000","2023-03-21","960"),
("932","248","100000","2023-04-21","961");
INSERT INTO stud_pay VALUES
("935","543","100000","2023-04-06","964"),
("936","544","75000","2023-04-06","965"),
("937","545","75000","2023-04-06","966"),
("939","622","100000","2023-04-04","968"),
("940","620","100000","2023-04-04","969"),
("941","52","100000","2023-04-07","970"),
("942","293","100000","2023-04-04","971"),
("943","208","100000","2023-06-01","972"),
("944","209","75000","2023-06-01","973"),
("945","210","75000","2023-07-02","974"),
("946","210","75000","2023-07-02","975"),
("947","211","100000","2023-07-02","976"),
("948","212","100000","2023-07-02","977"),
("949","213","50000","2023-08-01","978"),
("950","214","75000","2023-09-01","979"),
("951","214","75000","2023-09-01","980"),
("952","215","100000","2023-08-01","981"),
("953","216","100000","2023-08-01","982"),
("954","217","100000","2023-09-01","983"),
("955","218","100000","2023-09-01","984"),
("956","218","75000","2023-09-01","985"),
("957","219","75000","2023-09-01","986"),
("958","220","75000","2023-06-01","987"),
("959","220","75000","2023-06-01","988"),
("960","221","75000","2023-08-01","989"),
("961","66","100000","2023-04-04","990"),
("962","65","100000","2023-04-04","991"),
("963","64","100000","2023-04-06","992"),
("964","61","100000","2023-04-04","993"),
("965","55","100000","2023-04-04","994"),
("966","58","75000","2023-04-07","995"),
("967","63","100000","2023-04-11","996"),
("969","222","75000","2023-08-01","998"),
("970","222","75000","2023-08-01","999"),
("971","223","75000","2023-08-01","1000"),
("972","231","100000","2023-04-11","1001"),
("973","230","75000","2023-04-11","1002"),
("974","230","75000","2023-04-11","1003"),
("975","223","75000","2023-08-01","1004"),
("976","224","75000","2023-08-01","1005"),
("977","226","100000","2023-08-01","1006"),
("978","229","100000","2023-10-02","1007"),
("979","227","75000","2023-04-10","1008"),
("980","230","75000","2023-04-10","1009"),
("981","206","75000","2023-09-01","1010"),
("982","302","100000","2023-04-05","1011"),
("983","291","100000","2023-04-04","1012"),
("984","282","100000","2023-04-04","1013"),
("985","316","100000","2023-04-07","1014"),
("986","322","100000","2023-04-11","1015"),
("987","314","500000","2023-04-08","1016"),
("988","311","75000","2023-04-06","1017"),
("989","311","75000","2023-04-07","1018"),
("990","695","75000","2023-05-09","1019"),
("991","696","75000","2023-05-09","1020"),
("992","695","75000","2023-05-09","1021"),
("993","19","75000","2023-04-27","1022"),
("994","20","75000","2023-04-27","1023"),
("995","21","100000","2023-04-25","1024"),
("996","22","100000","2023-05-01","1025"),
("998","34","100000","2023-04-04","1027"),
("999","222","100000","2023-04-05","1028"),
("1000","217","75000","2023-04-05","1029"),
("1001","213","100000","2023-04-04","1030"),
("1002","279","100000","2023-05-04","1031"),
("1003","208","75000","2023-04-04","1032"),
("1004","208","75000","2023-04-04","1033"),
("1005","289","100000","2023-05-06","1034"),
("1006","189","75000","2023-03-30","1035"),
("1007","190","100000","2023-03-30","1036"),
("1008","191","100000","2023-03-30","1037"),
("1009","192","100000","2023-03-30","1038"),
("1010","284","100000","2023-05-02","1039"),
("1011","198","100000","2023-03-31","1040"),
("1012","199","100000","2023-03-31","1041"),
("1013","201","100000","2023-04-01","1042"),
("1014","206","100000","2023-04-04","1043"),
("1015","616","100","2023-04-11","1044"),
("1016","626","100","2023-04-13","1045"),
("1017","560","100000","2023-04-13","1046"),
("1018","561","75000","2023-04-13","1047"),
("1019","652","75000","2023-04-13","1048"),
("1020","559","100000","2023-04-18","1049"),
("1021","634","75000","2023-05-02","1050"),
("1022","608","100000","2023-04-25","1051"),
("1023","662","75000","2023-05-05","1052"),
("1024","661","100000","2023-05-05","1053"),
("1025","592","100000","2023-04-18","1054"),
("1026","465","100000","2023-03-22","1055"),
("1027","294","75000","2023-05-03","1056"),
("1028","184","100000","2023-03-29","1057"),
("1029","278","100000","2023-04-28","1058"),
("1030","179","100000","2023-03-28","1059"),
("1031","270","100000","2023-04-28","1060"),
("1032","271","100000","2023-04-27","1061"),
("1033","271","75000","2023-04-27","1062"),
("1034","171","50000","2023-03-28","1063"),
("1035","277","100000","2023-04-29","1064"),
("1037","257","100000","2023-04-25","1066"),
("1038","269","100000","2023-04-27","1067");
INSERT INTO stud_pay VALUES
("1039","266","75000","2023-04-25","1068"),
("1040","266","75000","2023-04-25","1069"),
("1041","243","75000","2023-04-18","1070"),
("1042","243","75000","2023-04-18","1071"),
("1043","234","100000","2023-04-14","1072"),
("1044","634","100000","2023-04-13","1073"),
("1045","565","100000","2023-04-14","1074"),
("1046","555","100000","2023-04-12","1075"),
("1047","240","100000","2023-04-18","1076"),
("1048","329","100000","2023-04-14","1077"),
("1049","340","100000","2023-04-18","1078"),
("1050","630","75000","2023-04-19","1079"),
("1051","631","75000","2023-04-19","1080"),
("1052","69","100000","2023-04-18","1081"),
("1053","345","75000","2023-04-18","1082"),
("1054","577","100000","2023-04-18","1083"),
("1055","586","100000","2023-04-21","1084"),
("1056","576","100000","2023-04-18","1085"),
("1057","571","100000","2023-04-18","1086"),
("1058","641","100000","2023-04-18","1087"),
("1059","640","100000","2023-04-18","1088"),
("1060","638","100000","2023-04-18","1089"),
("1061","93","100000","2023-04-22","1090"),
("1062","87","100000","2023-04-19","1091"),
("1063","77","100000","2023-04-18","1092"),
("1064","71","100000","2023-04-12","1093"),
("1065","640","100","2023-04-22","1094"),
("1066","598","100000","2023-04-25","1095"),
("1067","646","75000","2023-04-25","1096"),
("1068","647","75000","2023-04-25","1097"),
("1069","605","50000","2023-04-25","1098"),
("1070","648","100","2023-04-23","1099"),
("1071","267","75000","2023-04-27","1100"),
("1072","267","75000","2023-04-27","1101"),
("1073","335","75000","2023-04-14","1102"),
("1074","335","75000","2023-04-14","1103"),
("1075","335","75000","2023-04-14","1104"),
("1076","353","100000","2023-04-20","1105"),
("1077","274","75000","2023-04-24","1106"),
("1078","273","100000","2023-04-27","1107"),
("1079","90","100000","2023-04-18","1108"),
("1080","67","100000","2023-04-13","1109"),
("1081","625","75000","2023-04-28","1110"),
("1082","626","75000","2023-04-28","1111"),
("1083","614","100000","2023-04-27","1112"),
("1084","279","125000","2023-04-29","1113"),
("1085","660","75000","2023-05-02","1114"),
("1086","661","75000","2023-05-02","1115"),
("1087","638","100000","2023-05-02","1116"),
("1088","282","125000","2023-05-02","1117"),
("1089","283","100000","2023-05-02","1118"),
("1090","283","100000","2023-05-02","1119"),
("1091","644","75000","2023-05-02","1120"),
("1092","645","75000","2023-05-02","1121"),
("1093","111","100000","2023-05-02","1122"),
("1094","118","100000","2023-05-04","1123"),
("1095","114","100000","2023-05-03","1124"),
("1096","107","100000","2023-05-02","1125"),
("1097","119","100000","2023-05-03","1126"),
("1098","123","100000","2023-05-09","1127"),
("1099","663","75000","2023-05-05","1128"),
("1100","671","75000","2023-05-09","1129"),
("1101","393","100000","2023-05-05","1130"),
("1102","356","75000","2023-04-25","1131"),
("1103","356","75000","2023-04-25","1132"),
("1104","400","125000","2023-05-09","1133"),
("1105","371","75000","2023-04-27","1134"),
("1106","369","100000","2023-04-27","1135"),
("1107","346","100000","2023-04-11","1136"),
("1108","345","75000","2023-04-18","1137"),
("1109","345","75000","2023-04-18","1138"),
("1110","348","100000","2023-04-19","1139"),
("1111","341","100000","2023-04-18","1140"),
("1112","129","100000","2023-05-09","1141"),
("1113","689","100000","2023-05-05","1142"),
("1114","679","0","2023-05-01","1143"),
("1115","697","0","2023-05-01","1144"),
("1116","679","100000","2023-05-02","1145"),
("1117","678","100000","2023-05-02","1146"),
("1118","678","100000","2023-05-02","1147"),
("1119","647","75000","2023-04-29","1148"),
("1120","674","75000","2023-04-29","1149"),
("1121","677","100000","2023-05-02","1150"),
("1122","671","0","2023-04-22","1151"),
("1123","655","0","2023-04-22","1152"),
("1124","654","654","2023-04-22","1153"),
("1125","662","75000","2023-04-25","1154"),
("1126","662","75000","2023-04-25","1155"),
("1127","670","100000","2023-05-30","1156"),
("1128","686","100000","2023-05-01","1157");




CREATE TABLE `stud_tb` (
  `id` int(100) NOT NULL AUTO_INCREMENT,
  `id_note` int(100) NOT NULL,
  `userID` int(100) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8 NOT NULL,
  `age` varchar(100) NOT NULL,
  `sex` varchar(100) NOT NULL,
  `catg` varchar(100) NOT NULL,
  `datein` date NOT NULL,
  `p_name` varchar(250) NOT NULL,
  `p_phone` varchar(250) NOT NULL,
  `loc` varchar(250) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `userID` (`userID`),
  CONSTRAINT `stud_tb_ibfk_1` FOREIGN KEY (`userID`) REFERENCES `users_tb` (`id_user`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1158 DEFAULT CHARSET=utf8mb4;


INSERT INTO stud_tb VALUES
("334","1","6","فهد محمد","5","ذكر","تمهيدي","2023-04-09","محمد","00000000000","بلوك2"),
("335","2","6","سيف محمد وهاب","4","ذكر","روضة","2023-03-15","محمد وهاب","00000000000","بلوك2"),
("337","4","6","ايلا زيد","5","ذكر","تمهيدي","2023-03-20","زيد","00000000000","بلوك2"),
("338","5","6","عبدالله زيد","2","ذكر","حضانة","2023-03-20","زيد","00000000000","بلوك2"),
("339","6","6","حسن بلال ","1","ذكر","حضانة","2023-03-19","بلال","00000000000","بلوك2"),
("340","7","6","حسين علي اكبر","1","ذكر","روضة","2023-03-12","علي اكبر","00000000000","بلوك2"),
("341","8","6","حسن سرمد","1","ذكر","روضة","2023-02-22","سرمد","00000000000","بلوك2"),
("342","9","6","سيلين سرمد","4","انثى","روضة","2023-02-22","سرمد","00000000000","بلوك2"),
("343","10","6","لمار مرتضى ","1","انثى","روضة","2023-03-16","مرتضى","00000000000","بلوك 2"),
("346","13","6","الامير علي محمد","1","ذكر","روضة","2023-04-22","علي محمد","00000000000","بلوك2"),
("347","14","6","ادم محمد جواد","2","ذكر","روضة","2023-04-02","محمد جواد","00000000000","بلوك2"),
("350","17","6","ابراهيم عمار ","4","ذكر","روضة","2023-02-27","عمار","00000000000","بلوك2"),
("351","18","6","حسين محمد","5","ذكر","تمهيدي","2023-03-20","محمد","00000000000","بلوك2"),
("352","19","6","رقيه علي ","2","انثى","حضانة","2023-03-20","علي","00000000000","بلوك2"),
("356","23","6","احمد مصطفى ","1","ذكر","روضة","2023-03-19","مصطفى","00000000000","بلوك2"),
("357","24","6","يحى علاء ","5","ذكر","تمهيدي","2023-03-19","علاء","00000000000","بلوك2"),
("359","26","6","كرار احمد","5","ذكر","تمهيدي","2023-03-21","حسن","00000000000","بلوك2"),
("360","27","6","حيدر احمد","2","ذكر","حضانة","2023-03-21","احمد","00000000000","بلوك2"),
("361","28","6","سما احمد","6","انثى","تمهيدي","2023-03-21","احمد","00000000000","بلوك2"),
("362","29","6","جنى احمد","6","انثى","تمهيدي","2023-03-21","احمد","00000000000","بلوك2"),
("363","30","6","ميلا اسعد","3","انثى","حضانة","2023-03-29","اسعد","00000000000","بلوك2"),
("364","31","6","جود محمد","3","ذكر","روضة","2023-03-28","محمد","00000000000","بلوك2"),
("365","32","6","جمان محمد","4","انثى","حضانة","2023-03-28","محمد","00000000000","بلوك2"),
("366","33","6","ريحانة مصطفلى","5","انثى","تمهيدي","2023-03-19","مصطفى ","00000000000","بلوك2"),
("367","34","6","يمان ياسر","4","انثى","روضة","2023-03-23","ياسر","00000000000","بلوك2"),
("368","35","6","الياس ياسر","4","ذكر","روضة","2023-03-23","ياسر","00000000000","بلوك2"),
("369","36","6","نبا محمد","2","انثى","حضانة","2023-03-22","محمد","00000000000","بلوك2"),
("370","37","6","نرجس سجاد","3","انثى","حضانة","2023-03-12","سجاد","00000000000","بلوك2"),
("371","38","6","احمد سجاد ","2","ذكر","حضانة","2023-03-12","سجاد","00000000000","بلوك2"),
("373","40","6","ليا اياد هاشم","4","انثى","روضة","2023-03-22","اياد هاشم","00000000000","بلوك2"),
("375","42","6","احمد الرضا محمد","5","ذكر","تمهيدي","2023-03-15","الرضا محمد","00000000000","بلوك2"),
("376","43","6","الياس مصطفى","2","ذكر","حضانة","2023-03-26","مصطفى","00000000000","بلوك2"),
("377","44","6","نجات عبد الجبار ","2","انثى","حضانة","2023-02-22","عبد الجبار","00000000000","بلوك2"),
("378","45","6","زينب عبد الجبار","2","انثى","حضانة","2023-02-22","عبد الجبار","00000000000","بلوك2"),
("380","47","6","ليان عبد القادر","5","انثى","تمهيدي","2023-03-20","عبد القادر","00000000000","بلوك2"),
("381","48","6","اسد عبد القادر","5","ذكر","تمهيدي","2023-03-20","عبد القادر","00000000000","بلوك2"),
("382","49","6","صفا حيدر","5","انثى","تمهيدي","2023-03-22","حيدر","00000000000","بلوك2"),
("384","51","6","اسامه زيد","5","ذكر","تمهيدي","2023-04-01","زيد","00000000000","بلوك2"),
("385","52","6","ادم حيدر ","2","ذكر","حضانة","2023-03-26","حيدر","00000000000","بلوك2"),
("386","53","6","ايليا سيف صلاح","4","ذكر","روضة","2023-03-26","سيف صلاح","00000000000","بلوك2"),
("387","54","6","افين سلام ","4","انثى","روضة","2023-03-26","صلاح","00000000000","بلوك2"),
("388","55","6","علي كرار ","4","ذكر","روضة","2023-03-01","كرار","00000000000","بلوك2"),
("389","56","6","مسرة اسعد","4","انثى","روضة","2023-04-02","اسعد","00000000000","بلوك2"),
("390","57","6","منتضر اسعد","4","ذكر","روضة","2023-04-02","اسعد","00000000000","بلوك2"),
("391","58","6","رزان مهدي","4","انثى","روضة","2023-03-30","مهدي","00000000000","بلوك2"),
("392","59","6","علي مهدي","2","ذكر","حضانة","2023-03-30","مهدي","00000000000","بلوك2"),
("393","60","6","عباس مقداد","5","ذكر","تمهيدي","2023-04-02","مقداد","00000000000","بلوك2"),
("394","61","6","تميم حسين","2","ذكر","حضانة","2023-04-01","حسين","00000000000","بلوك2"),
("395","62","6","متين حسين","4","ذكر","روضة","2023-04-01","حسين","00000000000","بلوك2"),
("396","63","6","حسين خالد","4","ذكر","روضة","2023-03-22","خالد","00000000000","بلوك2"),
("397","64","6","علي حيدر ","4","ذكر","روضة","2023-04-02","حيدر","00000000000","بلوك2"),
("398","65","6","ايهم ضرغام","4","ذكر","روضة","2023-03-06","ضرغام","00000000000","بلوك2"),
("400","67","6","جنى محمد سلطان","4","انثى","روضة","2023-03-22","محمد سلطان","00000000000","بلوك2"),
("401","68","6","حسام صفاء","5","ذكر","تمهيدي","2023-04-05","صفاء","00000000000","بلوك2"),
("403","70","6","علي احمد نعمه","4","ذكر","روضة","2023-04-05","علي نعمة","00000000000","بلوك2"),
("404","71","6","ماس مهند ","5","انثى","تمهيدي","2023-04-02","مهند","00000000000","بلوك2"),
("405","72","6","حوراء مصعب","4","انثى","روضة","2023-03-06","مصعب","00000000000","بلوك2"),
("406","73","6","حسن علي عبد الحسين","5","ذكر","تمهيدي","2023-04-05","علي عبد الحسين","00000000000","بلوك2"),
("407","74","6","ادم علي ","5","ذكر","تمهيدي","2023-03-22","علي","00000000000","بلوك2"),
("408","75","6","شدن عبد الله","5","انثى","تمهيدي","2023-03-01","عبدالله","00000000000","بلوك2"),
("409","76","6","محمد عبدالله","2","ذكر","حضانة","2023-03-01","عبدالله","00000000000","بلوك2"),
("410","77","6","ميان عبد الله","2","انثى","حضانة","2023-03-01","عبدالله","00000000000","بلوك2"),
("411","78","6","علي نزار","5","ذكر","تمهيدي","2023-04-02","نزار","00000000000","بلوك2"),
("412","79","6","جود عمار","4","ذكر","روضة","2023-04-01","عمار","00000000000","بلوك2"),
("413","80","6","علي الرضا قاسم","5","ذكر","تمهيدي","2023-04-02","عبدالرضا قاسم","00000000000","بلوك2"),
("414","81","6","عباس اسامة","2","ذكر","حضانة","2023-04-06","اسامه","00000000000","بلوك2"),
("415","82","6","حمزه اسامة","2","ذكر","حضانة","2023-04-06","اسامة","00000000000","بلوك2"),
("417","84","6","در احمد","4","انثى","روضة","2023-04-06","احمد","00000000000","بلوك2"),
("418","85","6","قاسم احمد","2","ذكر","روضة","2023-04-06","احمد","00000000000","بلوك2"),
("419","86","6","زينب مصطفى جاسم","2","انثى","حضانة","2023-04-06","مصطفى جاسم","00000000000","بلوك2"),
("420","87","6","حسن علي ","5","ذكر","تمهيدي","2023-04-05","علي","00000000000","بلوك2"),
("421","88","6","انس علي ","4","ذكر","روضة","2023-04-09","علي","00000000000","بلوك2"),
("422","89","6","ماس علي","2","انثى","حضانة","2023-04-09","علي","00000000000","بلوك2"),
("423","90","6","كرار حيدر","5","ذكر","تمهيدي","2023-04-02","حيدر","00000000000","بلوك2"),
("424","91","6","ادم اوس","2","ذكر","حضانة","2023-03-11","اوس","00000000000","بلوك2"),
("426","93","6","قيصر محمد","2","ذكر","حضانة","2023-03-10","محمد","00000000000","بلوك2"),
("427","94","6","علي حسين","2","ذكر","روضة","2023-03-10","حسين","00000000000","بلوك2"),
("428","95","6","مهيمن صادق","5","ذكر","تمهيدي","2023-03-13","صادق","00000000000","بلوك2"),
("429","96","6","زين العابدين علي","2","ذكر","حضانة","2023-03-12","علي","00000000000","بلوك2"),
("432","99","6","لاريمار حيدر","4","انثى","روضة","2023-03-12","حيدر","00000000000","بلوك2"),
("433","100","7","علي صلاح","1","ذكر","تمهيدي","2023-04-02","صلاح","00000000000","بلوك3"),
("434","101","7","حسين علي عبد الجبار ","5","ذكر","تمهيدي","2023-03-26","علي","00000000000","بلوك3"),
("435","102","7","فهد حسين اياد","2","ذكر","تمهيدي","2023-03-26","حسين اياد","00000000000","بلوك3"),
("436","103","7","رهف محمود","4","انثى","روضة","2023-03-05","محمود","00000000000","بلوك3"),
("437","104","7","جود علي قاسم","2","ذكر","حضانة","2023-03-20","علي قاسم","00000000000","بلوك3"),
("438","105","7","محمد حسين حازم","4","ذكر","روضة","2023-03-22","حسين حازم","00000000000","بلوك3"),
("439","106","7","اساور محمد","1","انثى","تمهيدي","2023-03-23","محمد","00000000000","بلوك3"),
("441","108","7","ود احمد","2","انثى","حضانة","2023-03-05","احمد","00000000000","بلوك3"),
("442","109","7","كيان ضرغام","4","ذكر","روضة","2023-03-26","ضرغام","00000000000","بلوك3"),
("443","110","7","سيرين محمد محسن","5","انثى","تمهيدي","2023-03-29","محمد","00000000000","بلوك3"),
("444","111","7","محمد فأق","1","ذكر","روضة","2023-03-23","فاق","00000000000","بلوك3"),
("445","112","7","ديما محمد","-5","انثى","تمهيدي","2023-03-26","محمد","00000000000","بلوك3"),
("446","113","7","جمانه جاسم","2","انثى","حضانة","2023-04-02","جاسم","00000000000","بلوك3"),
("447","114","7","علي نورس","4","ذكر","روضة","2023-03-26","نورس","00000000000","بلوك3"),
("448","115","7","ايلين محمد ضياء","4","انثى","روضة","2023-01-25","محمد ضياء","00000000000","بلوك3"),
("450","117","7","ادم احمد شهاب ","1","ذكر","روضة","2023-01-24","احمد شهاب ","00000000000","بلوك3"),
("451","118","7","مسره خالد","4","انثى","روضة","2023-03-06","خالد","00000000000","بلوك3"),
("452","119","7","يوسف مهند","4","انثى","روضة","2023-03-30","مهند","00000000000","بلوك3"),
("453","120","7","زيد يحى طلال","2","ذكر","تحضيري","2023-03-28","يحى طلال","00000000000","بلوك3"),
("454","121","7","ليان مصطفى","4","انثى","روضة","2023-03-06","مصطفى ","00000000000","بلوك3");
INSERT INTO stud_tb VALUES
("455","122","7","فضل علي مؤيد","2","ذكر","تحضيري","2023-03-26","علي مؤيد","00000000000","بلوك3"),
("456","123","7","رضا نادر","-5","ذكر","تمهيدي","2023-03-26","نادر","00000000000","بلوك3"),
("457","124","7","تولين نادر","-5","انثى","تمهيدي","2023-03-26","نادر","00000000000","بلوك3"),
("458","125","7","علي لاكبر عمار","2","ذكر","تحضيري","2023-04-03","عمار","00000000000","بلوك3"),
("459","126","7","يوسف علي ستار","4","ذكر","روضة","2023-03-01","علي ستار","00000000000","بلوك3"),
("460","127","7","مصطفى حسن مجيد","2","ذكر","تحضيري","2023-02-26","حسن مجيد","00000000000","بلوك3"),
("461","128","7","عمار ياسر","2","ذكر","تحضيري","2023-04-05","ياسر","00000000000","بلوك3"),
("463","130","7","ياسمين محمد","2","انثى","حضانة","2023-03-28","محمد","00000000000","بلوك3"),
("464","131","7","جهاد حيدر","4","ذكر","روضة","2023-03-12","حيدر","00000000000","بلوك3"),
("465","132","7","علي صادق ","1","ذكر","روضة","2023-03-06","صادق","00000000000","بلوك3"),
("466","133","7","عباس صادق","1","ذكر","روضة","2023-03-06","صادق","00000000000","بلوك3"),
("467","134","7","جود طالب","1","ذكر","روضة","2023-04-06","طالب","00000000000","بلوك3"),
("468","135","7","محمد سيف الدين","2","ذكر","حضانة","2023-03-30","سيف الدين","00000000000","بلوك3"),
("469","136","7","جنه سيف الدين","5","انثى","تمهيدي","2023-03-30","سيف الدين","00000000000","بلوك3"),
("470","137","7","جعفر محمد فهد","4","ذكر","روضة","2023-04-04","محمد فهد","00000000000","بلوك3"),
("471","138","7","مريم روكان","4","انثى","روضة","2023-03-06","روكان","00000000000","بلوك3"),
("472","139","7","كرار ياسر نورس","5","ذكر","تمهيدي","2023-03-05","ياسر نورس","00000000000","بلوك3"),
("474","141","7","زين مصطفى حامد","1","ذكر","روضة","2023-03-12","مصطفى حامد","00000000000","بلوك3"),
("475","142","7","ابا الفضل محسن","5","ذكر","تمهيدي","2023-03-12","محسن","00000000000","بلوك3"),
("476","143","7","امير احمد علي","-5","ذكر","تمهيدي","2023-04-05","احمد علي","00000000000","بلوك3"),
("477","144","7","ناي علي صالح","4","انثى","روضة","2023-03-19","علي صالح","00000000000","بلوك3"),
("478","145","7","امير ضياء حسين","5","ذكر","تمهيدي","2023-03-06","ضياء حسين","00000000000","بلوك3"),
("479","146","7","مينا علي مهدي","1","انثى","روضة","2023-04-02","علي مهدي","00000000000","بلوك3"),
("480","147","7","احمد علي مهدي","5","ذكر","تمهيدي","2023-04-02","علي مهدي","00000000000","بلوك3"),
("481","148","7","ايليا نزار عبد الحسين","5","ذكر","تمهيدي","2023-03-06","نزار عبد الحسين","00000000000","بلوك3"),
("482","149","7","فاطمة حيدر","4","انثى","روضة","2023-04-04","حيدر","00000000000","بلوك3"),
("483","150","7","قمر احمد ملاذ","4","انثى","روضة","2023-04-05","احمد منال","00000000000","بلوك3"),
("484","151","7","رزان مرتضى","2","انثى","تحضيري","2023-04-05","مرتضى","00000000000","بلوك3"),
("485","152","7","جوان احمد","1","انثى","تحضيري","2023-03-06","احمد","00000000000","بلوك3"),
("486","153","7","علي احمد","2","ذكر","حضانة","2023-03-06","احمد","00000000000","بلوك3"),
("487","154","7","دانيه احمد","2","انثى","حضانة","2023-03-06","احمد","00000000000","بلوك3"),
("489","156","7","ارزه محمد","4","انثى","روضة","2023-03-12","محمد","00000000000","بلوك3"),
("490","157","7","ياسر احمد خضير","5","ذكر","تمهيدي","2023-04-02","محمد خضير","00000000000","بلوك3"),
("491","158","7","عباس علي سمير","2","ذكر","تحضيري","2023-03-19","علي سمير","00000000000","بلوك3"),
("492","159","7","فيروز كريم","5","انثى","تمهيدي","2023-03-12","كريم","00000000000","بلوك3"),
("493","160","7","دره كريم","-1","انثى","حضانة","2023-03-12","كريم","00000000000","بلوك3"),
("494","161","7","علي محمد حسين","5","ذكر","تمهيدي","2023-03-20","محمد حسين","00000000000","بلوك3"),
("495","162","7","علي محمد عبد الامير ","1","ذكر","روضة","2023-03-12","محمد عبد الامير","00000000000","بلوك3"),
("496","163","7","جنى علي","1","انثى","روضة","2023-03-12","علي","00000000000","بلوك3"),
("497","164","9","حسين علي لفته","1","ذكر","حضانة","2023-03-19","علي لفته","00000000000","بلوك5"),
("501","168","9","رزان محمد نجم","4","انثى","روضة","2023-03-18","محمد نجم","00000000000","بلوك5"),
("503","170","9","ملك ضياء","5","انثى","تمهيدي","2023-03-19","ضياء","00000000000","بلوك5"),
("506","173","9","عراق وقاص","4","ذكر","روضة","2023-03-19","وقاص","00000000000","بلوك5"),
("509","176","9","زهراء لؤي","1","انثى","روضة","2023-03-19","لؤي","00000000000","بلوك5"),
("512","179","9","عبد الله محمد هادي","5","ذكر","تمهيدي","2023-03-25","محمد هادي","00000000000","بلوك5"),
("513","180","9","اصف محمد","2","ذكر","حضانة","2023-03-24","محمد","00000000000","بلوك5"),
("514","181","9","رقيه سليم","2","انثى","حضانة","2023-03-25","سليم","00000000000","بلوك5"),
("515","182","9","اديم محمد","5","ذكر","حضانة","2023-03-24","محمد","00000000000","بلوك5"),
("518","185","9","دارين علي","4","انثى","روضة","2023-03-05","علي","00000000000","بلوك5"),
("519","186","9","حيدر علي","4","ذكر","روضة","2023-03-05","علي","00000000000","بلوك5"),
("520","187","9","قمر جواد","4","انثى","روضة","2023-03-01","جواد","00000000000","بلوك5"),
("522","189","9","قمر مصطفى","5","انثى","حضانة","2023-03-28","مصطفى","00000000000","بلوك5"),
("523","190","9","مريم حيدر","5","انثى","تمهيدي","2023-03-05","حيدر","00000000000","بلوك5"),
("524","191","9","ايوب امير","5","ذكر","تمهيدي","2023-04-01","امير","00000000000","بلوك5"),
("525","192","9","علي مثنى","5","ذكر","تمهيدي","2023-04-02","مثنى","00000000000","بلوك5"),
("526","193","9","جود علي ","1","ذكر","روضة","2023-03-07","علي","00000000000","بلوك5"),
("528","195","9","ميار عبد القادر","4","انثى","روضة","2023-03-08","عبد القادر","00000000000","بلوك5"),
("534","201","9","لافا نوزاد","1","انثى","روضة","2023-03-06","نوزاد","00000000000","بلوك5"),
("535","202","8","زين العابدين سرور","3","ذكر","تحضيري","2023-03-19","سرور","00000000000","بلوك4"),
("536","203","8","نرجس كرار","5","انثى","تمهيدي","2023-03-20","كرار","00000000000","بلوك4"),
("537","204","8","فاطمه كرار","4","انثى","روضة","2023-03-20","كرار","00000000000","بلوك4"),
("538","205","8"," ادم عامر","5","ذكر","تمهيدي","2023-03-11","عامر","00000000000","بلوك4"),
("539","206","8","امير عامر","2","ذكر","حضانة","2023-03-11","عامر","00000000000","بلوك4"),
("540","207","8","علي حيدر","1","ذكر","تحضيري","2023-03-12","حيدر","00000000000","بلوك4"),
("541","208","8","زينب امير","3","انثى","تحضيري","2023-03-17","امير","00000000000","بلوك4"),
("542","209","8","عباس امير","3","ذكر","تحضيري","2023-03-17","امير","00000000000","بلوك4"),
("543","210","8","نور ساطع ","3","انثى","تحضيري","2023-03-19","ساطع","00000000000","بلوك4"),
("544","211","8","ود علي ","2","انثى","حضانة","2023-03-20","علي","00000000000","بلوك4"),
("545","212","8","ايلين صباح","4","انثى","روضة","2023-03-20","صباح","00000000000","بلوك4"),
("546","213","8","ليث مصطفى","4","ذكر","روضة","2023-04-05","مصطفى","00000000000","بلوك4"),
("547","214","8","نرجس امير","4","انثى","روضة","2023-03-16","امير","00000000000","بلوك4"),
("548","215","8","امير محمد","2","ذكر","حضانة","2023-03-22","محمد","00000000000","بلوك4"),
("549","216","8","ايلين قيصر","3","انثى","تحضيري","2023-03-26","قيصر","00000000000","بلوك4"),
("550","217","8","عبدالله رسول","3","ذكر","تحضيري","2023-03-21","رسول","00000000000","بلوك4"),
("551","218","8","مرتضى عمر","5","ذكر","تمهيدي","2023-03-19","عمر","00000000000","بلوك4"),
("552","219","8","قسور احمد","2","ذكر","حضانة","2023-03-20","احمد","00000000000","بلوك4"),
("553","220","8","ريتال  محمد","1","انثى","حضانة","2023-03-20","احمد","00000000000","بلوك4"),
("554","221","8","جود ابراهيم","4","ذكر","روضة","2023-03-22","ابراهيم","00000000000","بلوك4"),
("555","222","8","سدن علي","2","انثى","حضانة","2023-03-23","علي","00000000000","بلوك4"),
("561","228","8","جعفر سهيل","5","ذكر","تمهيدي","2023-03-23","سهيل","00000000000","بلوك4"),
("562","229","8","ماريه محسن","5","انثى","تمهيدي","2023-03-01","محسن","00000000000","بلوك4"),
("563","230","8","محمد علي","2","ذكر","حضانة","2023-04-02","علي","00000000000","بلوك4"),
("564","231","8","تيم عمر","2","ذكر","حضانة","2023-03-26","عمر","00000000000","بلوك4"),
("566","233","8","ايهم خليل","2","ذكر","حضانة","2023-03-01","خليل","00000000000","بلوك4"),
("567","234","8","ايدن خليل","4","ذكر","روضة","2023-03-01","خليل","00000000000","بلوك4"),
("568","235","8","عبدالرزاق صلاح","1","ذكر","تمهيدي","2023-03-22","صلاح","00000000000","بلوك4"),
("569","236","8","علي سيف","5","ذكر","تمهيدي","2023-04-02","سيف","00000000000","بلوك4"),
("571","238","8","حمزه عماد","5","ذكر","تمهيدي","2023-04-01","عماد","00000000000","بلوك4"),
("572","239","8","ريتال محمد","5","انثى","تمهيدي","2023-03-01","محمد","00000000000","بلوك4"),
("573","240","8","لمار رائد","4","انثى","روضة","2023-03-02","رائد","00000000000","بلوك4"),
("575","242","8","فاطمه عصام","5","انثى","تمهيدي","2023-03-01","عصام","00000000000","بلوك4"),
("576","243","8","رضا عصام","5","ذكر","تمهيدي","2023-03-01","عصام","00000000000","بلوك4"),
("577","244","8","فضل الله سرمد","1","ذكر","حضانة","2023-04-01","سرمد","00000000000","بلوك4"),
("578","245","8","عبدالله سرمد","2","ذكر","تحضيري","2023-04-01","سرمد","00000000000","بلوك4"),
("579","246","8","ادم رائد","5","ذكر","تمهيدي","2023-04-07","رائد","00000000000","بلوك4"),
("580","247","8","بنامين ياسر","4","ذكر","روضة","2023-04-01","ياسر","00000000000","بلوك4"),
("581","248","8","يعقوب ياسر","2","ذكر","حضانة","2023-04-01","ياسر","00000000000","بلوك4"),
("582","249","8","يوسف ياسر","5","ذكر","تمهيدي","2023-04-01","ياسر","00000000000","بلوك4"),
("583","250","8","يامن مصطفى","3","ذكر","تحضيري","2023-04-01","مصطفى","00000000000","بلوك4"),
("584","251","8","مهيمن محمد","2","ذكر","حضانة","2023-04-02","محمد","00000000000","بلوك4");
INSERT INTO stud_tb VALUES
("586","253","8","مصطفى احسان","5","ذكر","تمهيدي","2023-04-04","احسان","00000000000","بلوك4"),
("587","254","8","محمد احسان","4","ذكر","روضة","2023-04-04","احسان","00000000000","بلوك4"),
("588","255","8","مرتضى كاظم","2","ذكر","حضانة","2023-04-03","كاظم","00000000000","بلوك4"),
("589","256","8","ام البنين كاظم","4","انثى","روضة","2023-04-03","كاظم","00000000000","بلوك4"),
("590","257","8","لليان محمد","3","انثى","تحضيري","2023-03-06","محمد","00000000000","بلوك4"),
("591","258","8","ليان محمد","3","انثى","تحضيري","2023-03-06","محمد","00000000000","بلوك4"),
("592","259","8","يمان محمد","4","انثى","روضة","2023-04-05","محمد","00000000000","بلوك4"),
("593","260","8","شدن محمد","4","انثى","روضة","2023-04-05","محمد","00000000000","بلوك4"),
("594","261","8","حسن احمد قاسم","5","ذكر","تمهيدي","2023-03-12","احمد قاسم","00000000000","بلوك4"),
("595","262","8","يوسف عبد الرحمن ","2","ذكر","حضانة","2023-03-09","عبد الرحمن","00000000000","بلوك4"),
("596","263","8","مسك عبد الرحمن","2","انثى","حضانة","2023-03-09","عبد الرحمن","00000000000","بلوك4"),
("598","265","8","ليان عامر","4","انثى","روضة","2023-04-07","عامر","00000000000","بلوك4"),
("599","266","8","ياسين حسنين","5","ذكر","تمهيدي","2023-03-08","حسنين","00000000000","بلوك4"),
("600","267","8","هيلين حسنين","5","انثى","تمهيدي","2023-03-08","حسنين","00000000000","بلوك4"),
("601","268","8","هبه الله مصطفى","3","انثى","تحضيري","2023-03-08","مصطفى","00000000000","بلوك4"),
("602","269","8","يقين احمد","4","انثى","روضة","2023-03-19","احمد","00000000000","بلوك4"),
("603","270","8","غسق احمد","3","انثى","تحضيري","2023-03-19","احمد","00000000000","بلوك4"),
("605","272","8","علي حامد","3","ذكر","تحضيري","2023-03-20","حامد","00000000000","بلوك4"),
("606","273","8","لارين حامد","4","انثى","روضة","2023-03-20","حامد","00000000000","بلوك4"),
("607","274","8","حيدر احمد","3","ذكر","حضانة","2023-03-20","احمد","00000000000","بلوك4"),
("608","275","8","يافا احمد","5","انثى","تمهيدي","2023-03-20","احمد","00000000000","بلوك4"),
("609","276","8","علي بسام","5","ذكر","تمهيدي","2023-03-18","بسام","00000000000","بلوك4"),
("610","277","8","حسين صادق","5","ذكر","تمهيدي","2023-03-08","صادق","00000000000","بلوك4"),
("611","278","8","زينب صادق","4","انثى","تمهيدي","2023-03-08","صادق","00000000000","بلوك4"),
("612","279","8","منتظر محمود","1","ذكر","روضة","2023-03-20","محمود","00000000000","بلوك4"),
("613","280","8","رهف نور الدين","4","انثى","تحضيري","2023-03-20","نور الدين","00000000000","بلوك4"),
("614","281","8","ازل نور الدين","5","انثى","تمهيدي","2023-03-20","نور الدين","00000000000","بلوك4"),
("617","284","8","اسامه ماهر","4","ذكر","روضة","2023-03-18","ماهر","00000000000","بلوك4"),
("618","285","12","مرجان وسام","1","ذكر","روضة","2023-03-12","وسام","00000000000","بلوك8"),
("620","287","12","ناي محمد","1","انثى","روضة","2023-03-10","محمد","00000000000","بلوك8"),
("621","288","12","ابراهيم محمد","1","ذكر","روضة","2023-03-10","محمد","00000000000","بلوك8"),
("622","289","12","ادم وسام","1","ذكر","روضة","2023-03-12","وسام","00000000000","بلوك8"),
("626","293","12","يوسف مهند","5","ذكر","تمهيدي","2023-03-11","مهند","00000000000","بلوك8"),
("627","294","12","يوسف مهند","1","ذكر","روضة","2023-03-11","مهند","00000000000","بلوك8"),
("628","295","12","كيان مهند","1","ذكر","روضة","2023-03-08","مهند","00000000000","بلوك8"),
("629","296","12","غنى محمد","1","انثى","روضة","2023-03-12","محمد","00000000000","بلوك8"),
("630","297","12","ادم داود","1","ذكر","روضة","2023-02-27","داود","00000000000","بلوك8"),
("631","298","12","صالح مهدي","1","ذكر","روضة","2023-03-29","مهدي","00000000000","بلوك8"),
("632","299","12","دره حيدر","1","انثى","روضة","2023-03-19","حيدر","00000000000","بلوك8"),
("633","300","12","ياسين فلاح","1","ذكر","روضة","2023-03-05","فلاح","00000000000","بلوك8"),
("634","301","12","سلا زيد","1","ذكر","روضة","2023-04-09","زيد","00000000000","بلوك8"),
("635","302","12","امير علي","1","ذكر","روضة","2023-03-14","علي","00000000000","بلوك8"),
("636","303","12","نور حميد","1","ذكر","روضة","2023-03-15","حميد","00000000000","بلوك8"),
("637","304","12","يمين حميد","1","ذكر","روضة","2023-03-15","حميد","00000000000","بلوك8"),
("638","305","12","رزان ياسر","1","انثى","روضة","2023-01-16","ياسر","00000000000","بلوك8"),
("640","307","12","حسين علي ","1","ذكر","روضة","2023-03-15","علي","00000000000","بلوك8"),
("643","310","12","ماري احمد","4","انثى","روضة","2023-03-05","احمد","00000000000","بلوك8"),
("646","313","12","ليث داود","4","ذكر","روضة","2023-03-27","داود","00000000000","بلوك8"),
("647","314","12","رامي مصطفى","4","ذكر","روضة","2023-03-05","مصطفى","00000000000","بلوك8"),
("648","315","12","يونس  ابراهيم","4","ذكر","روضة","2023-03-28","ابراهيم","00000000000","بلوك8"),
("649","316","12","ياس ابراهيم","4","ذكر","روضة","2023-03-22","ابراهيم","00000000000","بلوك8"),
("650","317","12","حسن هيثم","4","ذكر","روضة","2023-03-06","هيثم","00000000000","بلوك8"),
("651","318","12","جوان هيثم","4","ذكر","روضة","2023-03-06","هيثم","00000000000","بلوك8"),
("653","320","12","حسن حيدر","4","ذكر","روضة","2023-03-14","حيدر","00000000000","بلوك8"),
("654","321","11","حامد عمار","5","ذكر","تمهيدي","2023-03-12","عمار","00000000000","بلوك7"),
("657","324","11","سما محمد","6","انثى","تمهيدي","2023-03-13","محمد","00000000000","بلوك7"),
("658","325","11","ميار محمد","5","انثى","تمهيدي","2023-03-13","محمد","00000000000","بلوك7"),
("659","326","11","محمد حسام","4","ذكر","روضة","2023-03-21","حسام","00000000000","بلوك7"),
("660","327","11","امير حسام","4","ذكر","روضة","2023-03-19","حسام","00000000000","بلوك7"),
("661","328","11","رما ثائر","4","انثى","روضة","2023-03-19","ثائر","00000000000","بلوك7"),
("662","329","11","احمد منير","2","ذكر","حضانة","2023-03-18","منير","00000000000","بلوك7"),
("663","330","11","حسين نبيل","5","ذكر","تمهيدي","2023-03-19","نبيل","00000000000","بلوك7"),
("664","331","11","فرح محمد","6","انثى","تمهيدي","2023-03-23","محمد","00000000000","بلوك7"),
("667","334","11","زين العابدين احمد","2","ذكر","حضانة","2023-03-01","احمد","00000000000","بلوك7"),
("668","335","11","حسين احمد","5","ذكر","تمهيدي","2023-03-21","احمد","00000000000","بلوك7"),
("669","336","11","يارا احمد","4","انثى","روضة","2023-03-21","احمد","00000000000","بلوك7"),
("670","337","11","تارا احمد","4","انثى","روضة","2023-03-21","احمد","00000000000","بلوك7"),
("674","341","11","ياسين مصطفى","4","ذكر","روضة","2023-03-23","مصطفى","00000000000","بلوك7"),
("675","342","11","موسى مصطفى ","6","ذكر","تمهيدي","2023-03-23","مصطفى","00000000000","بلوك7"),
("676","343","11","حسن محمد","4","ذكر","روضة","2023-03-20","محمد","00000000000","بلوك7"),
("677","344","11","دره منتظر","5","انثى","تمهيدي","2023-03-05","منتظر","00000000000","بلوك7"),
("678","345","11","امير محمد","5","ذكر","روضة","2023-02-17","محمد","00000000000","بلوك7"),
("679","346","11","موسى فراس","4","ذكر","روضة","2023-03-23","فراس","00000000000","بلوك7"),
("681","348","11","يزن محمد","4","ذكر","روضة","2023-04-01","محمد","00000000000","بلوك7"),
("682","349","11","منه الله كرار","5","انثى","تمهيدي","2023-02-23","كرار","00000000000","بلوك7"),
("683","350","11","ساره مصطفى","5","انثى","تمهيدي","2023-04-01","مصطفى","00000000000","بلوك7"),
("684","351","11","يارا مصطفى","3","انثى","حضانة","2023-04-01","مصطفى","00000000000","بلوك7"),
("685","352","11","عباس هشام","4","ذكر","روضة","2023-03-28","هشام","00000000000","بلوك7"),
("686","353","11","محمد هشام","3","ذكر","حضانة","2023-03-28","هشام","00000000000","بلوك7"),
("688","355","11","حسين علي ","5","ذكر","تمهيدي","2023-03-24","علي","00000000000","بلوك7"),
("689","356","11","يمان علي ","3","انثى","حضانة","2023-03-24","علي","00000000000","بلوك7"),
("690","357","11","يحى وائل","4","ذكر","روضة","2023-04-01","وئل","00000000000","بلوك7"),
("691","358","11","غيث وائل","2","ذكر","حضانة","2023-04-01","وئل","00000000000","بلوك7"),
("694","361","11","يزن امير ","5","ذكر","تمهيدي","2023-03-01","امير","00000000000","بلوك7"),
("695","362","11","قمر امير ","3","انثى","تحضيري","2023-03-01","امير","00000000000","بلوك7"),
("696","363","11","كيان محمد","4","ذكر","روضة","2023-03-01","محمد","00000000000","بلوك7"),
("697","364","11","فضل محمد","1","ذكر","حضانة","2023-03-04","محمد","00000000000","بلوك7"),
("698","365","11","اشتر زيد","3","ذكر","تحضيري","2023-03-05","زيد","00000000000","بلوك7"),
("699","366","11","علي رضا زيد","4","ذكر","روضة","2023-03-05","زيد","00000000000","بلوك7"),
("700","367","11","شهم حيدر","4","ذكر","تحضيري","2023-03-05","حيدر","00000000000","بلوك7"),
("701","368","11","شمم حيدر","4","انثى","تحضيري","2023-03-05","حيدر","00000000000","بلوك7"),
("702","369","11","ضي وسام ","3","انثى","حضانة","2023-03-04","وسام","00000000000","بلوك7"),
("703","370","11","رزان كرار","4","انثى","روضة","2023-03-08","كرار","00000000000","بلوك7"),
("704","371","11","مريم كرار","4","انثى","روضة","2023-03-08","كرار","00000000000","بلوك7"),
("705","372","11","زين العابدين محمود","5","ذكر","تمهيدي","2023-03-08","محمود","00000000000","بلوك7"),
("706","373","11","زمرد محمود","3","انثى","تحضيري","2023-03-08","محمود","00000000000","بلوك7"),
("708","375","11","موسى احمد","5","ذكر","تمهيدي","2023-03-10","احمد","00000000000","بلوك7"),
("709","376","11","فاطمه امير ","5","انثى","تمهيدي","2023-03-09","امير","00000000000","بلوك7"),
("710","377","11","علي امير ","4","ذكر","روضة","2023-03-09","امير","00000000000","بلوك7"),
("711","378","11","حمزه حسين","5","ذكر","تمهيدي","2023-03-13","حسين","00000000000","بلوك7");
INSERT INTO stud_tb VALUES
("712","379","11","دانيه حسين ","3","انثى","تحضيري","2023-03-13","حسين","00000000000","بلوك7"),
("713","380","11","كيان علي ","5","ذكر","تمهيدي","2023-03-13","علي","00000000000","بلوك7"),
("714","381","11","يمان علي ","3","ذكر","حضانة","2023-03-13","علي","00000000000","بلوك7"),
("715","382","10","هيلين هيثم هلال","2","انثى","حضانة","2023-03-13","هيثم هلال","00000000000","بلوك6"),
("716","383","10","عباس علي عباس","5","ذكر","تمهيدي","2023-03-13","علي عباس","00000000000","بلوك6"),
("717","384","10","ناي علي عباس","3","انثى","تحضيري","2023-03-13","علي عباس","00000000000","بلوك6"),
("719","386","10","امير علي عبد الكريم ","2","ذكر","حضانة","2023-03-18","علي عبد الكريم","00000000000","بلوك6"),
("720","387","10","ريتال حسن محسن","2","انثى","حضانة","2023-03-19","حسن محسن","00000000000","بلوك6"),
("721","388","10","بدر علي ياسين","5","ذكر","تمهيدي","2023-03-19","علي ياسين","00000000000","بلوك6"),
("722","389","10","الحسن عبد الكريم غالب","5","ذكر","تمهيدي","2023-03-19","عبد الكريم غالب","00000000000","بلوك6"),
("729","396","10","علي طارق خالد","4","ذكر","روضة","2923-03-19"," طارق خالد","00000000000","بلوك6"),
("730","397","10","محمد طارق خالد","3","ذكر","تحضيري","2023-03-19"," طارق خالد","00000000000","بلوك6"),
("731","398","10","حسين عبد المحسن عيسى","5","ذكر","تمهيدي","2023-03-22","عبد المحسن عيسى","00000000000","بلوك6"),
("732","399","10","عبد الله امير علاء","4","ذكر","روضة","2023-03-19","امير علاء","00000000000","بلوك6"),
("733","400","10","ياسر امير علاء","6","ذكر","تمهيدي","2023-03-19","امير علاء","00000000000","بلوك6"),
("734","401","10","ديار ياسين طه","4","انثى","روضة","2023-03-19","ياسين طه","00000000000","بلوك6"),
("736","403","10","زينب صميم بسام","5","انثى","تمهيدي","2023-03-19","صميم بسام","00000000000","بلوك6"),
("738","405","10","تاليا حسن علي","2","انثى","تحضيري","2023-03-19","حسن علي","00000000000","بلوك6"),
("739","406","10","لارا محمد داود","4","انثى","روضة","2023-03-19","محمد داود","00000000000","بلوك6"),
("740","407","10","ريان فواد نجم","2","انثى","حضانة","2023-03-21","فواد نجم","00000000000","بلوك6"),
("741","408","10","علي عباس حسن","4","ذكر","روضة","2023-03-18","عباس حسن","00000000000","بلوك6"),
("742","409","10","امير عباس حسن","3","ذكر","تحضيري","2023-03-18","عباس حسن","00000000000","بلوك6"),
("743","410","10","الياس محمد خير الله","3","ذكر","تحضيري","2023-03-19"," محمد خير الله","00000000000","بلوك6"),
("744","411","10","عسل اسماعيل جمعه","5","انثى","تمهيدي","2023-03-20"," اسماعيل جمعه","00000000000","بلوك6"),
("745","412","10","غزل عبد الحميد جعفر","4","انثى","روضة","2023-03-20"," عبد الحميد جعفر","00000000000","بلوك6"),
("748","415","10","ادم مصطفى سلام","4","ذكر","روضة","2023-03-28","مصطفى سلام","00000000000","بلوك6"),
("749","416","10","غيث الكرار حسن","2","ذكر","حضانة","2023-03-26","الكرار حسن","00000000000","بلوك6"),
("750","417","10","زهراء جعفر هادي","5","انثى","تمهيدي","2023-03-23","جعفر هادي","00000000000","بلوك6"),
("751","418","10","موسى جعفر هادي","3","ذكر","تحضيري","2023-03-23","جعفر هادي","00000000000","بلوك6"),
("756","423","10","ريتاج صلاح محسن","4","انثى","روضة","2023-03-19","صلاح محسن","00000000000","بلوك6"),
("757","424","10","محمد صلاح محسن","3","ذكر","تحضيري","2023-03-19","كرارصلاح محسن","00000000000","بلوك6"),
("758","425","10","علي احمد جلوب ","2","ذكر","حضانة","2023-04-02","احمد جلوب","00000000000","بلوك6"),
("759","426","10","مرتضى مصطفى ساجد","5","ذكر","تمهيدي","2023-03-26","مصطفى ساجد","00000000000","بلوك6"),
("762","429","10","لارا اسعد حامد ","2","انثى","حضانة","2023-04-02","اسعد حامد","00000000000","بلوك6"),
("763","430","10","يوسف اسعد حامد","3","ذكر","تحضيري","2023-04-02","اسعد حامد","00000000000","بلوك6"),
("765","432","10","نسم حيدر اسماعيل","4","انثى","روضة","2023-03-26","حيدر سماعيل","00000000000","بلوك6"),
("767","434","10","در فاضل عباس","5","انثى","تمهيدي","2023-04-05","فاضل عباس","00000000000","بلوك6"),
("769","436","10","ريان علي احمد","3","انثى","تحضيري","2023-04-01","علي احمد","00000000000","بلوك6"),
("770","437","10","هيلين دريد سلمان","4","انثى","روضة","2023-04-02","دريد سلمان","00000000000","بلوك6"),
("771","438","10","سيف علي منذر","5","ذكر","تمهيدي","2023-04-05","علي منذر","00000000000","بلوك6"),
("772","439","10","جود محمد عبد الحسين","5","ذكر","تمهيدي","2023-04-02","محمد عبد الحسين","00000000000","بلوك6"),
("773","440","10","جوليا محمد عبد الحسين","2","انثى","حضانة","2023-04-02","محمد عبد الحسين","00000000000","بلوك6"),
("774","441","10","ياسين عمار ماجد","3","ذكر","تحضيري","2023-03-05","عمار ماجد","00000000000","بلوك6"),
("775","442","10","ابراهيم اسعد يوسف","4","ذكر","روضة","2023-04-04","اسعد يوسف","00000000000","بلوك6"),
("776","443","10","فضل عادل ياسين ","5","ذكر","تمهيدي","2023-04-02","عادل ياسين","00000000000","بلوك6"),
("777","444","10","سيدرا سيف هيثم","5","انثى","تمهيدي","2023-04-06","سيف هيثم","00000000000","بلوك6"),
("778","445","10","علي سيف هيثم ","2","ذكر","حضانة","2023-04-06","سيف هيثم","00000000000","بلوك6"),
("780","447","10","ميسم معمر محمد","2","انثى","حضانة","2023-04-02","معمر محمد","00000000000","بلوك6"),
("782","449","10","رهف ناضر جلوب","4","انثى","روضة","2023-04-07","ناضر جلوب","00000000000","بلوك6"),
("783","450","10","فهد ناضر جلوب","2","ذكر","حضانة","2023-04-07","ناضر جلوب","00000000000","بلوك6"),
("784","451","10","علي احمد كيلو حاشوش","4","ذكر","روضة","2023-04-09","احمد كيلو حاشوش","00000000000","بلوك6"),
("785","452","10","عبد الله احمد كيلو حاشوش","3","ذكر","تحضيري","2023-04-09"," احمد كيلو حاشوش","00000000000","بلوك6"),
("788","455","10","رضا محمد عبد الامير","2","ذكر","حضانة","2023-03-08","محمد عبد الامير","00000000000","بلوك6"),
("790","457","10","ادم علي محمود","4","ذكر","روضة","2023-03-10","علي محمود","00000000000","بلوك6"),
("791","458","10","در علي محمود","2","انثى","حضانة","2023-03-10","علي محمود","00000000000","بلوك6"),
("792","459","10","ريان نصير ناظم ","5","ذكر","تمهيدي","2023-03-11"," نصير ناظم","00000000000","بلوك6"),
("793","460","10","علي فاضل عبد الواحد","3","ذكر","تحضيري","2023-03-12","فاضل عبد الواحد","00000000000","بلوك6"),
("794","461","10","كرم مصطفى سلام","6","ذكر","تمهيدي","2023-03-12","مصطفى سلام","00000000000","بلوك6"),
("796","463","10","روز باسم حسين","5","ذكر","تمهيدي","2023-03-09","باسم حسين","00000000000","بلوك6"),
("797","464","10","لارين حسام سماعيل","3","انثى","تحضيري","2023-03-15","حسام سماعيل","00000000000","بلوك6"),
("798","465","10","ريتال مضر خالد","2","انثى","حضانة","2023-03-24"," مضر خالد","00000000000","بلوك6"),
("800","467","12","شمس محمد","2","انثى","حضانة","2023-03-05","محمد","00000000000","بلوك8"),
("802","468","11","شهد احمد","4","انثى","روضة","2023-03-13","احمد","00000000000","A7"),
("803","469","11","ادم احمد","1","ذكر","حضانة","2023-03-13","احمد","00000000000","A7"),
("804","470","11","اهم علاء","1","ذكر","حضانة","2023-03-19","علاء","00000000000","A7"),
("805","471","11","علي رضاحسين","4","ذكر","روضة","2023-03-19","حسين","00000000000","A7"),
("806","472","11","ادم خيام","5","ذكر","تمهيدي","2023-02-19","خيام","00000000000","A7"),
("808","474","11","ميار احمد","4","انثى","تمهيدي","2023-04-01","احمد","00000000000","A7"),
("809","475","11","مسرى تحسين","4","انثى","روضة","2023-02-26","تحسين","00000000000","A7"),
("810","476","8","فاطمه محمد","5","انثى","تمهيدي","2023-03-08","محمد","00000000000","a4"),
("811","477","8","مريم مصطفى","560","انثى","تمهيدي","2023-02-08","مصطفى","00000000000","B8"),
("812","478","8","ماريا عمر","5","انثى","تمهيدي","2023-03-24","عمر","00000000000","a4"),
("813","479","8","مريم حسام","2","انثى","حضانة","2023-03-19","حسام","00000000000","a4"),
("814","480","8","اسل حسام","4","انثى","روضة","2023-03-19","حسام","00000000000","a4"),
("816","482","6","هاشم نور","5","ذكر","تمهيدي","2023-03-28","0000000000","00000000000","a7"),
("817","483","6","رانسا قصي","5","انثى","تمهيدي","2023-03-29","0000000000","00000000000","0000"),
("818","484","8","فهد احمد","3","ذكر","تحضيري","2023-04-09","احمد","00000000000","A3"),
("819","485","8","اجوان احمد","5","انثى","تمهيدي","2023-02-19","احمد","00000000000","A8"),
("820","486","8","حميد مضر","4","ذكر","تحضيري","2023-04-05","مضر حميد","00000000000","a4"),
("821","487","8","يزن هادي","4","ذكر","روضة","2023-03-19","هادي","00000000000","A5"),
("822","488","8","عبد الله هادي","3","ذكر","تحضيري","2023-03-19","هادي","00000000000","A5"),
("823","489","11","بنين قصي","4","انثى","روضة","2023-02-27","قصي","00000000000","A7"),
("824","490","11","رهف حسين","4","انثى","روضة","2023-02-27","حسين","00000000000","A7"),
("825","491","8","جنى مازن","5","انثى","تمهيدي","2023-02-19","مازن","00000000000","a4"),
("827","493","11","ديما موسى","2","انثى","تحضيري","2023-03-01","موسى","00000000000","A7"),
("828","494","11","علي محمد المنتظر","5","ذكر","تمهيدي","2023-04-01","محمد المنتظر","00000000000","A7"),
("829","495","9","زهراء عبد القادر","5","انثى","تمهيدي","2023-03-08","عبد القادر","00000000000","بلوك5"),
("834","500","9","الان نوزاد","3","ذكر","حضانة","2023-03-06","نوزاد","00000000000","بلوك9"),
("835","501","9","ملاك صفاء","4","انثى","روضة","2023-03-06","صفاء","00000000000","بلوك5"),
("836","502","9","طيبه محمود","1","انثى","حضانة","2023-03-09","محمود","00000000000","بلوك5"),
("837","503","9","حسين مصطفى","4","ذكر","روضة","2023-03-12","مصطفى ","00000000000","بلوك4"),
("840","506","9","ايليا ارشد","2","انثى","حضانة","2023-03-15","ارشد","00000000000","بلوك5"),
("841","507","10","رحمة احمد حسين","4","انثى","روضة","2023-03-19","احمد حسين","07717473244","A6"),
("842","508","10","يمامة زيد مجيد","2","انثى","حضانة","2023-03-27","زيد مجيد","07713795524","A6"),
("843","509","10","محمد حيدر محمود","3","ذكر","تحضيري","2023-03-26","حيدر محمود","07727146146","A6"),
("844","510","10","نرجس احمد علاء","3","انثى","تحضيري","2023-03-26","احمد علاء","07703131993","A5"),
("848","514","10","زمرد وسام شاكر","3","انثى","تحضيري","2023-03-26","وسام شاكر","07801769622","A5"),
("849","515","12","شهد سيف كامل","4","انثى","روضة","2023-03-07","كامل","00000000000","A8"),
("850","516","12","غزل سيف كامل","3","انثى","حضانة","2023-03-07","كامل","00000000000","A8"),
("851","517","12","رتيل رامي","4","انثى","روضة","2023-03-29","رامي","00000000000","A8");
INSERT INTO stud_tb VALUES
("852","518","12","زين الدين فضاء","2","ذكر","حضانة","2023-03-28","فضاء","00000000000","A8"),
("855","521","12"," ليليان مهند","5","انثى","تمهيدي","2023-03-29","مهند","00000000000","A8"),
("856","522","12","يوسف علي حمزه","5","ذكر","تمهيدي","2023-02-25","علي","00000000000","A8"),
("858","524","12","علي اركان علي","5","ذكر","تمهيدي","2023-03-05","اركان علي","00000000000","A8"),
("859","525","12","فاطمه حسين","4","انثى","روضة","2023-02-28","حسين","00000000000","A8"),
("860","526","12","رقيه مشتاق","3","انثى","حضانة","2023-03-28","مشتاق","00000000000","A8"),
("861","527","12","علي مشتاق","2","ذكر","حضانة","2023-03-28","مشتاق","00000000000","A8"),
("862","528","12","ملك محمد","2","انثى","حضانة","2023-02-28","محمد","00000000000","A8"),
("863","529","12","كرم ميثم","5","ذكر","تمهيدي","2023-02-28","ميثم","00000000000","A8"),
("864","530","12","ثمر سيف علاء","5","انثى","تمهيدي","2023-03-15","سيف علاء","00000000000","A8"),
("865","531","12","ابراهيم يهاب","5","ذكر","تمهيدي","2023-03-02","يهاب","00000000000","A8"),
("866","532","12","ابراهيم يهاب","4","ذكر","روضة","2023-03-02","يهاب","00000000000","A8"),
("867","533","12","رزان اياد ","4","انثى","روضة","2023-03-01","اياد","00000000000","A8"),
("868","534","12","لارين احمد","4","انثى","روضة","2023-03-01","احمد","00000000000","A8"),
("869","535","12","يمان احمد خضر","2","ذكر","حضانة","2023-03-01","احمد خضر","00000000000","A8"),
("870","536","12","جود اكرم سعدون","5","ذكر","تمهيدي","2023-03-15","اكرم سعدون","00000000000","A8"),
("871","537","12","جمانه اكرم سعدون","3","انثى","حضانة","2023-03-15","اكرم سعدون","00000000000","A8"),
("872","538","7","داود علي قاسم ","-5","ذكر","روضة","2023-03-20","علي قاسم","00000000000","B2"),
("873","539","6","كرم حارث محمد","5","ذكر","تمهيدي","2023-03-27","0000000000","00000000000","0000"),
("874","540","6","جود حارث محمد","4","انثى","روضة","2023-03-27","0000000000","00000000000","0000"),
("875","541","6","بحر حارث محمد","2","ذكر","حضانة","2023-03-27","0000000000","00000000000","0000"),
("876","542","6","رهف رواد","4","انثى","روضة","2023-03-28","0000000000","00000000000","A2"),
("877","543","6","سراج منير","4","ذكر","روضة","2023-03-12","0000000000","00000000000","0000"),
("879","545","9","جود ليث ","5","انثى","تمهيدي","2023-04-02","ليث","00000000000","بلوك 6"),
("880","546","9","اية علي ","5","انثى","تمهيدي","2023-04-02","علي ","00000000000","بلوك5"),
("881","547","9","رضا علي ","5","ذكر","تمهيدي","2023-04-02","علي ","00000000000","بلوك5"),
("882","548","6","امين اديب","5","ذكر","تمهيدي","2023-03-20","0000000000","00000000000","0000"),
("884","550","9","ارجوان صلاح ","4","انثى","روضة","2023-03-19","صلاح ","00000000000","بلوك b3"),
("885","551","9","ايلين بلال ","5","انثى","تمهيدي","2023-03-23","بلال","00000000000","بلوك5"),
("886","552","9","فرح محمد هادي ","2","انثى","حضانة","2023-03-25","محمد هادي ","00000000000","بلوك 8"),
("887","553","9","ان علاء ","1","انثى","حضانة","2023-03-26","علاء ","00000000000","بلوك b3"),
("888","554","9","ارام علاء ","4","ذكر","روضة","2023-03-26","علاء ","00000000000","بلوك b3"),
("889","555","9","عسل امجد ","4","انثى","روضة","2023-03-26","امجد ","00000000000","بلوك5"),
("890","556","9","ليلاس حسن ","5","انثى","تمهيدي","2023-02-26","حسن راضي ","00000000000","بلوك5"),
("891","557","9","علا حيدر ","4","انثى","روضة","2023-03-26","حيدر ثابت ","00000000000","بلوك5"),
("892","558","9","جنى حيدر ","3","انثى","حضانة","2023-03-26","حيدر ثابت ","00000000000","بلوك5"),
("893","559","9","علي يوسف ","1","ذكر","حضانة","2023-02-26","يوسف ","00000000000","بلوك5"),
("894","560","9","ليان صبري ","4","انثى","روضة","2023-02-26","صبري ","00000000000","بلوك5"),
("895","561","9","محمد احمد ","4","ذكر","روضة","2023-02-25","احمد ","00000000000","بلوك5"),
("896","562","9","وتين عصام ","4","انثى","روضة","2023-02-27","عصام ","00000000000","بلوك5"),
("897","563","9","رسيل ياسر ","5","انثى","تمهيدي","2023-03-27","ياسر ","00000000000","بلوك b3"),
("898","564","9","جود ليث ","5","انثى","تمهيدي","2023-04-02","ياسر ","00000000000","بلوك 6"),
("899","565","9","روند علي ","4","انثى","روضة","2023-03-23","علي ","00000000000","بلوك5"),
("900","566","9","حسين محمد ","2","ذكر","حضانة","2023-03-01","محمد ","00000000000","بلوك b3"),
("901","567","9","رضا علي ","5","ذكر","تمهيدي","2023-02-26","علي ","00000000000","بلوك5"),
("902","568","9","اية علي ","5","انثى","تمهيدي","2023-02-26","علي ","00000000000","بلوك5"),
("903","569","9","فاطمهة ذوالفقار ","4","انثى","روضة","2023-03-02","ذوالفقار ","00000000000","بلوك b3"),
("904","570","9","مهدي ذوالفقار ","1","ذكر","حضانة","2023-03-02","ذوالفقار ","00000000000","بلوك b3"),
("905","571","12","افين عدي","4","انثى","روضة","2023-03-05","عدي","00000000000","A8"),
("906","572","8","امير علي","2","ذكر","حضانة","2023-03-19","علي","00000000000","a4"),
("907","573","8","ليان علي","4","انثى","روضة","2023-03-22","علي","00000000000","A3"),
("909","575","8","محمد عمار","4","ذكر","روضة","2023-03-26","عمار","00000000000","a4"),
("910","576","8","جود محمد","3","انثى","تحضيري","2023-03-28","محمد","00000000000","A2"),
("912","578","12","احمد راشد","2","انثى","حضانة","2023-03-01","راشد","00000000000","A8"),
("913","579","6","ادم احمد","5","ذكر","تمهيدي","2023-03-05","0000000000","00000000000","0000"),
("914","580","6","قمر مهند","4","انثى","روضة","2023-04-06","0000000000","00000000000","0000"),
("915","581","6","فيان علي","5","انثى","تمهيدي","2023-04-04","0000000000","00000000000","0000"),
("917","583","6","محمد احمد كاظم","2","ذكر","حضانة","2023-03-19","0000000000","00000000000","0000"),
("918","584","6","علي احمد كاظم","2","ذكر","حضانة","2023-03-19","0000000000","00000000000","0000"),
("919","585","7","دره كريم كطافه","-1","انثى","حضانة","2023-03-12","كريم كطافه","00000000000","B2"),
("920","586","7","ابراهيم محمود صكر ","-1","انثى","حضانة","2023-03-05"," محمود  صكر ","07717929610","a2"),
("921","587","12","علي زيد","4","ذكر","روضة","2023-03-06","علي","00000000000","A8"),
("922","588","12","يسر خالد","4","انثى","روضة","2023-03-02","خالد","00000000000","A8"),
("924","590","12","زين العابدين خالد","2","ذكر","حضانة","2023-03-02","خالد","00000000000","A8"),
("925","591","12","امير محمد","5","ذكر","تمهيدي","2023-03-05","محمد","00000000000","A8"),
("926","592","12","الياس محمد","2","ذكر","حضانة","2023-03-05","محمد","00000000000","A8"),
("927","593","12","رقيه علي","1","انثى","حضانة","2023-03-08","علي","00000000000","A8"),
("928","594","12","حسين علي","3","ذكر","حضانة","2023-03-10","علي","00000000000","A8"),
("929","595","12","يوسف علي عبدالستار","5","ذكر","تمهيدي","2023-03-05","علي","00000000000","A8"),
("930","596","10","حسن حيدر حسن","3","ذكر","حضانة","2023-03-06","حيدر حسن","07714114814","A6"),
("932","598","10","ماسة حيدر محمد","2","انثى","حضانة","2023-03-06","حيدر محمد","07729855080","A6"),
("933","599","11","زين العابدين احمد مصطفى","5","ذكر","روضة","2023-03-01","احمد مصطفى","00000000000","A7"),
("934","600","11","كوثر عباس","4","انثى","روضة","2023-03-02","عباس","00000000000","A7"),
("935","601","11","عسل احمد","4","انثى","تحضيري","2023-04-02","احمد","00000000000","A7"),
("936","602","10","جود خالد محمود","2","ذكر","حضانة","2023-03-06","خالد محمود","07715204059","A3"),
("937","603","11","رانسي وسام","4","انثى","روضة","2023-03-05","وسام","00000000000","A7"),
("938","604","11","افنان علي","3","انثى","تحضيري","2023-03-05","علي","00000000000","A7"),
("939","605","11","يوسف نمير","4","ذكر","روضة","2023-03-05","نمير","00000000000","A7"),
("940","606","11","عبد الله فهد","5","ذكر","تمهيدي","2023-03-06","فهد","00000000000","A7"),
("941","607","11","غدير بهاء","5","انثى","تمهيدي","2023-03-01","بهاء","00000000000","A7"),
("942","608","11","منتظر بهاء","5","ذكر","تمهيدي","2023-03-01","بهاء","00000000000","A7"),
("943","609","7","منه علي جاسم ","6","انثى","تمهيدي","2023-03-14","علي جاسم","00000000000","B2"),
("944","610","7","ميار علي جاسم","-6","انثى","تمهيدي","2023-03-14","علي جاسم","","B2"),
("945","611","7","بلاسم احمد بلاسم","-2","ذكر","حضانة","2023-03-12","احمد بلاسم","00000000000","A3"),
("946","612","7","رزان حسن علي","-2","انثى","تحضيري","2023-03-15","حسين علي","00000000000","A3"),
("947","613","7","ادم حيدر عبد الحميد","-3","انثى","تحضيري","2023-03-19","حيدر عبد الحميد","00000000000","A3"),
("948","614","7"," ود صادق ","-2","انثى","حضانة","2023-03-15","صادق جبر ","00000000000","A3"),
("949","615","7","زين العابدين صادق","-2","انثى","حضانة","2023-03-15","صادق جبر ","00000000000","A3"),
("950","616","7","مريم نصر الله ","-5","انثى","روضة","2023-03-15","نصر الله","00000000000","A2"),
("951","617","7","دينه احمد يوسف ","-2","انثى","حضانة","2023-03-26","احمد يوسف ","00000000000","A3"),
("952","618","7","محمد احمد يوسف ","-5","ذكر","روضة","2023-03-26","احمد يوسف ","00000000000","A3"),
("953","619","7","ايليا مرتضى ","-3","ذكر","تحضيري","2023-03-26","مرتضى ","00000000000","A3"),
("954","620","7","جوان محمد سالم ","-2","انثى","تحضيري","2023-03-20","محمد سالم ","00000000000","B3"),
("955","621","7","ليان محمد سالم ","-2","انثى","تحضيري","2023-03-20","محمد سالم ","00000000000","B3"),
("956","622","7","عباس محمد هاشم","-3","ذكر","تحضيري","2023-03-26","محمد هاشم","00000000000","A3"),
("957","623","7","جود علي طالب ","-3","ذكر","تحضيري","2023-04-06","علي قاسم","00000000000","B2"),
("958","624","7","داود علي قاسم ","-4","ذكر","روضة","2023-03-20","علي قاسم","00000000000","B2"),
("959","625","7","ليان عباس رحمن","-3","انثى","تحضيري","2023-03-19","عباس رحمن","00000000000","A3"),
("960","626","7","رفيف علي زعيل ","-5","انثى","تمهيدي","2023-02-19","علي زعيل ","00000000000","A3"),
("961","627","7","محمد عبد الله ","-4","ذكر","روضة","2023-03-22","عبد الله ","00000000000","A7");
INSERT INTO stud_tb VALUES
("964","630","6","زينب معتز","4","انثى","روضة","2023-03-07","0000000000","00000000000","0000"),
("965","631","6","شاهين احمد","5","ذكر","تمهيدي","2023-03-07","احمد","00000000000","0000"),
("966","632","6","مسك احمد","4","انثى","روضة","2023-03-07","0000000000","00000000000","0000"),
("968","634","8","سيف صلاح","5","ذكر","تمهيدي","2023-03-05","صلاح","00000000000","a4"),
("969","635","8","محمود جمال","5","ذكر","تمهيدي","2023-03-05","جمال","00000000000","A2"),
("970","636","10","سدن عباس فاضل","4","انثى","روضة","2023-03-08","عباس فاضل","07715203931","A6"),
("971","637","12","مصطفى حسين خليل","4","ذكر","روضة","2023-03-05","حسين خليل","00000000000","A8"),
("972","638","12","ياسين فلاح","5","ذكر","تمهيدي","2023-05-02","فلاح","00000000000","A8"),
("973","639","12","يقين حميد","2","انثى","حضانة","2023-05-02","حميد","00000000000","A8"),
("974","640","12","معصومه احمد","3","انثى","تحضيري","2023-06-02","احمد","00000000000","A8"),
("975","641","12","دانيال احمد","2","ذكر","حضانة","2023-06-02","احمد","00000000000","A8"),
("976","642","12","سلا زيد","4","انثى","روضة","2023-06-02","زبد","00000000000","A8"),
("977","643","12","مريم مهند","4","انثى","تمهيدي","2023-06-02","مهند","00000000000","A8"),
("978","644","12","امنيه حسين قيس","4","انثى","تمهيدي","2023-07-02","حسين","00000000000","A8"),
("979","645","12","مهيمن مصطفى","5","ذكر","تمهيدي","2023-08-02","مصطفى","00000000000","A8"),
("980","646","12","ادم مصطفى","3","ذكر","تحضيري","2023-08-02","مصطفى","00000000000","A8"),
("981","647","12","ديما علي","5","انثى","تمهيدي","2023-07-02","علي","00000000000","A8"),
("982","648","12","دره حيدر","5","انثى","تمهيدي","2023-07-02","حيدر","00000000000","A8"),
("983","649","12","كيان مهند","5","ذكر","تمهيدي","2023-08-02","مهند","00000000000","A8"),
("984","650","12","ناتي علي","4","انثى","تمهيدي","2023-08-02","علي","00000000000","A8"),
("985","651","12","ناي محمد مصطفى","2","انثى","حضانة","2023-08-02","محمد مصطفى","00000000000","A8"),
("986","652","12","مصطفى محمد مصطفى","5","ذكر","تمهيدي","2023-08-02","محمد","00000000000","A8"),
("987","653","12","ديما حيدر","5","انثى","تمهيدي","2023-05-02","حيدر","00000000000","A8"),
("988","654","12","لارين حيدر","3","انثى","تحضيري","2023-05-02","حيدر","00000000000","A8"),
("989","655","12","دره اياد","5","انثى","تمهيدي","2023-07-02","اياد","00000000000","A8"),
("990","656","9","ملك بدر ","5","انثى","تمهيدي","2023-03-05","بدر","00000000000","بلوك b3"),
("991","657","9","رزان صباح ","3","انثى","حضانة","2023-03-05","صباح رمضان ","00000000000","بلوك b3"),
("992","658","9","عباس محمد ","4","ذكر","روضة","2023-03-07","محمد فاضل ","00000000000","801"),
("993","659","9","ياسر ضلال","4","ذكر","روضة","2023-03-05","ضلال","00000000000","بلوك5"),
("994","660","9","الياس حسنين ","1","ذكر","حضانة","2023-03-05","حسنين محمد ","00000000000","111"),
("995","661","9","ميلا عبد القادر ","1","انثى","حضانة","2023-03-08","عبد القادر ","00000000000","بلوك5"),
("996","662","10","سدن علي عادل","2","انثى","حضانة","2023-03-12","علي عادل ","07713184131","B3"),
("998","664","12"," ناي محمد","5","انثى","تمهيدي","2023-07-02","محمد","00000000000","A8"),
("999","665","12","ابراهيم محمد","3","ذكر","روضة","2023-07-02","محمد","00000000000","A8"),
("1000","666","12","ليان احمد","4","ذكر","روضة","2023-07-02","احمد","00000000000","A8"),
("1001","667","7","انمى مثنى ","-6","انثى","تمهيدي","2023-03-12","مثنى ","07700000000","A3"),
("1002","668","7","يارا محمد طالب","-2","انثى","حضانة","2023-03-12","محمد طالب ","07700000000","A2"),
("1003","669","7","سارا محمد طالب ","-2","انثى","تحضيري","2023-03-12","محمد طالب ","07700070000","A2"),
("1004","670","12","علي احمد","3","ذكر","روضة","2023-07-02","احمد","00000000000","A8"),
("1005","671","12","حسين علي","3","ذكر","روضة","2023-07-02","علي","00000000000","A8"),
("1006","672","12","جنات محمد","5","انثى","تمهيدي","2023-07-02","محمد","00000000000","A8"),
("1007","673","12","زينب حسين","5","انثى","تمهيدي","2023-09-02","حسين","00000000000","A8"),
("1008","674","12","امير رافت","4","ذكر","روضة","2023-03-11","رافت","00000000000","A8"),
("1009","675","12","اامير رافت","2","ذكر","تحضيري","2023-03-11","رافت","00000000000","A8"),
("1010","676","12","شاناز فيصل","4","انثى","روضة","2023-08-02","فيصل غازي","00000000000","A8"),
("1011","677","12","منتظر علي صباح","5","ذكر","تمهيدي","2023-03-06","علي","00000000000","A8"),
("1012","678","12","رهف علي صباح","4","انثى","روضة","2023-03-05","علي","00000000000","A8"),
("1013","679","12","موسى ظافر","5","ذكر","تمهيدي","2023-03-05","ظافر","00000000000","A8"),
("1014","680","12","ريان عبدالوهاب","4","ذكر","تمهيدي","2023-03-08","عبد الوهاب","00000000000","A8"),
("1015","681","12","يمان محمد كريم ","4","ذكر","روضة","2023-03-12","محمد","00000000000","A8"),
("1016","682","12","رقيه حسين","3","انثى","روضة","2023-03-09","حسين","00000000000","A8"),
("1017","683","12","ايلا احمد عصام","1","انثى","حضانة","2023-03-07","محمد","00000000000","B3"),
("1018","684","12","يمان محمد عصام","4","ذكر","روضة","2023-03-08","محمد","00000000000","A8"),
("1019","685","8","ميلا احمد","5","انثى","تمهيدي","2023-04-09","احمد","00000000000","A2"),
("1020","686","8","ايلا احمد","2","انثى","حضانة","2023-04-09","احمد","00000000000","A2"),
("1021","687","8","سيلا احمد","2","انثى","حضانة","2023-04-09","احمد","00000000000","A2"),
("1022","688","10","محمد حيدر خماس","5","ذكر","تمهيدي","2023-03-28","حيدر خماس","07901445542","A6"),
("1023","689","10","علي حيدر خماس","5","ذكر","تمهيدي","2023-03-28","حيدر خماس","07901445542","A6"),
("1024","690","10","يوسف احمد صبيح","5","ذكر","تمهيدي","2023-03-26","احمد صبيح","07735910490","A6"),
("1025","691","10","سمانة ايوب صبري","4","انثى","روضة","2023-04-01","ايوب صبري","07702789021","A6"),
("1027","693","10","امير علي رعد","4","ذكر","روضة","2023-03-05","علي رعد","07807744791","A1"),
("1028","694","7","ميار علاء ياركه","-3","انثى","تحضيري","2023-03-06","علاء ياركه","07700000000","A3"),
("1029","695","7","علي طارق","-4","ذكر","روضة","2023-03-06","علي ","07800000000","B3"),
("1030","696","7","امير محمد سمير ","-5","ذكر","تمهيدي","2023-03-05","محمد سمير ","07700000000","B2"),
("1031","697","7","امير احمد خيري ","-4","ذكر","روضة","2023-04-04","احمد خيري","00000000000","A3"),
("1032","698","7","جاد فؤاد","-3","ذكر","تحضيري","2023-03-05","فؤاد نعيم","00000000000","حي الوحده"),
("1033","699","7","جنه فؤاد نعيم ","-4","انثى","روضة","2023-03-05","فؤاد نعيم","00000000000","حي الوحده "),
("1034","700","7","زين العابدين محمد ","-5","ذكر","تمهيدي","2023-04-06","محمد ","00000000000","A3"),
("1035","701","7","قمر محمد حسن ","-3","انثى","تحضيري","2023-02-28","محمد حسن ","0000000000","A3"),
("1036","702","7","رضا وسام سامي","-5","ذكر","تمهيدي","2023-02-28","وسام سامي ","07700000000","A6"),
("1037","703","7","عبد الله كمال ابراهيم","-4","ذكر","روضة","2023-02-28","كمال ابراهيم","00000000000","A3"),
("1038","704","7","ادم عدي","-4","ذكر","روضة","2023-02-28","ادم عدي","00000000000","A3"),
("1039","705","7","رحمه اسامه ","-3","انثى","تحضيري","2023-04-02","اسامه علي ","00000000000","A3"),
("1040","706","7","ميرال انور جمعه","-6","انثى","تمهيدي","2023-03-01","انور جمعه","00000000000","A3"),
("1041","707","7","سلطان ساري ","-3","ذكر","تحضيري","2023-03-01","ساري حسن","00000000000","A3"),
("1042","708","7","مؤمل احمد ","-4","ذكر","روضة","2023-03-02","احمد شهاب ","00000000000","A3"),
("1043","709","7","ورد رفيف ","-5","انثى","تمهيدي","2023-03-05"," رفيف ","00000000000","A9"),
("1044","710","11","بهار احمد","3","انثى","تحضيري","2023-03-12","احمد","00000000000","A7"),
("1045","711","11","ايليا","1","ذكر","حضانة","2023-03-14","حسن","00000000000","A7"),
("1046","712","6","علي الرضا مصطفى","5","ذكر","تمهيدي","2023-03-14","مصطفى عبد الجواد","00000000000","A9 905 شقة 501"),
("1047","713","6","محمد ليث","4","ذكر","روضة","2023-03-14","ليث محمد","07704569507","A1 110 شقة 604"),
("1048","714","6","مريم ليث","2","ذكر","حضانة","2023-03-14","ليث محمد","07704569507","A9 905 شقة 501"),
("1049","715","6","منتظر زياد","5","ذكر","تمهيدي","2023-03-19","0000000000","00000000000","0000"),
("1050","716","6","منتظر اسعد","2","ذكر","حضانة","2023-04-02","0000000000","00000000000","0000"),
("1051","717","6","وهب عبد الله","3","ذكر","حضانة","2023-03-26","0000000000","00000000000","0000"),
("1052","718","6","فاطمة احمد","4","انثى","روضة","2023-04-05","0000000000","00000000000","0000"),
("1053","719","6","غنى مرتضى","4","انثى","روضة","2023-04-05","0000000000","00000000000","0000"),
("1054","720","6","ليان بشار","4","انثى","روضة","2023-03-19","0000000000","00000000000","0000"),
("1055","721","6","مهدي ميثم","4","ذكر","روضة","2023-02-20","0000000000","00000000000","0000"),
("1056","722","7","ضي احمد سعدي","-3","انثى","تحضيري","2023-04-03","احمد سعدي","07700000000","حي الوحده"),
("1057","723","7","محمد صفاء حسين احمد","-4","ذكر","روضة","2023-02-27","صفاء حسين احمد","00000000000","A6"),
("1058","724","7","يوسف نورس ستار ","-4","ذكر","روضة","2023-03-29","نورس ستار ","00000000000","A2"),
("1059","725","7","روان طه حقي","-4","انثى","روضة","2023-02-26","طه حقي","07700000000","B2"),
("1060","726","7","حور سامي","-3","انثى","تحضيري","2023-03-29","سامي حسن","00000000000","A3"),
("1061","727","7","مصطفى اسعد","-5","ذكر","تمهيدي","2023-03-28","اسعد","00000000000","A3"),
("1062","728","7","يزن اسعد","-3","ذكر","تحضيري","2023-03-28","اسعد","00000000000","A3"),
("1063","729","7","فرح علي مؤيد","-2","انثى","حضانة","2023-02-26","علي مؤيد","00000000000","B3"),
("1064","730","7","اصاله حسام نصري","-3","انثى","تحضيري","2023-03-30","حسام نصري ","00000000000","A3"),
("1066","732","7","ملك علي احمد","-3","انثى","تحضيري","2023-03-26","علي احمد","07700000000","B2"),
("1067","733","7","علي حسن وهيب ","-6","ذكر","تمهيدي","2023-03-28","حسن وهيب","07700000000","A3");
INSERT INTO stud_tb VALUES
("1068","734","7","غزل عباس","-3","انثى","تحضيري","2023-03-26","عباس","07717929610","A3"),
("1069","735","7","فراس عباس","-4","ذكر","روضة","2023-03-26","عباس","00000000000","A3"),
("1070","736","7","فاطمه سعيد محمد","-3","انثى","تحضيري","2023-03-19","سعيد محمد","07700000000","B2"),
("1071","737","7","فضل سعيد محمد","-4","ذكر","روضة","2023-03-19","سعيد محمد","07700000000","B2"),
("1072","738","7","ليان عدنان كاظم ","-4","انثى","روضة","2023-03-15","عدنان كاظم جريح","07700507303","A3"),
("1073","739","8","تالين وليد","2","انثى","حضانة","2023-03-14","وليد","00000000000","a4"),
("1074","740","6","داود سلام","5","ذكر","تمهيدي","2023-03-15","0000000000","00000000000","A2"),
("1075","741","6","ديمة سعيد","5","انثى","تمهيدي","2023-03-13","0000000000","00000000000","0000"),
("1076","742","7"," محمد مهدي مازن رضا ","-3","ذكر","تحضيري","2023-03-19"," مهدي مازن رضا ","07715696626","B3/304"),
("1077","743","12","ثمر سيف علاء","5","انثى","تمهيدي","2023-03-15","سيف علاء","00000000000","A8"),
("1078","744","12","حوراء امير","5","انثى","تمهيدي","2023-03-19","امير","00000000000","A8"),
("1079","745","11","ميران مروان","3","ذكر","تحضيري","2023-03-20","مروان","00000000000","A7"),
("1080","746","11","يمان مروان","4","ذكر","روضة","2023-03-20","مروان","00000000000","A7"),
("1081","747","10","ليا قصي","2","انثى","حضانة","2023-03-19","لياقصي","07818384747","A6"),
("1082","748","12","ميرال علي","1","انثى","حضانة","2023-03-19","علي","00000000000","A8"),
("1083","749","6","ابا الحسن ضرغام","5","ذكر","تمهيدي","2023-03-19","0000000000","00000000000","0000"),
("1084","750","6","سراج احمد","4","ذكر","روضة","2023-03-22","0000000000","00000000000","0000"),
("1085","751","6","جمانة سيف","4","انثى","روضة","2023-03-19","0000000000","00000000000","0000"),
("1086","752","6","مؤمل حسنين","5","ذكر","تمهيدي","2023-03-19","0000000000","00000000000","0000"),
("1087","753","8","ليا ليث","2","انثى","حضانة","2023-03-19","ليث","00000000000","A8"),
("1088","754","8","تاليا حيدر","2","انثى","حضانة","2023-03-19","حيدر","00000000000","B3"),
("1089","755","8","هيثم عدنان","4","ذكر","روضة","2023-03-19","عدنان","00000000000","a4"),
("1090","756","9","بيسان حيدر ","4","انثى","روضة","2023-03-23","حيدر خالد حسين ","00000000000","b209"),
("1091","757","9","حسن علي ","3","ذكر","حضانة","2023-03-20","علي سالم ","00000000000","505"),
("1092","758","9","جنى تمام ","4","انثى","روضة","2023-03-19","تمام عبد الباقي ","00000000000","510"),
("1093","759","9","عباس سعد طالب ","3","ذكر","حضانة","2023-03-13","سعد طالب ","00000000000","بلوك5"),
("1094","760","11","ريان قيصر","5","ذكر","روضة","2023-03-23","قيصر","00000000000","A7"),
("1095","761","6","علي محمد حكمت","5","ذكر","تمهيدي","2023-03-26","محمد حكمت","00000000000","b1"),
("1096","762","11","جود علي","4","ذكر","روضة","2023-03-26","علي","00000000000","A7"),
("1097","763","11","ريتاج علي","1","انثى","حضانة","2023-03-26","علي","00000000000","A7"),
("1098","764","6","مؤمل عبد الهادي","4","ذكر","روضة","2023-03-26","0000000000","00000000000","A2"),
("1099","765","11","علي عمار","4","ذكر","روضة","2023-03-24","عمار","00000000000","A7"),
("1100","766","7","علي  الدر كاظم فالح","-2","ذكر","حضانة","2023-03-28","كاظم فالح","00000000000","B1"),
("1101","767","7","يعسوب الدين كاظم فالح","-1","ذكر","حضانة","2023-03-28","كاظم فالح","00000000000","B1"),
("1102","768","12","زمرد عمر","5","انثى","تمهيدي","2023-03-15","عمر","00000000000","A8"),
("1103","769","12","ايلينا عمر","3","انثى","تحضيري","2023-03-15","عمر","00000000000","A8"),
("1104","770","12","محمد عمر","3","ذكر","تحضيري","2023-03-15","عمر","00000000000","A8"),
("1105","771","12","ماسه جواد","4","انثى","روضة","2023-03-21","جواد","00000000000","A8"),
("1106","772","12","شهم علي حمزه","3","ذكر","تحضيري","2023-03-25","علي","00000000000","A8"),
("1107","773","12","رهف  مثنى","2","انثى","حضانة","2023-03-28","مثنى","00000000000","A8"),
("1108","774","10","تيم رائدابراهيم","4","ذكر","روضة","2023-03-19","رائد ابراهيم","07702938326","A6"),
("1109","775","10","الين احمد رعد","4","انثى","روضة","2023-03-14","احمد رعد","07724997033","A1"),
("1110","776","6","حسين انمار","5","ذكر","تمهيدي","2023-03-29","0000000000","00000000000","0000"),
("1111","777","6","مريم انمار","3","انثى","حضانة","2023-03-29","0000000000","00000000000","0000"),
("1112","778","6","علي حيدر محمد","3","ذكر","حضانة","2023-03-28","0000000000","حيدر محمد","0000"),
("1113","779","7","  محمد عبد المحسن الكشوش","-2","ذكر","حضانة","2023-03-30","عبد المحسن الكشوش","07737511195","A3"),
("1114","780","11","حسين علي ","4","ذكر","روضة","2023-04-02","علي","00000000000","A7"),
("1115","781","11","مريم علي","2","انثى","حضانة","2023-04-02","علي","00000000000","A7"),
("1116","782","6","ليا محمد عبد المنعم","4","انثى","روضة","2023-04-02","محمد عبد المنعم","00000000000","A2"),
("1117","783","7","ماسه محمد علي ","-5","انثى","روضة","2023-04-02","محمد علي ","00000000000","B2"),
("1118","784","7","محمد جعفر عاشور ","-5","ذكر","تمهيدي","2023-04-02","جعفر عاشور ابريسم","07731636433","A3/311"),
("1119","785","7","علي جعفر عاشور ","-2","ذكر","حضانة","2023-04-02","جعفر عاشور ابريسم","07731636433","A3/"),
("1120","786","6","علي حيدر حسين","2","ذكر","حضانة","2023-04-02","حيدر حسين","00000000000","0000"),
("1121","787","6","يوسف حيدر حسين","2","ذكر","حضانة","2023-04-02","حيدر حسين","00000000000","A2"),
("1122","788","9","انس مرتجى ","4","ذكر","روضة","2023-04-02","مرتجى ","00000000000","بلوك b3"),
("1123","789","10","مريم مهند عبد الامير","5","انثى","تمهيدي","2023-04-04","مهند عبد الامير","07730719213","B1"),
("1124","790","10","كرار حيدر سعيد","1","ذكر","حضانة","2023-04-03","حيدر سعد","07711679938","A6"),
("1125","791","10","علي احمد صلاح","4","ذكر","روضة","2023-04-02","احمد صلاح","07710078426","A1"),
("1126","792","10","ايلاف سلمان داود","5","انثى","تمهيدي","2023-04-03","سلمان داود","07710798366","A1"),
("1127","793","10","رهف غزوان واصف","4","انثى","روضة","2023-04-09","غزوان واصف","07719433868","B1"),
("1128","794","6","علي احمد","3","ذكر","حضانة","2023-04-05","0000000000","00000000000","0000"),
("1129","795","6","سيلين محمد","2","انثى","حضانة","2023-04-09","0000000000","00000000000","0000"),
("1130","796","12","دانه وسام محمد","5","انثى","تمهيدي","2023-04-05","وسام","00000000000","A8"),
("1131","797","12","نادين حيدر","4","انثى","روضة","2023-03-26","حيدر","00000000000","A8"),
("1132","798","12","وضاح حيدر","2","ذكر","تحضيري","2023-03-26","حيدر","00000000000","A8"),
("1133","799","12","صفاء احمد ابراهيم","5","انثى","تمهيدي","2023-04-09","احمد","00000000000","A8"),
("1134","800","12","ياس ابراهيم","2","ذكر","حضانة","2023-03-28","ابراهيم","00000000000","A8"),
("1135","801","12","نايا وائل","3","انثى","تحضيري","2023-03-28","وائل","00000000000","A8"),
("1136","802","12","لتين احمد حسن","1","انثى","حضانة","2023-03-12","احمد","00000000000","A8"),
("1137","803","12","كرار علي","4","ذكر","روضة","2023-03-19","علي","00000000000","A8"),
("1138","804","12","ميرال علي","2","انثى","حضانة","2023-03-19","علي","00000000000","A8"),
("1139","805","12","حرير انس عمران","1","انثى","حضانة","2023-03-20","انس","00000000000","A8"),
("1140","806","12","حسن عصام","3","ذكر","تحضيري","2023-03-19","عصام","00000000000","A8"),
("1141","807","10","فاطمة عمار مصطفى","4","انثى","روضة","2023-04-09","عمار مصطفى","07719369216","A1"),
("1142","808","8","موسى احمد هلال","5","ذكر","تمهيدي","2023-04-05","احمد هلال","00000000000","B2"),
("1143","809","8","اروى نضير","5","انثى","تمهيدي","2023-04-01","نضير","00000000000","B3"),
("1144","810","8","حسن نضير","3","ذكر","تحضيري","2023-04-01","نضير","00000000000","B3"),
("1145","811","8","كرم علي نعيم","2","ذكر","حضانة","2023-04-02","علي","00000000000","A4"),
("1146","812","8","محمد احمد","3","ذكر","تحضيري","2023-04-02","احمد","00000000000","a4"),
("1147","813","8","محمد احمد","3","ذكر","تحضيري","2023-04-02","احمد","00000000000","a4"),
("1148","814","8","محمد الجواد ماهر","5","ذكر","تمهيدي","2023-03-30","ماهر","00000000000","a4"),
("1149","815","8","علي ماهر","3","ذكر","تحضيري","2023-03-30","ماهر","00000000000","a4"),
("1150","816","8","زينه سامر","3","انثى","تحضيري","2023-04-02","سامر","00000000000","a4"),
("1151","817","8","زينه علي","5","انثى","تمهيدي","2023-03-23","علي","00000000000","a4"),
("1152","818","8","علي منتصر","3","ذكر","تحضيري","2023-03-23","منتصر","00000000000","B3"),
("1153","819","8","ادم مروان","4","ذكر","روضة","2023-03-23","مروان","00000000000","A9"),
("1154","820","8","رند ياسر","4","انثى","روضة","2023-03-26","ياسر","00000000000","A8"),
("1155","821","8","رانيا ياسر","4","انثى","روضة","2023-03-26","ياسر","00000000000","A8"),
("1156","822","8","اسد الله حيدر","5","ذكر","تمهيدي","2023-04-30","حيدر","00000000000","a4"),
("1157","823","8","سمى مهند","5","انثى","تمهيدي","2023-04-01","مهند","00000000000","a4");




CREATE TABLE `users_tb` (
  `id_user` int(100) NOT NULL AUTO_INCREMENT,
  `user_name` varchar(250) NOT NULL,
  `user_pass` varchar(250) NOT NULL,
  `role` varchar(100) NOT NULL,
  PRIMARY KEY (`id_user`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4;


INSERT INTO users_tb VALUES
("1","admin","20172017","Admin"),
("6","A2","1234","User"),
("7","A3","1234","User"),
("8","A4","1234","User"),
("9","A5","1234","User"),
("10","A6","1234","User"),
("11","A7","1234","User"),
("12","A8","1234","User");


