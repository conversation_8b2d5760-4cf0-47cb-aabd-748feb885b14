<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حضور الطلاب </title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.dataTables.min.js"></script>
    <link rel="stylesheet" href="css/jquery.dataTables.min.css">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
   </head>
   <body>
   <form action="" method="POST">
  <div class="search_ac">
  <select name="catgs" id="selc" placeholder='اختر مستخدم'>
        <option value="0" selected disabled >اختر صنف تدريس </option>
        <option value="روضة" >روضة</option>
        <option value="حضانة" >حضانة</option>
        <option value="تمهيدي" >تمهيدي</option>
        <option value="تحضيري" >تحضيري</option>
    </select>
  <select name="acout_type" id="selc2" placeholder='اختر مستخدم'>
      <option value="0" selected disabled>اختر مستخدم </option>
     <?php
            $sql="SELECT * FROM users_tb ";
            $result=mysqli_query($con,$sql);
            if($result){
                while($row=mysqli_fetch_assoc($result)){
                    $user_name=$row['user_name'];
                    $id=$row['id_user'];
                echo '<option value='.$id.'>'.$user_name.'</option>';
                }
            }
            ?>
     
     </select>
   
    <input name="dateChose" type="date" required>
    <label for=""> اختر تاريخ</label>
    <button class="btn btn-warning" name='myInput'  > اظهار</button> 
  </div>
  <div class="wrapper2" id="tost_info">
    <div id="toast2">
      <div class="container-11">
        <i id="icon" class="fa-solid fa-money-bills "></i>
      </div>
      <div class="container-22">
        <p class="p1">CASH INFO </p>
        <p class="p2">تم اضافة البينانات</p>
      </div>
    </div>
  </div>
  <table class="table">
                            <thead>
                            <tr>
                            <td> اسم المستخدم</td>
                            <td>تاريخ الحضور</td>
                            <td>صنف الدراسة</td>
                            <td>اسم ولي الامر</td>
                            <td>اسم الطالب</td>
                            </tr>
                            </thead>
                            <tbody>
   </body>
   <script>
    let x;
    let toast = document.getElementById("toast2");
         p1 = document.querySelector(".p1");
         p2 = document.querySelector(".p2");
    function Warning(ts,ic,tx1,ss,icC,time) {
      clearTimeout(x);
    icon.className=icC;
    toast.style.borderRight=ts;
    icon.style.color=ic;
    p1.innerText=tx1;
    p2.style.fontSize="16px";
    p2.innerText=ss;
    toast.style.transition='1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition='1s';
      x = setTimeout(() => {
        toast.style.transform = "translateX(-500px)"
      }, time);
    }
    
  </script>
   <?php
    if(isset($_POST['myInput'])){
        if(!isset($_POST['catgs']) || !isset($_POST['acout_type'])){
        $msg1=" ! انتبه ";
        $msg2="يرجى  اختيار جميع  الحقول  ";
        $iconC="fa-solid fa-circle-info";
        $time=5000;
        echo "<script> Warning('8px solid rgb(247, 167, 22)','#f7a716','$msg1','$msg2','$iconC','$time')</script>";
        }else{
            $user=$_POST['acout_type'];
            $catg=$_POST['catgs'];
            $date=$_POST['dateChose'];
            $sql= "SELECT * FROM users_tb,stud_tb,stat WHERE users_tb.id_user=stud_tb.userID AND stud_tb.id=stat.id_stud AND users_tb.id_user='$user' AND stud_tb.catg='$catg' AND stat.data_stat='$date'";
            $resel=mysqli_query($con,$sql);
            if(mysqli_num_rows($resel)>0){
                $count=mysqli_num_rows($resel);
                $msg1=" حظور ";
                $msg2=" $count : مجموع  حظور الطلاب   ";
                $iconC="fa-solid fa-circle-info";
                $time=50000;
                echo "<script> Warning('8px solid rgb(34, 196, 242)','rgb(34, 196, 242)','$msg1','$msg2','$iconC','$time')</script>";


                while($row=mysqli_fetch_assoc($resel)){
                    ?>
                     <tr>
                        <td><?php echo $row['user_name']    ?></td>
                        <td><?php echo $row['data_stat']    ?></td>
                        <td><?php echo $row['catg']    ?></td>
                        <td><?php echo $row['p_name']    ?></td>
                        <td><?php echo $row['name']    ?></td>
                     </tr>


                    <?php 
                }
                ?>
                </tbody>
                </table>
                <?php
            }else{
                $msg1="! لايوجد ";
                $msg2="لاتوجد بيانات لهذا التاريخ ";
                $iconC="fa-solid fa-circle-info";
                $time=5000;
                echo "<script> Warning('8px solid rgb(243, 35, 82)','rgb(243, 35, 82)','$msg1','$msg2','$iconC','$time')</script>";

            }
        }
    }









  ?>
</html>