<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Mod"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>  الطلاب الفعالين A5</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.dataTables.min.js"></script>
    <link rel="stylesheet" href="css/jquery.dataTables.min.css">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
   </head>
    <body>
    <table class="table" id="Table">
  <thead>
    <tr>
    <th scope="col"> مستخدم الحضانة  </th>
    <th scope="col"> الايام المتبقية </th>
      <th scope="col">حالة الاشتراك</th>
      <th scope="col">قيمة الاشتراك</th>
      <th scope="col">تاريخ النفاذ </th>
      <th scope="col">تاريخ الاشتراك</th>
      <th scope="col">رقم ولي الامر</th>
      <th scope="col">اسم ولي الامر</th>
      <th scope="col">صنف التسجيل</th>
      <th scope="col">السكن</th>
      <th scope="col">الجنس</th>
      <th scope="col"> العمر</th>
      <th scope="col">اسم الطالب  </th>
      <th scope="col">رقم  الوصل </th>

      
    </tr>
  </thead>
  <tbody id="myTable">
    <?php
      $datenow=date('Y-m-d');
      $Date2=date('Y-m-d', strtotime($datenow. ' + 1 days'));
      $Date5=date('Y-m-d', strtotime($datenow. ' + 365 days'));
      $sqlA2="SELECT * FROM stud_tb,stud_pay,users_tb WHERE DATE(stud_pay.date_exp) BETWEEN date('$Date2')AND Date('$Date5') AND stud_tb.id=stud_pay.id_stud AND users_tb.id_user=stud_tb.userID AND users_tb.user_name='A5' ";
      $result=mysqli_query($con,$sqlA2);
      if($result){
       while($row=mysqli_fetch_assoc($result)) {
          $id_pay=$row['id_pay'];
          $id_note=$row['id_note'];
          $name=$row['name'];
          $age=$row['age'];
          $sex=$row['sex'];
          $catg=$row['catg'];
          $datein=$row['datein'];
          $p_name=$row['p_name'];
          $p_phone=$row['p_phone'];
          $loc=$row['loc'];
          $date_exp=$row['date_exp'];
          $cash_stud=number_format($row['cash_stud']);
          $user_name=$row['user_name'];
          $id_pay=$row['id_pay'];
          $date_in=strtotime(date('y-m-d'));
          $date_out=strtotime($date_exp);
          $stat=$date_out-$date_in;
          $cek=floor($stat/(60*60*24));
          if($cek<=0){
            $mes='<h3 class=exp>منتهي</h3>';
        }elseif($cek<=10 & $cek>0){
            $mes='<h3 class=soon >قريبا</h3>';
         }else{
            $mes='<h3 class=still >فعال</h3>';
        }

          echo '<tr>
          <td> '.$user_name.' </td>
          <td> '.$cek.' </td>
          <td> '.$mes.' </td>
          <td> IQD '.$cash_stud.'  </td>
          <td>'.$date_exp.'  </td>
          <td>'.$datein.'</td>
          <td>'.$p_phone.'</td>
          <td>'.$p_name.'</td>
          <td>'.$catg.'</td>
          <td>'.$loc.'  </td>
          <td>'.$sex.'</td>
          <td>'.$age.'</td>
          <td>'.$name.'</td>
          <td>'.$id_pay.'</td>

          
          </tr>
        ';
         }
      }
  

    ?>
   
  </tbody>
</table>
   </body>
<script>
  $(document).ready(function () {
    $("#Table").DataTable();
  });
</script>
</html>