<?php
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Mod"){
        

 }else{
        header("location:http:../login.php",true);
        die("");
       
    }
    }else{
        header("location:../login.php",true);
        die("");
      }
      if(isset($_GET['exitbtn'])){
        header("location:../logout.php",true);
        exit();
      }
    

?>

<nav>
   <style>
      #capss{
         
         color: aliceblue;
         font-size: 30px;
         cursor: pointer;
      }
      #capss:hover{
         color: gray;
      }
   </style>
<div class='logo2'><img src="../Admin/css/logooo.png" alt=""></div>
         <div class="logo">
         
         <form action=""><button class='btn btn-danger mb-1' name="exitbtn" type='submit' id='exit_btn'>تسجيل الخروج </button>
         <label for="" ><?php echo $_SESSION['user']->user_name; ?> مرحبا بك</label></form>
         
         </div>
         <input type="checkbox" id="click">
         <label for="click" class="menu-btn" id="caps">
         <i id="capss" class="fa-solid fa-bars" ></i>
         </label>
         
         <ul>
            <li><a href="../Moda/About_us.php"><i class="fa-solid fa-exclamation"></i> ماذا عنا</a></li>
            <li><a href="../Moda/ATTANC.php"><i class="fa-solid fa-users"></i> حضور الطلاب </a></li>
            <li><a href="../Moda/employee_AttANC.php"><i class="fa-solid fa-users"></i> حضور الموظفين </a></li>
            <li><a class="active" href="../Moda/home.php">الرئيسية</a></li>
            
            
         </ul>
      </nav>
