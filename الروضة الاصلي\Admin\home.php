<?php
session_start();
try {
    if (isset($_SESSION['user'])) {
        if ($_SESSION['user']->role === "Admin") {
            // Admin logic here
        } else {
            header("location:../login.php", true);
            die("");
            echo "don't work";
        }
    } else {
        header("location:../login.php", true);
        die("");
    }
} catch (Exception $e) {
    echo "Error in session handling: " . $e->getMessage();
}

try {
    $user = 'kidzrcle_rwda';
    $pass = 'kidzrcle_rwda';
    $database = new PDO("mysql:host=localhost;dbname=kidzrcle_rwda;charset=utf8;", $user, $pass);
    $getUser = $database->prepare("SELECT * FROM stud_tb");
    $getUser->execute();
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage();
}

try {
    $con = new mysqli("localhost", $user, $pass, 'kidzrcle_rwda');
  $con->set_charset("utf8");
    if ($con->connect_error) {
        throw new Exception("Connection failed: " . $con->connect_error);
    }

    $datenow = date('Y-m-d');
    $Date = date('Y-m-d', strtotime($datenow . ' + 10 days'));
    $Date2 = date('Y-m-d', strtotime($datenow . ' + 1 days'));
    $Date3 = date('Y-m-d', strtotime($datenow . ' + 30 days'));
    $Date4 = date('Y-m-d', strtotime($datenow . ' + 5 days'));
    $Date5 = date('Y-m-d', strtotime($datenow . ' + 365 days'));

    // Queries
    $sql = "SELECT * FROM stud_tb, stud_pay, users_tb WHERE DATE(stud_pay.date_exp) BETWEEN date('$Date2') AND Date('$Date5') AND stud_tb.id = stud_pay.id_stud AND users_tb.id_user = stud_tb.userID";
    $res = mysqli_query($con, $sql);

    $sql = "SELECT * FROM stud_pay WHERE DATE(date_exp) <= '$datenow'";
    $res2 = mysqli_query($con, $sql);

    $sql = "SELECT * FROM stud_pay WHERE DATE(date_exp) BETWEEN '$Date2' AND '$Date'";
    $res3 = mysqli_query($con, $sql);

    $sql = "SELECT * FROM stud_tb WHERE catg='روضة'";
    $res4 = mysqli_query($con, $sql);

    $sql = "SELECT * FROM stud_tb WHERE catg='حضانة'";
    $res5 = mysqli_query($con, $sql);

    $sql = "SELECT * FROM depit_tb";
    $res6 = mysqli_query($con, $sql);

    $sql = "SELECT * FROM stud_tb WHERE catg='تمهيدي'";
    $res7 = mysqli_query($con, $sql);

    $sql = "SELECT * FROM stud_tb WHERE catg='تحضيري'";
    $res8 = mysqli_query($con, $sql);
} catch (Exception $e) {
    echo "Error in query execution: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الرئيسية</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <script src="js/all.js"></script>
    <link rel="icon" href="css/icon.ico">
   
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php include "addon/topbar.php"; ?>
</head>
<body>
    <div class="rect" id="clock">
        <div class="cir">
            <div class="inner_cir">
                <div class="brand">KIDS ACADMEY</div>
                <div class="center"></div>
                <div id="hr"></div>
                <div id="min"></div>
                <div id="sec"></div>
            </div>
        </div>
    </div>

    <div class="grid">
        <div class="col"> 
            <a href="expried_soon.php">
                <h3>الطلاب على وشك الانتهاء</h3>
                <?php echo mysqli_num_rows($res3); ?>
            </a>
        </div>
        <div class="col"> 
            <a href="new_stud.php">
                <h3>الطلاب الفعالين </h3>
                <?php echo mysqli_num_rows($res); ?>
            </a>
        </div>
        <div class="col"> 
            <a href="allstud.php">
                <h3> عدد الطلاب الكلي </h3>
                <?php echo $getUser->rowCount(); ?>
            </a>
        </div>
        <div class="col"> 
            <a href="tadeery.php">
                <h3> عدد طلاب صنف التحضيري</h3>
                <?php echo mysqli_num_rows($res8); ?>
            </a>
        </div>
        <div class="col"> 
            <a href="tamhediStud.php">
                <h3> عدد طلاب صنف التمهيدي</h3>
                <?php echo mysqli_num_rows($res7); ?>
            </a>
        </div>
        <div class="col"> 
            <a href="expired_stud.php">
                <h3>الطلاب منتهي الاشتراك</h3>
                <?php echo mysqli_num_rows($res2); ?>
            </a>
        </div>
        <div class="col"> 
            <a href="allDepit.php">
                <h3>عدد المصروفات</h3>
                <?php echo mysqli_num_rows($res6); ?>
            </a>
        </div>
        <div class="col"> 
            <a href="hadanaStud.php">
                <h3> عدد طلاب صنف الحضانة</h3>
                <?php echo mysqli_num_rows($res5); ?>
            </a>
        </div>
        <div class="col">
            <a href="rodaStud.php">
                <h3> عدد طلاب صنف الروضة</h3>
                <?php echo mysqli_num_rows($res4); ?>
            </a>
        </div>

        <div class="col">
            <a href="manage_needs.php">
                <h3>الاحتياجات</h3>
                <i class="fas fa-clipboard-list" style="font-size: 2em; color: #667eea;"></i>
            </a>
        </div>

        <div class="col">
            <a href="manage_leaves.php">
                <h3>طلبات الإجازة</h3>
                <i class="fas fa-calendar-check" style="font-size: 2em; color: #28a745;"></i>
            </a>
        </div>
    </div>
    <footer class="footer_p">
        <p>جميع الحقوق محفوظة لمؤسسة كيدز اكادمي </p>
    </footer>
    <script>
        var sec = 0;
        var hor = 0;
        var min = 0;
        var d = new Date();

        setInterval(function() {
            d = new Date();
            sec = d.getSeconds() * 6;
            min = d.getMinutes() * 6;
            hor = d.getHours() * 30 + Math.round(min / 12);
            document.getElementById("sec").style.transform = "rotate(" + sec + "deg)";
            document.getElementById("min").style.transform = "rotate(" + min + "deg)";
            document.getElementById("hr").style.transform = "rotate(" + hor + "deg)";
        }, 1000);

        function isChecked() {
            if (document.getElementById("click").checked) {
                document.getElementById("clock").style.display = "flex";
            } else {
                document.getElementById("clock").style.display = "none";
            }
        }
    </script>
</body>
</html>
